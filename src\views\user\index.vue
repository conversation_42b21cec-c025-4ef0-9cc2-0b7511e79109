<template>
  <!-- eslint-disable -->
  <div class="user-container" v-loading="loading" element-loading-text="加载中，请稍等...">
    <tip v-if="!isRealName" type="default" class="mb-20">
      尊敬的 {{ personName }}，您好！您还未进行实名认证，系统部分功能使用受限，请扫描下方
      <span class="link">邕e登APP</span> 二维码安装App进行实名认证。
    </tip>
    <tip v-else type="default">
      <p class="mt-0">尊敬的 {{ personName }}，您好！欢迎您使用{{ systemTitle }}，我们将在这里为您提供便捷的多测合一办理服务。</p>
      <p class="mb-0">
        您可以
        <a class="link" @click="openOperationManual()">点击此处</a>下载用户使用手册。
      </p>
      <!-- 建设单位 -->
      <template v-if="roles.indexOf('c-d') >= 0">
        <p class="mb-0">
          您当前的单位是：
          <span class="company-name">{{ companyName }}</span>
          ，您在该单位的角色为：{{ companyRole }}。
          <a class="link" @click="setSwitchCompanyDialogVisible(true)">点击此处</a>可切换您所在的其他单位。
        </p>
      </template>
    </tip>
    <el-row
      :gutter="60"
      class="list"
      :class="{'column-three': filterList.length % 3 === 0, 'column-four': filterList.length % 4 === 0 }"
    >
      <el-col
        v-for="(item, index) in filterList"
        :key="index"
        :xs="24"
        :sm="12"
        :md="12"
        :lg="filterList.length % 4 === 0 ? 6: 8"
        :xl="filterList.length % 4 === 0 ? 6: 8"
      >
        <div class="list-item flex" @click="toPage(item)">
          <div class="list-item__icon flex">
            <i :class="`iconfont icon-${item.icon}`"></i>
          </div>
          <h4 class="list-item__title">{{ item.title }}</h4>
          <p class="list-item__desc">{{ item.desc }}</p>
        </div>
      </el-col>
    </el-row>
    <!-- 切换单位 -->
    <switch-company :visible.sync="switchCompanyDialog.visible" />
  </div>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";
import UserApi from "api/user/index.js";
// mixins
import SwitchCompanyMixin from "mixins/switch-company.js";

const urlPrefix = process.env.VUE_APP_URL_PREFIX;
const baseUrl = process.env.VUE_APP_BASE_URL;

export default {
  name: "UserCenter",
  mixins: [SwitchCompanyMixin],
  computed: {
    ...mapGetters([
      "personName",
      "isRealName",
      "companyStateCode",
      "companyID",
      "companyName",
      "companyRole",
      "roles",
      "currentEnv"
    ])
  },
  watch: {
    roles(val) {
      this.filterList = this.setFilterList();
    }
  },
  data() {
    return {
      loading: false,
      filterList: [],
      list: [
        {
          icon: "user1",
          title: "用户信息",
          roles: ["3"],
          visible: true,
          desc: "在这里，可以查看您的个人信息或进行密码修改等操作。",
          outLink: false,
          url: "/user/info"
        },
        {
          icon: "yewuguanli",
          title: "办理业务",
          visible: true,
          desc: "在这里，可以在线办理“多测合一”的相关业务。",
          outLink: false,
          url: "/user/business"
        },
        {
          icon: "yewu1",
          title: "我的业务",
          visible: true,
          desc: "在这里，可以查看您所申请办理的业务，包括正在办理和已办结的。",
          outLink: false,
          url: "/user/my-business"
        },
        {
          icon: "danwei",
          title: "测绘单位注册",
          roles: ["1"],
          visible: true,
          desc: "在这里，可以进行测绘单位注册或查看单位注册信息。",
          outLink: false,
          url: "/user/company-register"
        },
        {
          icon: "danwei",
          title: "单位信息",
          roles: ["2"],
          visible: true,
          desc: "在这里，可以对您的单位信息进行维护变更。",
          outLink: false,
          url: "/user/company-info"
        },
        {
          icon: "zhuanyi-10",
          title: "业务移交",
          roles: ["2-a"],
          visible: true,
          desc: "在这里，可以对业务进行移交。",
          outLink: false,
          url: "/user/transfer-business"
        }

        // {
        //   icon: "house",
        //   title: "楼盘信息",
        //   roles: ['c-d-2'],
        //   visible: true,
        //   desc: "在这里，可以对楼盘信息进行管理。",
        //   outLink: false,
        //   url: "/user/building-info/index"
        // },
      ],
      timer: null,
      systemTitle: process.env.VUE_APP_SYSYTEM_TITLE
    };
  },
  created() {
    this.init();
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    init() {
      this.filterList = this.setFilterList();
      this.getCompanyAudit();
    },
    setFilterList() {
      if (!this.roles || !this.roles.length) {
        return [this.list[0]];
      }
      return this.list.filter(e => {
        if (!e.roles) return true;

        const hasPermission = this.roles.some(role => e.roles.includes(role));
        if (hasPermission) {
          // 已提交注册
          if (e.title === "单位注册" && this.companyStateCode > 0) {
            e.url = "/user/company-register/info";
          }
          if (e.title === "单位信息") {
            e.url = "/user/company-info";
          }
          return e;
        }
      });
    },
    toPage(e) {
      if (e.outLink) {
        const returnUrl = window.location.href;
        window.location.href = `${baseUrl}${e.url}?returnUrl=${returnUrl}`;
      } else {
        if (e.url) {
          this.$router.push({ path: e.url });
        }
      }
    },
    // 获取单位审核状态，感觉状态修改权限和菜单
    getCompanyAudit() {
      if (this.timer) {
        this.clearTimer();
      }

      const { companyStateCode, roles } = this;
      if (companyStateCode === 3 || companyStateCode === 5) return;
      else if (companyStateCode > 0) {
        this.timer = setInterval(() => {
          UserApi.GetCompanyInfoByUserInfo()
            .then(async res => {
              const { Data, StateCode } = res;

              if (!Data) {
                this.clearTimer();
              }
              if (Data.State === 3) {
                this.clearTimer();

                let newRoles = [...roles];
                newRoles.splice(
                  newRoles.findIndex(e => e === "1"),
                  1
                );

                // 已完成注册
                let role =
                  Data.CType === "测绘单位" ? ["c-m", "2"] : ["c-d", "2"];
                newRoles = newRoles.concat(role);

                this.loading = true;
                this.$message.success("您申请注册的单位已审核通过");
                // 修改路由权限
                await this.$store.dispatch("permission/generateRoutes", {
                  roles: newRoles
                });
                // 修改角色
                await this.$store.dispatch("user/setRoles", newRoles);
                this.loading = false;
              }
            })
            .catch(err => console.log(err));
        }, 30000);
      }
    },
    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 打开操作指南
    openOperationManual() {
      window.open(
        `${urlPrefix}/static/pdf/南宁市多测合一信息化管理用户手册.pdf`,
        "_blank"
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.user-container {
  padding: 20px 0 10px 0;

  .list {
    &.column-four {
      /deep/ .el-col {
        border-right: #e6e6e6 1px solid;
        height: 180px;
        margin: 40px 0;

        &:nth-child(4n) {
          border-right: none;
        }
      }
    }

    &.column-three {
      /deep/ .el-col {
        border-right: #e6e6e6 1px solid;
        height: 180px;
        margin: 40px 0;

        &:nth-child(3n) {
          border-right: none;
        }
      }
    }
  }

  /deep/ .el-col {
    border-right: #e6e6e6 1px solid;
    height: 180px;
    margin: 30px 0;

    &:nth-child(3n) {
      border-right: none;
    }

    &:last-child {
      border-right: none;
    }
  }

  .list {
    &-item {
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      &:hover {
        .list-item__title {
          color: $color-primary;
        }
      }

      &__icon {
        width: 70px;
        height: 70px;
        border: 1px solid $color-primary;
        color: $color-primary;
        align-items: center;
        justify-content: center;
        border-radius: 100px;

        .iconfont {
          font-size: 40px;
        }
      }

      &__title {
        text-align: center;
        margin-top: 20px;
        font-size: 22px;
        font-weight: normal;
        margin-bottom: 0;
      }

      &__desc {
        text-indent: 2em;
        color: #7e8998;
      }
    }
  }
}

.link {
  color: $color-primary;
  text-decoration: underline;
  cursor: pointer;
  &:hover {
    color: #09f;
  }
}

.company-name {
  color: $color-primary;
  font-weight: bold;
}
</style>
