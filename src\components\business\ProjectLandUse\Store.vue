<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="`${row ? '编辑' : '添加'}${title}`"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    width="900px"
    @close="close"
    class="project-land-use-store-container"
  >
    <el-form
      v-if="form"
      ref="form"
      :rules="rules"
      class="landuse-form"
      :model="form"
      label-width="0"
    >
      <table class="table" cellpadding="0" cellspacing="0">
        <tr>
          <th colspan="3">
            <span class="required">用途名称</span>
          </th>
          <td colspan="7">
            <el-form-item prop="landUse" class="mb-15">
              <el-cascader
                v-if="landUseOps.length"
                v-model="form.landUseId"
                placeholder="请选择用途名称"
                :show-all-levels="false" 
                :options="landUseOps"
                :props="{ checkStrictly: false }"
                clearable
              ></el-cascader>
            </el-form-item>
          </td>
          <th colspan="2">
            <span class="required">占用比例（%）</span>
          </th>
          <td colspan="4">
            <el-form-item prop="percent" class="mb-15">
              <el-input
                v-model="form.percent"
                placeholder="请输入整数且范围从1至100"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
       
      </table>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit('form')"
        >确认{{ row ? "编辑" : "添加" }}</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/business/index.js";
export default {
  name: "ProjectLandUseStoreDialog",

  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前项
    row: {
      type: Object,
      default: "",
    },
    // 索引
    index: {
      type: Number,
      default: -1,
    },
    // 记录列表
    list: {
      type: Array,
      default: () => [],
    },
    // 标题
    title: {
      type: String,
      default: "土地用途",
    },
  },
  data() {
    return {
      
      defaultLandUseForm: {
        id: "",
        landUseId: "",
        landUse: "",        
        code: "",
        percent: ""
      }, //提交到后台的存储格式[{landUse: "", percent: "", code:""}]

      form: "",

      //土地用途下列列表数据
      landUseOps: [],

      rules: {
        landUseId: [
          {
            required: true,
            message: "请选择用途",
            trigger: "blur",
          },
        ],
        percent: [
          {
            required: true,
            message: "请输入整数且范围从1至100",
            trigger: "blur",
          },
        ],    
      },
      edit: false,
    };
  },
  watch: {
    //显示弹窗时触发
    visible(val) {
      if (val) {
        this.init("土地用途");
      }
    },
  },
  methods: {
    // 初始化
    init(name) {
      const { row, defaultLandUseForm } = this;
      this.form = JSON.parse(JSON.stringify(row ? row : defaultLandUseForm));
      console.log(this);
      Api.GetLandUseList()
        .then((res) => {
          const { StateCode, Data, Message } = res;
          if (StateCode===1) {
           this.landUseOps=res.Data;
          } else {
            this.landUseOps = [];
            this.$message.error(Message);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置表单
    reset() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // 关闭存储弹窗
    close() {
      const { form, edit, index } = this;

      this.reset();
      this.edit = false;
      this.$emit("cancel");
      this.$emit("update:visible", false);
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { form, index, row, list, findItem } = this;
          const {
            landUseId,
            landUse,            
            code,
            percent,
          } = form;

          let error = false;

          if (!landUseId) {
            this.$message.warning("请选择用途");
            return false;
          }
          if (!percent) {
            this.$message.warning("请输入占用比例");
            return false;
          } else {
            if (isNaN(parseInt(percent))){
              this.$message.warning("占用比例请输入整数");
              return false;
            }
            if (percent.indexOf(".") >= 0) {
              this.$message.warning("占用比例请输入整数");
              return false;
            }
            if(percent>100){
                this.$message.warning("占用比例不能大于100");
                return false;
            }
            if(percent<=0){
                this.$message.warning("占用比例必须大于0");
                return false;
            }

            if (list) {
              var total = 0;
              $.each(list, function(index, item) {
                if (!row || (row && item.id != row.id)) {
                  total += parseInt(item.percent);
                }
              });

              if (total + parseInt(percent) > 100) {
                this.$message.warning("总占用比例不能大于100");
                return false;
              }
            }
          }

          var useId = form.landUseId[form.landUseId.length - 1];
          var landUseItem = findItem(this.landUseOps, useId);
          form.id = $.now();
          form.code = landUseItem.code;
          form.landUse = landUseItem.name;

          let params = { ...form };

          const isExist = list.find((e) => e.code === form.code);

          //如果是新增的判断
          if (isExist && !row) {
            this.$message.warning("该土地用途已存在，不可重复！");
            return false;
          }

          //如果是编辑的判断
          if (isExist && row && isExist.code == form.code && isExist.id != row.id) {
            this.$message.warning("该土地用途已存在，不可重复！");
            return false;
          }

          this.edit = true;
          this.$emit("submit", params, index);
          this.$emit("update:visible", false);
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },

    //递归获取土地用途节点数据
    findItem(list, id) {
      const { findItem } = this;

      var landUseItem = null;
      $.each(list, function(index, item){
        if (item.value == id){
          landUseItem = item;
          return false;
        }
        else {
          if (item.children != null) {
            landUseItem = findItem(item.children, id);
            if (landUseItem != null)
              return false;
          }
        }
      })
      return landUseItem;
    },
    
  },  
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-bottom: 10px;
  padding-top: 10px;
}

.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  th {
    background: #f8f8f8;
    color: #909399;
  }
  th,
  td {
    text-align: center;
    padding: 5px 0;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
  }

  td {
    .table-div:last-child {
      div {
        border-bottom: none;
      }
    }
  }

  .th-fixed-width {
    min-width: 65px;
    width: 65px;
    max-width: 65px;
  }

  &-add-btn {
    // border: 1px dashed #dfe6ec;
    width: 100%;
    // border-radius: 4px;
    text-align: center;
    margin-top: -1px;
    height: 45px;
    line-height: 45px;
    cursor: pointer;
    position: relative;
    z-index: 10;
    color: $color-primary;
    margin-top: -1px;

    // &:hover {
    //   border-color: $color-primary;
    // }

    & > i {
      margin-right: 5px;
    }
  }

  &-del-btn {
    min-width: 65px;
    padding: 5px;
    line-height: inherit;
    color: $color-primary;
    cursor: pointer;
  }
}

.table-div {
  width: 100%;
  display: table;
  line-height: 1;
  min-height: 35px;

  div {
    display: table-cell;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    padding: 5px 0;

    &:last-child {
      border-right: none;
    }
  }
}

.landuse-form {
  /deep/ .el-input__inner {
    border: none;
    padding: 5px 10px;
  }

  /deep/ .el-input {
    border: none;
    padding: 0;
  }

  /deep/ .el-textarea {
    border: none;
  }

  /deep/ .el-textarea__inner {
    border: none;
    padding: 5px;
    min-height: 40px !important;
    // height: auto !important;
  }

  /deep/ .el-form-item {
    margin-bottom: 0;
    padding: 0;
    border: none;
  }
  /deep/ .el-form-item__error {
    padding-left: 10px;
  }
}

.td-input {
  /deep/ .el-form-item__error {
    padding-left: 0;
  }
}

.required {
  &::before {
    content: "*";
    color: #ff4949;
    margin-right: 4px;
  }
}
</style>