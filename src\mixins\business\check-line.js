/*
 * 模块 : 验线模块相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-07-25
 * 版本 : version 1.0
 */
/* eslint-disable */
// 组件
import ProjectLicenceView from "components/business/ProjectLicence/View.vue";
import ProjectLicenceGetDialog from "components/business/ProjectLicence/Get.vue";
import ProjectLicenceStoreDialog from "components/business/ProjectLicence/Store.vue";
import DynamicTable from "components/common/Table/DynamicTable";
import ProjectLandUseStoreDialog from 'components/business/ProjectLandUse/Store.vue';

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  components: {
    ProjectLicenceView,
    ProjectLicenceGetDialog,
    ProjectLicenceStoreDialog,
    DynamicTable,
    ProjectLandUseStoreDialog,
  },
  data() {
    return {
      // 工程规划许可证获取弹窗配置
      licenceGetDialog: {
        visible: false,
        type: 0,
        title: "工程规划许可证"
      },
      // 工程规划许可证自行添加弹窗配置
      licenceStoreDialog: {
        visible: false,
        row: null,
        index: -1,
      },
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title",
      },
      licenceTableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50",
        },
        {
          title: "工程规划许可证号",
          key: "Code",
          align: "center",
        },
        {
          title: "建设单位",
          key: "ConstructCompany",
          align: "center",
        },
        {
          title: "项目名称",
          key: "ProjectName",
          align: "center",
        },
      ],
      // 工程规划许可证查看弹窗配置
      licenceViewDialog: {
        visible: false,
        row: null,
        index: -1,
      },


      //土地用途自行添加弹窗配置
      landuseStoreDialog: {
        visible: false,
        row: null,
        index: -1
      },

      //土地用途表格标题
      landuseTableHeader: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: '50'
        },
        {
          title: '用途名称',
          key: 'landUse',
          align: 'center'
        },
        {
          title: '占用比例（%）',
          key: 'percent',
          align: 'center'
        }
      ],

      // 定时器
      timer: null,
      // 楼盘信息表pdf
      propertyInfo: null,
      // 成果审核反馈
      auditFeedback: [],
      // 测绘成果上传地址
      resultUploadUrl: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostSurveyProjectResult`,
    };
  },
  methods: {
    // 处理要提交的业务数据
    handleBusinessData() {
      const { businessID, ContentInfo, form1 } = this;
      const { BusinessName, GroundCode, ProjectPlanPermission, ProjectLandUse,
        CompanyAddress, LegalPersonName, LegalPersonNumber, LegalPersonPhone, PlanConditionNumber,
        UnifiedProjectCode, ProjectType, PersonType, IsZJ } = form1;
      const { BuildingTableInfo, ProjectResultInfo } = ContentInfo;

      this.BaseInfo.BusinessName = BusinessName;

      const data = {
        BaseInfo: this.BaseInfo,
        ContentInfo: {
          ID: businessID,
          GroundCode,
          ProjectPlanPermission: JSON.stringify(ProjectPlanPermission),
          BuildingTableInfo: JSON.stringify(BuildingTableInfo),
          ProjectResultInfo: JSON.stringify(ProjectResultInfo),
          ProjectLandUse: JSON.stringify(ProjectLandUse),
          CompanyAddress: CompanyAddress,
          LegalPersonName: LegalPersonName,
          LegalPersonNumber: LegalPersonNumber,
          LegalPersonPhone: LegalPersonPhone,
          PlanConditionNumber: PlanConditionNumber,
          UnifiedProjectCode: UnifiedProjectCode,
          ProjectType: ProjectType,
          PersonType: PersonType,
          IsZJ: IsZJ
        },
      };

      return data;
    },
    // 查看工程规划许可证
    viewLicence(row, index) {
      this.licenceViewDialog = {
        visible: true,
        row,
        index,
      };
    },
    // 工程规划许可证获取成功
    licenceGetSuccess(params) {
      this.form1.ProjectPlanPermission.push(params);
      this.$refs.form1.clearValidate("ProjectPlanPermission");
    },
    // 工程规划许可证获取弹窗可见性
    setLicenceGetDialogVisible(val) {
      this.licenceGetDialog.visible = val;
      if(!val){
        this.licenceGetDialog.type = 0;
      }
    },
    // 删除工程规划许可证
    delLicence(row, index) {
      this.form1.ProjectPlanPermission.splice(index, 1);
    },
    // 显示工程规划许可证存储弹窗
    storeLicence(row, index) {
      this.licenceStoreDialog = {
        visible: true,
        row,
        index,
      };
    },
    // 工程规划许可证存储成功
    licenceStoreSuccess(data, index) {
      if (index >= 0) {
        this.$set(this.form1.ProjectPlanPermission, index, data);
      } else {
        this.form1.ProjectPlanPermission.push(data);
      }
    },
    // 工程规划许可证存储弹窗可见性
    setLicenceStoreDialogVisible(val) {
      this.licenceStoreDialog.visible = val;
    },

    // 删除土地用途
    delLandUse(row, index) {
      this.form1.ProjectLandUse.splice(index, 1)
    },
    // 显示土地用途存储弹窗(编辑土地用途时调用)
    storeLandUse(row, index) {
      this.landuseStoreDialog = {
        visible: true,
        row,
        index
      }
    },
    // 土地用途存储成功
    landuseStoreSuccess(data, index) {
      if (index >= 0) {
        this.$set(this.form1.ProjectLandUse, index, data);
      } else {
        this.form1.ProjectLandUse.push(data);
      }
    },
    // 土地用途存储弹窗可见性（添加土地用途时调用）
    setLandUseStoreDialogVisible(val) {
      this.landuseStoreDialog.visible = val;
      this.landuseStoreDialog.row = null;
      this.landuseStoreDialog.index = -1;
    }
  },
};
