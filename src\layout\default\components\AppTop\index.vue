<template>
  <!-- eslint-disable -->
  <div class="top-container fixed flex">
    <div class="wrapper-container flex">
      <div class="top-container-left">
        <span class="link flex" @click="showMsg">
          <i class="iconfont icon-message"></i>
          <span class="hidden-xs-only">意见反馈</span>
        </span>
      </div>
      <div class="top-container-right">
        <span
          class="top-container-right-item link hidden-xs-only"
          @click="$router.push({ name: 'About' })"
        >
          关于我们
          <i class="el-icon-arrow-right"></i>
        </span>
        <span
          class="top-container-right-item link hidden-xs-only"
          @click="$router.push({ name: 'Contact' })"
        >
          联系方式
          <i class="el-icon-arrow-right"></i>
        </span>
        <template v-if="personName">
          <el-popover placement="bottom" width="320" trigger="hover">
            <div class="user-info-container">
              <div class="user-info-title">
                <span>用户信息</span>
                <router-link to="/user/info" class="url-btn fr" v-if="showAccountSettings()">
                  账户设置
                  <i class="el-icon-arrow-right"></i>
                </router-link>
              </div>
              <ul class="user-info-list mt-10">
                <li class="user-info-list-item">
                  账号：{{ userName | isNull }}
                </li>
                <li class="user-info-list-item">
                  真实姓名：{{ personName | isNull }}
                  <el-tag
                    style="margin-left:5px;"
                    size="mini"
                    :type="isRealName ? 'success' : 'info'"
                    effect="dark"
                    >{{ isRealName ? "已实名" : "未实名" }}</el-tag
                  >
                </li>
                <li class="user-info-list-item">
                  {{ roles.indexOf("c-m") >= 0 ? "所在" : "当前" }}单位：{{
                    companyName | isNull
                  }}
                </li>
                <li class="user-info-list-item">
                  单位类型：
                  <template v-if="companyType">
                    <!-- 测绘单位 -->
                    <el-tag v-if="companyType === '测绘单位'">{{
                      companyType
                    }}</el-tag>
                    <!-- 建设单位 -->
                    <el-tag v-else class="tag-blue">{{ companyType }}</el-tag>
                  </template>
                  <span v-else>-</span>
                </li>
                <li class="user-info-list-item">
                  单位角色：
                  <el-tag v-if="companyRole === '单位管理员'">{{
                    companyRole
                  }}</el-tag>
                  <el-tag v-if="companyRole === '报建员'" type="success">{{
                    companyRole
                  }}</el-tag>
                  <el-tag v-if="companyRole === '测绘人员'" type="success">{{
                    companyRole
                  }}</el-tag>
                  <el-tag v-if="companyRole === '注册测绘师'" type="warning">{{
                    companyRole
                  }}</el-tag>
                  <span v-if="!companyRole">-</span>
                  <!-- 建设单位 -->
                  <el-button
                    v-if="roles.indexOf('c-d') >= 0"
                    type="text"
                    size="mini"
                    class="fr change-company"
                    @click="setSwitchCompanyDialogVisible(true)"
                  >
                    切换单位
                    <i class="el-icon-arrow-right"></i>
                  </el-button>
                </li>
              </ul>
            </div>
            <span slot="reference" class="top-container-right-item link">
              <i class="el-icon-s-custom"></i>
              {{ personName }}
            </span>
          </el-popover>
          <el-popconfirm title="确认退出系统?" @onConfirm="logout">
            <span slot="reference" class="top-container-right-item link"
              >注销</span
            >
          </el-popconfirm>
        </template>
      </div>
    </div>
    <!-- 切换单位 -->
    <switch-company :visible.sync="switchCompanyDialog.visible" />
  </div>
</template>

<script>
/* eslint-disable */
import { mapGetters } from "vuex";
// mixins
import SwitchCompanyMixin from "mixins/switch-company.js";

export default {
  name: "AppTop",
  mixins: [SwitchCompanyMixin],
  computed: {
    ...mapGetters([
      "token",
      "userName",
      "personName",
      "isRealName",
      "companyName",
      "companyType",
      "companyRole",
      "currentEnv",
      "roles",
    ]),
  },
  methods: {
    async logout() {
      try {
        await this.$store.dispatch("user/logout");
        await this.$store.dispatch("permission/generateRoutes", null);
        this.$message.success("您已退出该系统");
        this.$router.push({ name: "Home" });
      } catch (err) {
        console.log(err);
        this.$router.push({ name: "Home" });
      }
    },
    showMsg() {
      this.$alert("功能建设中，敬请期待").catch(err => console.log(err));
    },
    //判断当前登录账户是否为企业账户
    showAccountSettings(){
      if(this.userName.indexOf("qy_") === 0){
        return false;
      } else {
        return true;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.top-container {
  display: flex;
  background: #ffffff;
  width: 100%;
  color: #999;
  font-size: 12px;
  border-bottom: #eee 1px solid;

  &.fixed {
    position: fixed;
    z-index: 1001;
    top: 0;
    left: 0;
  }

  .wrapper-container {
    justify-content: space-between;
    align-items: center;
    height: 40px;
  }

  .link {
    align-items: center;
    &:hover {
      color: $color-primary;
      cursor: pointer;
    }
  }

  &-left {
    .icon-message {
      margin-right: 2px;
    }
  }

  &-right {
    &-item {
      margin-left: 10px;

      .el-icon-s-custom {
        margin-right: 2px;
      }
    }
  }
}

.user-info {
  &-title {
    padding-top: 5px;
    padding-bottom: 10px;
    border-bottom: $border;

    .url-btn {
      &:hover {
        color: $color-primary;
      }
    }
  }

  &-list {
    &-item {
      margin: 10px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.change-company {
  &:hover {
    color: #09f;
  }
}
</style>
