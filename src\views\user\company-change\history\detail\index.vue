<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="1000px"
      @close="close"
      class="change-detail-container"
    >
      <div v-loading="loading" element-loading-text="加载中，请稍等...">
        <el-tabs v-model="activeName" type="card" class="detail-tabs">
          <el-tab-pane
            v-for="(item) in tabs"
            :key="item.label"
            :label="item.label"
            :name="item.name"
          >
            <component
              :ref="item.name"
              :is="item.component"
              :active-tab-name="activeName"
              :data="data[item.data]"
              :loading="loading"
              :disable-edit="true"
              :info-type="infoType"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
      <el-radio-group class="info-type-radio" v-model="infoType" @change="changeInfo">
        <el-radio-button label="new">新单位信息</el-radio-button>
        <el-radio-button label="old">原单位信息</el-radio-button>
      </el-radio-group>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// mixins
import CompanyInfoTabs from "mixins/company-info/tabs.js";
// 组件
import Surveyor from "views/user/company-change/mapping/registered-surveyor/index.vue";
import ChangeApplyInfo from "views/user/company-change/mapping/change-apply-info/index.vue";
import AuditInfo from "./auditInfo.vue";
// Api
import Api from "api/company-change/index.js";

export default {
  name: "CompanyAuditDetail",
  mixins: [CompanyInfoTabs],
  components: {
    Surveyor,
    AuditInfo,
    ChangeApplyInfo
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false
    },
    // 记录数据
    row: {
      type: Object,
      default: () => ({
        ID: null,
        CompanyType: "测绘单位"
      })
    }
  },
  data() {
    return {
      loading: false,
      title: "单位信息变更详情",
      infoType: "new",
      newContent: {},
      oldContent: {}
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      this.activeName = "base";
      this.infoType = "new";

      this.mappingTabs = [
        {
          label: "基本信息",
          name: "base",
          data: "CompanyBaseInfo",
          component: "BaseInfo"
        },
        {
          label: "测绘资质信息",
          name: "qualifications",
          data: "CompanyQualification",
          component: "Qualifications"
        },
        {
          label: "注册测绘师信息",
          name: "surveyor",
          data: "CompanyEmployees",
          component: "Surveyor"
        },
        {
          label: "变更申请信息",
          name: "changeApplyInfo",
          data: "ChangeApplyInfo",
          component: "ChangeApplyInfo"
        },
        {
          label: "审核信息",
          name: "auditInfo",
          data: "AuditInfo",
          component: "AuditInfo"
        }
      ];

      this.initTabs();

      if (this.row.ID) {
        this.getCompanyChangeInfo();
      }
    },
    // 获取单位信息变更详情
    getCompanyChangeInfo() {
      this.loading = true;
      Api.GetModifyInfo(this.row.ID)
        .then(res => {
          const { StateCode, Data } = res;
          if (StateCode === 1) {
            let newContent = JSON.parse(Data.NewContent);
            let oldContent = JSON.parse(Data.OldContent);

            const {
              TypeName,
              CreateUserName,
              CreateUserNo,
              CreateUserPhone,
              ModifyReason,
              StateCode,
              AuditPerson,
              ResponseReason,
              SignTime,
              CloseTime
            } = Data;

            let ChangeApplyInfo = {
              TypeName,
              CreateUserName,
              CreateUserNo,
              CreateUserPhone,
              ModifyReason
            };

            let AuditInfo = {
              StateCode,
              AuditPerson,
              ResponseReason,
              SignTime,
              CloseTime
            };

            this.newContent = { ...newContent, ChangeApplyInfo, AuditInfo };
            this.oldContent = { ...oldContent, ChangeApplyInfo, AuditInfo };

            this.data = this.newContent;
          } else this.$message.error(res.Message);
          this.loading = false;
        })
        .catch(err => {
          console.log(err);
          this.loading = false;
          this.initTabs();
          this.$message.error("服务器繁忙，请刷新重试");
        });
    },
    // 改变信息类型
    changeInfo(e) {
      this.data = e === "new" ? { ...this.newContent } : { ...this.oldContent };
    },
    // 关闭存储弹窗
    close() {
      this.tabs = [];
      this.data = {};

      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.change-detail-container {
  /deep/ .el-dialog__body {
    padding: 10px 20px;
    position: relative;
  }
}

.detail-tabs {
  color: #000;
  /deep/ .el-tabs__header {
    margin-bottom: 0;
  }

  /deep/ .el-tabs__nav {
    overflow: hidden;
  }

  /deep/ .el-tabs__item {
    // background: #f4f5f7;

    &.is-active {
      background: #ffffff;
      border-bottom: none;
    }
  }

  /deep/ .el-tabs__content {
    min-height: 250px;
    padding: 20px;
    background: #ffffff;
    border-left: 1px solid #dfe4ed;
    border-right: 1px solid #dfe4ed;
    border-bottom: 1px solid #dfe4ed;
    border-radius: 0 0 4px 4px;
  }
}

.info-type-radio {
  position: absolute;
  right: 20px;
  top: 10px;
}
</style>