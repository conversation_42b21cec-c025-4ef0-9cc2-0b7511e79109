<template>
  <!-- eslint-disable -->
  <div class="view-info-container">
    <div class="business-info-title">
      <span>申请信息</span>
    </div>
    <el-card shadow="never">
      <el-form ref="form" :model="form" label-width="180px">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务编号：">{{ baseInfo.BusinessNumber | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务类型：">{{ baseInfo.BusinessType | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务名称：">{{ baseInfo.BusinessName | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人：">{{ baseInfo.CreatePersonName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机号：">{{ baseInfo.CreatePersonPhone | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位：">{{ baseInfo.DeveloperName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测绘单位：">{{ baseInfo.SurveyCompanyName | isNull }}</el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="业务信息" name="2">
            <el-form-item label="工程规划许可证：">
              <template
                v-if="contentInfo.ProjectPlanPermission && contentInfo.ProjectPlanPermission.length"
              >
                <project-licence-view :row="contentInfo.ProjectPlanPermission[0]" />
              </template>
              <empty v-else />
            </el-form-item>
            <el-form-item label="不动产单元号（宗地号）：">{{ contentInfo.GroundCode | isNull }}</el-form-item>
          </el-collapse-item>
          <el-collapse-item title="附件信息" name="3" v-if="showAttachmentInfo">
            <attachment-info
              :base-info="baseInfo"
              :step="step"
              :step-list="stepList"
              :attachment-data="attachmentData"
              @upload-success="upload($event, 'form', 'Others')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import ProjectLicenceView from "components/business/ProjectLicence/View.vue";
import AttachmentInfo from "./attachmentInfo.vue";
// mixins
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "PreMappingViewInfo",
  mixins: [ViewInfo],
  components: { ProjectLicenceView, AttachmentInfo },
  computed: {
    showAttachmentInfo() {
      const { step, mappingCompany } = this;
      if (step === 3 && mappingCompany) {
        return false;
      }

      return true;
    }
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
