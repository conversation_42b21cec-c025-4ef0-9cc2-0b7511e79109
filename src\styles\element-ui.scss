// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  // position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// form
.el-form-item__error {
  font-size: 14px;
}

.el-form-item {
  margin-bottom: 25px;
}

.el-card {
  margin: 15px;
}

.el-button--text {
  cursor: pointer;
}

.el-input__validateIcon.el-icon-circle-check {
  color: #67c23a;
}

aside {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

.el-table th.is-leaf {
  background-color: #f8f8f8;
}

.el-range-editor--medium .el-range-separator {
  text-indent: -6px;
}

// 导航
.el-menu--popup-bottom-start {
  .nav-list-menu__item {
    .el-menu-item {
      color: $subMenuText !important;
      i {
        color: $subMenuText;
      }

      &.is-active {
        i,
        span {
          color: $subMenuActiveText;
        }

        &:hover {
          i {
            color: $subMenuActiveText;
          }
        }
      }

      &:hover {
        background: #1b1f23 !important;
        color: $subMenuText !important;

        i {
          color: $subMenuText;
        }
      }
    }
  }
}

.el-menu--horizontal {
  .el-menu {
    &.el-menu--popup {
      background: #2e363f;
    }

    .nav-list-submenu {
      .el-menu-item,
      .el-submenu__title {
        background: #2e363f;

        &:hover {
          color: $subMenuActiveText;
          background: #1b1f23 !important;
        }
      }
    }
  }
}

.app-breadcrumb-container.el-breadcrumb .no-redirect {
  color: #777 !important;
}

.el-loading-mask {
  z-index: 1000 !important;
}

// 弹窗样式
.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: fixed !important; // 解决谷歌浏览器49版本因优先使用原UI样式position:relative导致弹窗窗口未居中的问题
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /*height:600px;*/
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);

  .el-dialog__body {
    // flex: 1;
    overflow: auto;
    max-height: calc(100% - 110px);
  }
}

// 修复BUG：多选在IE中样式不正常，数据内容不显示
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .el-cascader__tags > span {
    flex: auto;
  }

  i.el-cascader-node__postfix {
    top: 50%;
    transform: translateY(-50%);
  }
}

// 确认弹窗
.el-message-box__message{
  font-size: 16px;
}

// 消息弹窗
.el-message__content{
  font-size: 16px !important;
}