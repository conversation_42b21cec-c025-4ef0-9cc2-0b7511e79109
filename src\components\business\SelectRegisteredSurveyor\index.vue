<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="800px"
    @close="close"
    class="select-registered-survayor-dialog-container"
  >
    <div>
      <!-- 表格 -->
      <dynamic-table
        v-loading="listLoading"
        element-loading-text="加载中，请稍等..."
        :table-header="tableHeader"
        :table-data="listData"
        :default-props="tableProps"
        :showPagination="false"
        empty-text="暂无可选择的注册测绘师"
      >
        <el-table-column width="50" label="序号" align="center">
          <template slot-scope="{ $index }">{{
            $index + 1
          }}</template>
        </el-table-column>
        <el-table-column
          prop="PersonName"
          label="姓名"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="PersonPhone"
          label="联系电话"
          align="center"
        ></el-table-column>
        <el-table-column
          v-if="listData.length"
          prop="action"
          label="操作"
          width="100"
          fixed="right"
          align="center"
        >
          <template slot-scope="{ row, $index }">
            <el-button
              type="text"
              icon="el-icon-check"
              @click="selectRegisteredSurveyor(row, $index)"
              v-if="row.IsExpired != true"
              >选择</el-button
            >
            <div v-if="row.IsExpired == true">已过期</div>
          </template>
        </el-table-column>
      </dynamic-table>
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// 组件
import DynamicTable from "components/common/Table/DynamicTable";
// mixins
import Table from "mixins/table.js";
// Api
import Api from "api/registered-surveyor-auth/index.js";

export default {
  name: "SelectRegisteredSurveyor",
  components: { DynamicTable },
  mixins: [Table],
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 业务ID
    businessId: {
      type: String,
      default: null,
    },
    // 环节ID
    actionId: {
      type: String,
      default: null,
    },
    // 业务名称
    flowName: {
      type: String,
      default: "",
    },
    // 竣工用地面积
    completionLandArea: {
      type: String,
      default: null,
    },
    // 竣工总建筑面积
    completionBuildingArea: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      listLoading: false,
      tableHeader: [],
      title: "提交委托方验收",
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      const { businessId, $message } = this;
      this.listLoading = true;
      Api.GetAuthorizedSurveyMasters(businessId)
        .then((res) => {
          const { StateCode, Data, Message } = res;
          if (StateCode === 1) {
            this.listData = Data;
          } else {
            $message.error(Message);
          }
          this.listLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.listLoading = false;
        });
    },
    // 选择注册测绘师
    selectRegisteredSurveyor(row, index) {
      const { businessId, actionId, flowName, completionLandArea, completionBuildingArea, $message } = this;

      this.$confirm(
        `您将选择注册测绘师<span style="color: #f0ad4e">【${
          row.PersonName
        }】</span>进行<span style="color: #27b0f1">【${flowName}】</span>成果确认`,
        "温馨提示",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消",
        }
      )
        .then(() => {
          Api.ConfirmSurveyResultUsingSurveyMasterAuth(businessId, actionId, row.AuthRequest.ID, completionLandArea, completionBuildingArea)
            .then(res => {
              const { StateCode, Data, Message } = res;
              if (StateCode === 1) {
                $message.success("操作成功");
                this.$emit("select", null);
                this.close();
              } else {
                $message.error(Message);
              }
            })
            .catch(err => {
              console.log(err);
            });
        })
        .catch((err) => console.log(err));
    },
    // 关闭存储弹窗
    close() {
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
  },
};
</script>
