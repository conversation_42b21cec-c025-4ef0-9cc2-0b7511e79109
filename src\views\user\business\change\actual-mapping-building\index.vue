<template>
  <business-layout
    class="pre-mapping-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="prevBtn"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @prev="prev"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1 && developer">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业主单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <!-- <el-form-item
              label-width="170px"
              label="工程规划许可证件："
              prop="ProjectLicenceImg"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.ProjectLicenceImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '工程规划许可证件',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ProjectLicenceImg')"
                @delete="del($event, 'form1', 'ProjectLicenceImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="建筑定位图："
              prop="BuildingLocationMap"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.BuildingLocationMap"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '建筑定位图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BuildingLocationMap')"
                @delete="del($event, 'form1', 'BuildingLocationMap')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="总平面图："
              prop="SitePlan"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.SitePlan"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '总平面图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SitePlan')"
                @delete="del($event, 'form1', 'SitePlan')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="经备案的建筑设计图："
              prop="BuildingDesgin"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.BuildingDesgin"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '经备案的建筑设计图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BuildingDesgin')"
                @delete="del($event, 'form1', 'BuildingDesgin')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="门牌证明："
              prop="DoorPlate"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.DoorPlate"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '门牌证明',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'DoorPlate')"
                @delete="del($event, 'form1', 'DoorPlate')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item> -->
            <!-- ChangeDescription -->
            <el-form-item
              label-width="170px"
              label="变更后的楼盘信息表："
              prop="ChangeBuildingInfo"
            >
              <list-upload
                file-format="xls / xlsx"
                :file-list="form1.ChangeBuildingInfo"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '变更后的楼盘信息表',
                }"
                :on-check-format="checkXls"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ChangeBuildingInfo')"
                @delete="del($event, 'form1', 'ChangeBuildingInfo')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="变更说明："
              prop="ChangeDescription"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.ChangeDescription"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '变更说明',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ChangeDescription')"
                @delete="del($event, 'form1', 'ChangeDescription')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label-width="170px" label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar / dwg"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2 && BaseInfo.StateCode !== 2">
        <tip class="mb-20 font-20 text-center bold">
          <!-- 已提交审核，
          <i
            class="el-icon-loading mr-5"
          />您上传的变更说明已提交审核，审核时间为3-5个工作日，请耐心等待结果 -->
          变更申请已提交至南宁市不动产登记中心进行检查（3个工作日）
        </tip>
      </div>
      <div v-if="currentStep === 2 && BaseInfo.StateCode === 2">
        <tip class="mb-20 font-20 text-center bold"> 已完成审核并变更 </tip>
      </div>
      <!-- 测绘成果下载 -->
      <result
        v-if="showResult()"
        class="mb-20"
        :survey-result="surveyResult"
        :property-info="propertyInfo"
        :pre-report="form3.ActualReport"
        :audit-feedback="auditFeedback"
        :building-table-info="ContentInfo.BuildingTableInfo"
        :project-result-info="ContentInfo.ProjectResultInfo"
        :name="FlowInfo.FlowName"
        :step="currentStep"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep == 3">
        <el-button
          v-if="developer && BaseInfo.StateCode === 0"
          :loading="prevBtn.loading"
          type="primary"
          @click="reSelectSurveyCompany()"
        >
          <i class="iconfont icon-back mr-5" />重新委托测绘单位
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="disabledBack()"
          @click="setReasonDialogVisible('退回业务', true)"
        >
          <i class="iconfont icon-back mr-5" />退回业务
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          type="success"
          :disabled="disabledBack()"
          @click="submitAccept(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交验收
        </el-button>
      </template>
      <template
        v-if="currentStep === 4 && developer && BaseInfo.StateCode === 1"
      >
        <el-button
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('验收不通过', true)"
        >
          <i class="iconfont icon-back mr-5" />验收不通过
        </el-button>
        <el-button
          class="ml-10"
          :loading="acceptBtn.loading"
          type="success"
          @click="accept()"
        >
          <i class="el-icon-check mr-5" />验收完成
        </el-button>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2 || (currentStep === 1 && mappingCompany)"
        :base-info="BaseInfo"
        :content-info="ContentInfo"
        :attachment-data="form1"
        :step="currentStep"
        :step-list="stepList"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 额外内容 -->
    <template slot="extra">
      <!-- 获取工程规划许可证 -->
      <project-licence-get-dialog
        :visible.sync="licenceGetDialog.visible"
        :type="licenceGetDialog.type"
        :list="form1.ProjectPlanPermission"
        :business-id="businessID"
        :business-class="BaseInfo.BusinessClass"
        @add-lience="storeLicence(null, -1)"
        @submit="licenceGetSuccess"
        @close="setLicenceGetDialogVisible(false)"
      />
      <!-- 存储工程规划许可证 -->
      <project-licence-store-dialog
        :visible.sync="licenceStoreDialog.visible"
        :row="licenceStoreDialog.row"
        :index="licenceStoreDialog.index"
        :list="form1.ProjectPlanPermission"
        @submit="licenceStoreSuccess"
        @close="setLicenceStoreDialogVisible(false)"
      />
      <!-- 提交验收，选择注册测绘师弹窗 -->
      <select-registered-surveyor
        :visible.sync="selectRSurveyorDialog.visible"
        :business-id="BaseInfo.ID"
        :action-id="CurrentAction.ID"
        :flow-name="FlowInfo.FlowName"
        @select="getBusiness"
        @close="setSelectRSurveyorDialogVisible(false)"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import TimingProgressBar from "components/business/TimingProgressBar/index.vue";
import ViewInfo from "./view-info/index.vue";
import AttachmentInfo from "./view-info/attachmentInfo.vue";
import Result from "./result.vue";
// mixins
import BusinessMixin from "mixins/business/index.js";
import CheckLineMixin from "mixins/business/check-line.js";
import SurveyResultMixin from "mixins/business/survey-result.js";
import SelectMappingCompanyMixin from "mixins/business/select-mapping-company.js";
import SelectRegisteredSurveyorMixin from "mixins/business/select-registered-survey.js";
// 校验
import {
  validateBusinessName,
  validateAttachment,
  validateGroundCode,
} from "utils/form.js";

export default {
  name: "PreMapping",
  components: {
    TimingProgressBar,
    ViewInfo,
    AttachmentInfo,
    Result,
  },
  mixins: [
    BusinessMixin,
    CheckLineMixin,
    SurveyResultMixin,
    SelectMappingCompanyMixin,
    SelectRegisteredSurveyorMixin,
  ],
  data() {
    return {
      // 业务内容信息，不同业务内容不同
      ContentInfo: {
        ProjectPlanPermission: [],
      },
      // 步骤1
      form1: {
        BusinessName: null,
        // GroundCode: null,
        // ProjectPlanPermission: [],
        // ProjectLicenceImg: [],
        // BuildingLocationMap: [],
        // SitePlan: [],
        // BuildingDesgin: [],
        // DoorPlate: [],
        ChangeDescription: [],
        ChangeBuildingInfo: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],
        // GroundCode: [
        //   { required: true, validator: validateGroundCode, trigger: "blur" },
        // ],
        // ProjectPlanPermission: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) => {
        //       if (
        //         !this.form1.ProjectPlanPermission ||
        //         !this.form1.ProjectPlanPermission.length
        //       ) {
        //         callback(new Error("请添加工程规划许可证"));
        //       } else {
        //         callback();
        //       }
        //     },
        //     trigger: "change",
        //   },
        // ],
        // ProjectLicenceImg: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) =>
        //       validateAttachment(
        //         rule,
        //         value,
        //         callback,
        //         this,
        //         "form1",
        //         "ProjectLicenceImg",
        //         "工商许可证件"
        //       ),
        //     trigger: "change",
        //   },
        // ],
        // BuildingLocationMap: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) =>
        //       validateAttachment(
        //         rule,
        //         value,
        //         callback,
        //         this,
        //         "form1",
        //         "BuildingLocationMap",
        //         "建筑定位图"
        //       ),
        //     trigger: "change",
        //   },
        // ],
        // SitePlan: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) =>
        //       validateAttachment(
        //         rule,
        //         value,
        //         callback,
        //         this,
        //         "form1",
        //         "SitePlan",
        //         "总平面图"
        //       ),
        //     trigger: "change",
        //   },
        // ],
        // BuildingDesgin: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) =>
        //       validateAttachment(
        //         rule,
        //         value,
        //         callback,
        //         this,
        //         "form1",
        //         "BuildingDesgin",
        //         "经备案的建筑设计图"
        //       ),
        //     trigger: "change",
        //   },
        // ],
        // DoorPlate: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) =>
        //       validateAttachment(
        //         rule,
        //         value,
        //         callback,
        //         this,
        //         "form1",
        //         "DoorPlate",
        //         "门牌证明"
        //       ),
        //     trigger: "change",
        //   },
        // ],

        // ChangeDescription
        ChangeDescription: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ChangeDescription",
                "变更说明"
              ),
            trigger: "change",
          },
        ],
        // ChangeBuildingInfo
        ChangeBuildingInfo: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ChangeBuildingInfo",
                "变更后的楼盘信息表"
              ),
            trigger: "change",
          },
        ],
      },
      // 步骤3
      form3: {
        Data: [],
        ActualReport: [],
      },
      rules3: {
        Data: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Data",
                "测绘成果"
              ),
            trigger: "change",
          },
        ],
        ActualReport: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "ActualReport",
                "不动产实核报告"
              ),
            trigger: "change",
          },
        ],
      },
    };
  },
  destroyed() {
    this.clearTimer(this.resultTimer);
  },
  methods: {
    // 额外处理请求数据
    handleApiData(Data) {
      const {
        ProjectPlanPermission,
        BuildingTableInfo,
        ProjectResultInfo,
      } = Data.ContentInfo;
      this.ContentInfo = {
        ...Data.ContentInfo,
        ProjectPlanPermission: ProjectPlanPermission
          ? JSON.parse(ProjectPlanPermission)
          : [],
        BuildingTableInfo: BuildingTableInfo
          ? JSON.parse(BuildingTableInfo)
          : [],
        ProjectResultInfo: ProjectResultInfo
          ? JSON.parse(ProjectResultInfo)
          : [],
      };
    },
    // 处理表单数据
    handleFormData(currentStep) {
      const {
        BaseInfo,
        ContentInfo,
        Attachments,
        mappingCompany,
        getSurveyResultCheckState,
      } = this;
      const { BusinessName } = BaseInfo;
      const {
        DataCheckID,
        DataCheckState,
        GroundCode,
        ProjectPlanPermission,
      } = ContentInfo;

      // 处理附件
      let ProjectLicenceImg = [];
      let BuildingLocationMap = [];
      let SitePlan = [];
      let WorkingDraw = [];
      let BuildingDesgin = [];
      let DoorPlate = [];
      let ChangeDescription = [];
      let ChangeBuildingInfo = [];
      let Others = [];
      let ActualReport = [];
      this.auditFeedback = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "工程规划许可证件":
                ProjectLicenceImg.push(e);
                break;
              case "建筑定位图":
                BuildingLocationMap.push(e);
                break;
              case "总平面图":
                SitePlan.push(e);
                break;
              case "全套建筑施工图":
                WorkingDraw.push(e);
                break;
              case "经备案的建筑设计图":
                BuildingDesgin.push(e);
                break;
              case "门牌证明":
                DoorPlate.push(e);
                break;
              // ChangeDescription
              //ChangeBuildingInfo
              case "变更后的楼盘信息表":
                ChangeBuildingInfo.push(e);
                break;
              case "变更说明":
                ChangeDescription.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "项目成果附件") {
            switch (e.AttachmentCategories) {
              case "不动产实核报告":
                ActualReport.push(e);
                break;
              case "不动产测绘成果":
                this.surveyResult = e;
                break;
              case "变更后的楼盘信息表":
                this.propertyInfo = e;
                break;
              case "不动产实核测绘成果审核反馈":
                this.auditFeedback.push(e);
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
        });
      }

      this.form1 = {
        BusinessName,
        // GroundCode,
        // ProjectPlanPermission,
        // ProjectLicenceImg,
        // BuildingLocationMap,
        // SitePlan,
        // WorkingDraw,
        // BuildingDesgin,
        // DoorPlate,
        ChangeDescription,
        ChangeBuildingInfo,
        Others,
      };

      this.form3 = {
        // 判断Data是否有值，DataCheckState： 0 检查中  1 检查通过  2 检查不通过
        Data:
          DataCheckID && DataCheckState > 0
            ? [{ name: "hasResult", status: "success" }]
            : [],
        ActualReport,
      };

      // 审核中定时请求接口
      if (currentStep === 3 && mappingCompany) {
        if (DataCheckID && DataCheckState === 0) {
          getSurveyResultCheckState(DataCheckID);
        }
      }
    },
    // 获取工程规划许可证
    getLicence() {
      this.licenceGetDialog = {
        visible: true,
        type: this.form1.ProjectPlanPermission.length > 0 ? 1 : 0,
      };
    },
    // 工程规划许可证获取成功
    licenceGetSuccess(params) {
      this.form1.ProjectPlanPermission[0] = params;
      this.$refs.form1.clearValidate("ProjectPlanPermission");
    },
    // 工程规划许可证存储成功
    licenceStoreSuccess(params, index) {
      this.form1.ProjectPlanPermission[0] = params;
      this.$refs.form1.clearValidate("ProjectPlanPermission");
    },
    // 显示成果数据
    showResult() {
      const { currentStep, mappingCompany, ContentInfo } = this;

      // if (
      //   currentStep === 3 &&
      //   mappingCompany &&
      //   ContentInfo.DataCheckID &&
      //   ContentInfo.DataCheckState === 1
      // ) {
      //   return true;
      // }
      if (currentStep === 2) {
        return true;
      }

      return false;
    },
    // 禁用退回、提交验收和确认通过（测试按钮）
    disabledBack() {
      const { DataCheckID, DataCheckState } = this.ContentInfo;

      // 正在在检查成果
      if (DataCheckID && DataCheckState === 0) {
        return true;
      }
      return false;
    },

    // 处理按钮
    handleOperateBtn(currentStep) {
      this.closeBtn.visible = currentStep <= 1 ? true : false;
      this.saveBtn.visible = currentStep <= 1 ? true : false;
      this.nextBtn.visible = currentStep <= 1 ? true : false;
      this.prevBtn.visible = currentStep <= 1 ? true : false;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
