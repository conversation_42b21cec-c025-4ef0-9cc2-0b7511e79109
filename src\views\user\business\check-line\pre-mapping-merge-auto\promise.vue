<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      class="create-business-dialog"
      @close="close"
      center
    >
      <ol class="promise-list">
        <span class="promise-title">本公司（单位）承诺：</span>
        <li class="promise-list-item">
          该项目（单体）的《建筑设计方案文本》已通过城乡规划主管部门审批，并已取得城乡规划主管部门核发的《建设工程规划许可证》及附件；
        </li>
        <li class="promise-list-item">
          该项目（单体）的建筑设计图纸将按照《建筑设计方案》内容进行审图备案；
        </li>
        <li class="promise-list-item">
          我司将严格按照城乡规划主管部门审批的《建筑设计方案》、《建设工程规划许可证》及附件和其他批准文件等相关规划内容进行建设；若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a class="link" @click="openPromisePdf()">点击此处</a
          >下载不动产预核业务承诺书</span
        >
      </ol>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm()"
          :loading="saveLoading"
          :disabled="confirmBtn.disabled"
          >我承诺{{ this.confirmBtn.text }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";
const urlPrefix = process.env.VUE_APP_URL_PREFIX;
export default {
  name: "Promise",
  computed: {
    ...mapGetters(["userName", "personName"]),
    // 显示示例
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: "承诺书",
      confirmBtn: {
        disabled: true,
        time: 15,
        text: "",
        timer: null,
      },
      // loading
      saveLoading: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  destroyed() {
    this.clearTimer(this.confirmBtn.timer);
  },
  methods: {
    // 初始化
    init() {
      this.countDown();
      const { business } = this;
    },
    //倒计时方法
    countDown() {
      this.confirmBtn.time = 15;
      this.confirmBtn.disabled = true;
      const timeCount = this.confirmBtn.time;
      this.confirmBtn.text = `（${timeCount}s）`;
      if (!this.confirmBtn.timer) {
        this.confirmBtn.timer = setInterval(() => {
          if (this.confirmBtn.time > 1 && this.confirmBtn.time <= timeCount) {
            this.confirmBtn.disabled = true;
            this.confirmBtn.time--;
            this.confirmBtn.text = `（${this.confirmBtn.time}s）`;
          } else {
            this.confirmBtn.disabled = false;
            // clearInterval(this.confirmBtn.timer);
            this.confirmBtn.text = ``;
            // this.confirmBtn.timer = null;
            this.clearTimer();
          }
        }, 1000);
      }
    },
    // 清除定时器
    clearTimer() {
      if (this.confirmBtn.timer) {
        clearInterval(this.confirmBtn.timer);
        this.confirmBtn.timer = null;
      }
    },
    // 打开操作指南
    openPromisePdf() {
      window.open(`${urlPrefix}/static/pdf/不动产预核业务承诺书.pdf`, "_blank");
    },

    // 关闭存储弹窗
    close() {
      this.clearTimer();
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm() {
      this.$emit("submit", true);
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
.create-business-dialog {
  /deep/ .el-dialog__body {
    padding-bottom: 5px;
    padding-top: 0;
  }
  /deep/ .el-dialog__title {
    font-weight: bold;
    letter-spacing: 2px;
    font-size: 20px;
  }
  .promise-title {
    margin-left: -15px;
    font-size: 16px;
    padding: 5px 0;
    display: inline-block;
    color: #303133;
  }
  .promise-list-item {
    list-style-type: decimal;
    padding: 5px 0;
    font-size: 16px;
    color: #303133;
  }
  .download-line {
    font-size: 14px;
    display: inline-block;
    padding-top: 5px;
    .link {
      color: #f07057;
      text-decoration: underline;
      cursor: pointer;
    }
  }
}
</style>
