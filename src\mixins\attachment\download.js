/*
 * 模块 : 文件下载相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-10-23
 * 版本 : version 1.0
 */
/* eslint-disable */
// Api
import Api from "api/public/index.js";
// 工具
import { downloadFileByUrl, downloadFileByStream } from "utils/index.js";

export default {
  methods: {
    // 下载文件
    async download(file) {
      if (!file || !file.ID) {
        this.$message.error("文件不存在，无法下载");
        return;
      }

      this.$emit("download-start", file);

      // 通过key值获取文件绝对路径下载
      try {
        const res = await Api.GetAttachmentDownloadKey(file.ID);

        const { StateCode, Data } = res;
        const { AttachmentName, AttachmentExt } = file;

        if (StateCode === 1) {
          downloadFileByUrl(
            Api.DownloadAttachmentByKey(Data, AttachmentName + AttachmentExt),
            AttachmentName + AttachmentExt
          );
          this.$emit("download-end", file);
        } else {
          this.$emit("download-fail", file);
        }
      } catch (err) {
        this.$emit("download-fail", file);
      }

      // 通过文件流下载
      // Api.DownloadAttachment(file.ID)
      //     .then((res) => {
      //         const { AttachmentName, AttachmentExt } = file;

      //         downloadFileByStream(res, AttachmentName + AttachmentExt);
      //         this.$emit("download-end", file);
      //     })
      //     .catch((err) => {
      //         this.$emit("download-fail", file);
      //     });
    },
    // 下载图片
    downloadImg(file) {
      var a = window.document.createElement("a");
      a.download = file.AttachmentName + file.AttachmentExt;
      a.href = Api.ShowImg(file.ID);
      a.target = "_blank";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
  },
};
