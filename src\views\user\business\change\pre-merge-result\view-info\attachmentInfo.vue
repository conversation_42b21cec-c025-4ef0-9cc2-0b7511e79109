<template>
  <!-- eslint-disable -->
  <el-form ref="form" :model="form" label-width="140px">
    <el-form-item label-width="160px" label="工程规划许可证件：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.ProjectLicenceImg"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '工程规划许可证件',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ProjectLicenceImg')"
        @delete="del($event, 'form', 'ProjectLicenceImg')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ProjectLicenceImg"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="不动产权证书：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="pdf"
        :file-list="form.PropertyCertificate"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '不动产权证书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PropertyCertificate')"
        @delete="del($event, 'form', 'PropertyCertificate')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PropertyCertificate"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="土地出让合同/土地划拨决定书：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="pdf"
        :file-list="form.LandContract"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '土地出让合同/土地划拨决定书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'LandContract')"
        @delete="del($event, 'form', 'LandContract')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.LandContract"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="经备案的建筑设计图：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="dwg"
        :file-list="form.BuildingDesgin"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '经备案的建筑设计图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'BuildingDesgin')"
        @delete="del($event, 'form', 'BuildingDesgin')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
         v-else
        :file-list="attachmentData.BuildingDesgin"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="变更说明：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.ChangeDescription"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '变更说明',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ChangeDescription')"
        @delete="del($event, 'form', 'ChangeDescription')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ChangeDescription"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="授权委托书：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="pdf"
        :file-list="form.AuthorizationLetter"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '授权委托书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'AuthorizationLetter')"
        @delete="del($event, 'form', 'AuthorizationLetter')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.AuthorizationLetter"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="建筑定位图：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="dwg"
        :file-list="form.BuildingLocationMap"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '建筑定位图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'BuildingLocationMap')"
        @delete="del($event, 'form', 'BuildingLocationMap')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.BuildingLocationMap"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="总平面图：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="dwg"
        :file-list="form.SitePlan"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '总平面图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'SitePlan')"
        @delete="del($event, 'form', 'SitePlan')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.SitePlan"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="门牌证明（变更门牌时需上传）：">
      <list-upload
        v-if="isRecordBack(4)"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.DoorPlate"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '门牌证明（变更门牌时需上传）',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'DoorPlate')"
        @delete="del($event, 'form', 'DoorPlate')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.DoorPlate"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="其他：">
      <list-upload
        v-if="isRecordBack(4) || isRecordAction(4)"
        file-format="png / jpg / gif / bmp / pdf / zip / rar"
        :file-list="form.Others"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '其他附件',
        }"
        :on-check-format="checkOthers"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'Others')"
        @delete="del($event, 'form', 'Others')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.Others"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
  </el-form>
</template>

<script>
/* eslint-disable */
// 组件
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "PreMappingAttachmentInfo",
  mixins: [ViewInfo],
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e;
        if (e.length) {
          this.$refs[formName].clearValidate(attr);
        }
      }

      this.$emit("upload-success", e);
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
