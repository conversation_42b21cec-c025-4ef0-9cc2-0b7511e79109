<template>
  <business-layout
    class="pre-mapping-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="prevBtn"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @prev="prev"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1 && developer">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业主单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="业务信息" style="margin-top: -10px">
            <el-form-item
              label-width="190px"
              label="工程规划许可证："
              prop="ProjectPlanPermission"
            >
              <span slot="label">
                工程规划许可证
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点击查看图例"
                  placement="bottom"
                >
                  <i
                    class="el-icon-question"
                    @click="viewLegend('工规证')"
                  /> </el-tooltip>：</span>
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="form1.ProjectPlanPermission"
                :default-props="tableProps"
                :show-pagination="false"
              >
                <el-table-column
                  prop="action"
                  label="操作"
                  width="180"
                  fixed="right"
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index)"
                    >查看</el-button>
                    <el-button
                      v-if="row.Add && BaseInfo.StateCode !== 4"
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="storeLicence(row, $index)"
                    >编辑</el-button>
                    <el-popconfirm
                      v-if="BaseInfo.StateCode !== 4"
                      title="确认删除?"
                      @onConfirm="delLicence(row, $index)"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        class="ml-10"
                      >删除</el-button>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </dynamic-table>
              <div
                v-if="BaseInfo.StateCode !== 4"
                class="table-add-btn"
                @click="setLicenceGetDialogVisible(true)"
              >
                <i class="el-icon-plus" />添加工程规划许可证
              </div>
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="不动产单元号（宗地号）："
              prop="GroundCode"
            >
              <el-input
                v-model.trim="form1.GroundCode"
                placeholder="宗地号为7位或者19位，如：450103001001GB00001"
                disabled
              />
            </el-form-item>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item
              label-width="170px"
              label="工程规划许可证件："
              prop="ProjectLicenceImg"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.ProjectLicenceImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '工程规划许可证件',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ProjectLicenceImg')"
                @delete="del($event, 'form1', 'ProjectLicenceImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="不动产权证书："
              prop="PropertyCertificate"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.PropertyCertificate"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '不动产权证书',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'PropertyCertificate')"
                @delete="del($event, 'form1', 'PropertyCertificate')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="土地出让合同/土地划拨决定书："
              prop="LandContract"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.LandContract"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '土地出让合同/土地划拨决定书',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'LandContract')"
                @delete="del($event, 'form1', 'LandContract')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
              <div class="upload-tip">
                <i class="el-icon-warning-outline" />
                <span>温馨提示：二选一，只需要上传土地出让合同或者土地划拨决定书</span>
              </div>
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="经备案的建筑设计图："
              prop="BuildingDesgin"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.BuildingDesgin"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '经备案的建筑设计图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BuildingDesgin')"
                @delete="del($event, 'form1', 'BuildingDesgin')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="变更说明："
              prop="ChangeDescription"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.ChangeDescription"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '变更说明',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ChangeDescription')"
                @delete="del($event, 'form1', 'ChangeDescription')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="授权委托书："
              prop="AuthorizationLetter"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.AuthorizationLetter"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '授权委托书',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'AuthorizationLetter')"
                @delete="del($event, 'form1', 'AuthorizationLetter')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="建筑定位图："
              prop="BuildingLocationMap"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.BuildingLocationMap"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '建筑定位图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BuildingLocationMap')"
                @delete="del($event, 'form1', 'BuildingLocationMap')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="总平面图："
              prop="SitePlan"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.SitePlan"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '总平面图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SitePlan')"
                @delete="del($event, 'form1', 'SitePlan')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="门牌证明（变更门牌时需上传）："
              prop="DoorPlate"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.DoorPlate"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '门牌证明（变更门牌时需上传）',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'DoorPlate')"
                @delete="del($event, 'form1', 'DoorPlate')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>

            <el-form-item label-width="170px" label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar / dwg"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2 && developer">
        <!-- 委托测绘单位 -->
        <mapping-company-list
          class="mb-20"
          :business-class="BaseInfo.BusinessClass"
          :business-id="businessID"
          :current-action-id="CurrentAction.ID"
          :flow-name="FlowInfo.FlowName"
          @select="refreshStep"
        />
      </div>
      <div v-if="currentStep === 3">
        <!-- 上传测绘成果 -->
        <tip
          v-if="developer"
          class="mb-20 font-20 text-center bold"
        >等待测绘单位汇交并确认测绘成果...</tip>
        <el-form
          v-if="mappingCompany"
          ref="form3"
          class="mb-40"
          :model="form3"
          :rules="rules3"
          label-width="100px"
        >
          <!-- 提示语 -->
          <tip
            v-if="!ContentInfo.DataCheckID"
            class="mb-20"
          >请上传测绘成果</tip>
          <template v-else>
            <div v-if="ContentInfo.DataCheckState === 0" class="mb-20">
              <tip type="default" class="mb-20">
                测绘成果已上传完成，
                <i
                  class="el-icon-loading mr-5"
                />系统正在对成果进行检查，检查大概需要5分钟，请耐心等待结果
              </tip>
              <timing-progress-bar :is-finished="surveyResultCheckFinished" />
            </div>
            <tip
              v-if="ContentInfo.DataCheckState === 1"
              type="success"
              class="mb-20"
            >
              您上传的测绘成果符合南宁市不动产测绘成果格式要求，可<template
                v-if="surveyBusinessManager || surveyAdmin"
              >联系本单位注册测绘师</template>登录邕e登App进入“我的授权”模块刷脸确认测绘成果。若想修改，请重新上传测绘成果
            </tip>
            <tip
              v-if="ContentInfo.DataCheckState === 2"
              type="error"
              class="mb-20"
            >
              您上传的测绘成果未能通过检查，请
              <span
                class="link"
                @click="downloadSurveyResultErrorReport('不动产预测绘')"
              >点击此处</span>下载成果检查报告，待整改后重新上传
            </tip>
          </template>
          <!-- 附件信息 -->
          <el-collapse value="1" class="mb-20">
            <el-collapse-item title="申请信息附件" name="1">
              <attachment-info
                v-if="mappingCompany"
                class="mt-10"
                :base-info="BaseInfo"
                :step="currentStep"
                :step-list="stepList"
                :attachment-data="form1"
                :actions-infos="ActionsInfos"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-collapse-item>
          </el-collapse>
          <!-- 上传按钮 -->
          <el-row class="mb-20">
            <el-col :xs="24" :sm="11" :md="11" :lg="11" :xl="11">
              <el-form-item label-width="260px" label="竣工用地面积（该地块用地面积）：" prop="CompletionLandArea">
                <el-input
                  v-model="form3.CompletionLandArea"
                  placeholder="请输入竣工用地面积"
                  style="width: 90%"
                >
                  <template slot="append">㎡</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="11" :md="11" :lg="11" :xl="11" :offset="2">
              <el-form-item label-width="260px" label="竣工总建筑面积（单体总建筑面积）：" prop="CompletionBuildingArea">
                <el-input
                  v-model="form3.CompletionBuildingArea"
                  placeholder="请输入竣工总建筑面积"
                  style="width: 90%"
                >
                  <template slot="append">㎡</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-if="
              !ContentInfo.DataCheckID ||
                (ContentInfo.DataCheckID && ContentInfo.DataCheckState > 0)
            "
            :gutter="12"
          >
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label-width="100px" label="测绘成果：" prop="Data">
                <mdb-upload
                  :id="businessID"
                  :file-list="form3.Data"
                  :file-size="102400"
                  :upload-url="resultUploadUrl"
                  @upload-success="resultUpload"
                />
              </el-form-item>
            </el-col>
            <el-col
              class="example-container"
              :xs="24"
              :sm="12"
              :md="12"
              :lg="12"
              :xl="12"
            >
              <p class="example-hint">温馨提示：</p>
              <ol class="example-list">
                <li class="example-list-item">
                  所上传的测绘成果须符合
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment('不动产预测绘数据汇交标准.docx')
                    "
                  >《南宁市不动产预测绘数据汇交标准》</a>
                </li>
                <li class="example-list-item">
                  所上传的测绘成果文件参考样例：
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment(
                        '不动产预测绘（测绘成果样例）.mdb'
                      )
                    "
                  >不动产预测绘（测绘成果样例）.mdb</a>
                </li>
                <li class="example-list-item">
                  如有疑问，请联系南宁市不动产登记中心测绘管理部，联系电话：
                  <a class="example-list-item__phone">4306662</a>
                </li>
              </ol>
            </el-col>
          </el-row>
          <!-- 不动产预核报告 -->
          <!-- <tip
            v-if="!form3.PreReport.length"
            class="mb-20"
          >请上传不动产预核报告</tip>
          <tip
            v-else
            type="success"
            class="mb-20"
          >您的不动产预核报告已上传完成</tip>
          <el-form-item
            label-width="145px"
            label="不动产预核报告："
            prop="PreReport"
          >
            <list-upload
              file-format="pdf"
              :file-list="form3.PreReport"
              :file-size="102400"
              :data="{
                BusinessType: BaseInfo.BusinessType,
                BusinessID: businessID,
                AttachmentType: '项目成果附件',
                AttachmentCategories: '不动产预核报告',
              }"
              :on-check-format="checkPDF"
              @upload-success="upload($event, 'form3', 'PreReport')"
              @delete="del($event, 'form3', 'PreReport')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-form-item> -->
        </el-form>
      </div>
      <div
        v-if="currentStep === 4 && mappingCompany && BaseInfo.StateCode !== 2"
        class="mb-20"
      >
        <tip
          class="font-20 text-center bold"
        >测绘成果已确认通过，等待业主单位验收成果...</tip>
      </div>
      <div v-if="currentStep === 5 && BaseInfo.StateCode !== 2" class="mb-20">
        <tip class="font-20 text-center bold">
          变更申请已提交至南宁市不动产登记中心进行检查（8个工作日）
        </tip>
      </div>
      <!-- 测绘成果下载 -->
      <result
        v-if="showResult()"
        class="mb-20"
        :survey-result="surveyResult"
        :property-info="propertyInfo"
        :pre-report="form3.PreReport"
        :audit-feedback="auditFeedback"
        :building-table-info="ContentInfo.BuildingTableInfo"
        :project-result-info="ContentInfo.ProjectResultInfo"
        :name="FlowInfo.FlowName"
        :step="currentStep"
        :approval-opinion-table-info="ApprovalOpinionTableInfo"
        :flow-steps-info="FlowStepsInfo"
        :completion-land-area="ContentInfo.CompletionLandArea"
        :completion-building-area="ContentInfo.CompletionBuildingArea"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep == 3">
        <el-button
          v-if="developer && BaseInfo.StateCode === 0"
          :loading="prevBtn.loading"
          type="primary"
          @click="reSelectSurveyCompany()"
        >
          <i class="iconfont icon-back mr-5" />重新委托测绘单位
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="disabledBack()"
          @click="setReasonDialogVisible('退回业务', true)"
        >
          <i class="iconfont icon-back mr-5" />退回业务
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          type="success"
          :disabled="disabledAccept()"
          @click="submitAccept(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交验收
        </el-button>
      </template>
      <template
        v-if="currentStep === 4 && developer && BaseInfo.StateCode === 1"
      >
        <el-button
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('验收不通过', true)"
        >
          <i class="iconfont icon-back mr-5" />验收不通过
        </el-button>
        <el-button
          class="ml-10"
          :loading="acceptBtn.loading"
          type="success"
          @click="accept()"
        >
          <i class="el-icon-check mr-5" />验收完成
        </el-button>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2 || (currentStep === 1 && mappingCompany)"
        :base-info="BaseInfo"
        :content-info="ContentInfo"
        :attachment-data="form1"
        :step="currentStep"
        :step-list="stepList"
        :actions-infos="ActionsInfos"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
        @view-licence="viewLicence"
      />
    </template>
    <!-- 额外内容 -->
    <template slot="extra">
      <!-- 获取工程规划许可证 -->
      <!-- 工程规划许可证预览 -->
      <el-dialog
        title="查看工程规划许可证"
        :visible="licenceViewDialog.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
        width="1100px"
        @close="licenceViewDialog.visible = false"
      >
        <project-licence-view
          :row="licenceViewDialog.row"
          :index="licenceViewDialog.index"
        />
      </el-dialog>
      <!-- 获取工程规划许可证 -->
      <project-licence-get-dialog
        :visible.sync="licenceGetDialog.visible"
        :list="form1.ProjectPlanPermission"
        :business-id="businessID"
        :business-class="BaseInfo.BusinessClass"
        @add-lience="storeLicence(null, -1)"
        @submit="licenceGetSuccess"
        @close="setLicenceGetDialogVisible(false)"
      />
      <!-- 存储工程规划许可证 -->
      <project-licence-store-dialog
        :visible.sync="licenceStoreDialog.visible"
        :row="licenceStoreDialog.row"
        :index="licenceStoreDialog.index"
        :list="form1.ProjectPlanPermission"
        @submit="licenceStoreSuccess"
        @close="setLicenceStoreDialogVisible(false)"
      />
      <!-- 提交验收，选择注册测绘师弹窗 -->
      <select-registered-surveyor
        :visible.sync="selectRSurveyorDialog.visible"
        :business-id="BaseInfo.ID"
        :action-id="CurrentAction.ID"
        :flow-name="FlowInfo.FlowName"
        :completion-land-area="form3.CompletionLandArea"
        :completion-building-area="form3.CompletionBuildingArea"
        @select="getBusiness"
        @close="setSelectRSurveyorDialogVisible(false)"
      />
      <!-- 图例弹窗 -->
      <legend-dialog
        :title="legendDialog.title"
        :visible.sync="legendDialog.visible"
        :licence-type="legendDialog.legendType"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import TimingProgressBar from "components/business/TimingProgressBar/index.vue";
import ViewInfo from "./view-info/index.vue";
import AttachmentInfo from "./view-info/attachmentInfo.vue";
import Result from "./result.vue";
import LegendDialog from "components/business/legendDialog/index.vue";
// mixins
import BusinessMixin from "mixins/business/index.js";
import CheckLineMixin from "mixins/business/check-line.js";
import SurveyResultMixin from "mixins/business/survey-result.js";
import SelectMappingCompanyMixin from "mixins/business/select-mapping-company.js";
import SelectRegisteredSurveyorMixin from "mixins/business/select-registered-survey.js";
// 校验
import {
  validateBusinessName,
  validateAttachment,
  validateAutoGroundCode,
} from "utils/form.js";

export default {
  name: "PreMapping",
  components: {
    TimingProgressBar,
    ViewInfo,
    AttachmentInfo,
    Result,
    LegendDialog,
  },
  mixins: [
    BusinessMixin,
    CheckLineMixin,
    SurveyResultMixin,
    SelectMappingCompanyMixin,
    SelectRegisteredSurveyorMixin,
  ],
  data() {
    return {
      //初审意见和外业意见
      ApprovalOpinionTableInfo:[],
      //云平台步骤数据
      FlowStepsInfo:[],

      // 业务内容信息，不同业务内容不同
      ContentInfo: {
        ProjectPlanPermission: [],
      },
      // 步骤1
      form1: {
        BusinessName: null,
        GroundCode: null,
        ProjectPlanPermission: [],
        ProjectLicenceImg: [],
        ProjectLicenceImg: [],
        PropertyCertificate: [],
        BuildingLocationMap: [],
        SitePlan: [],
        BuildingDesgin: [],
        DoorPlate: [],
        ChangeDescription: [],
        AuthorizationLetter: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],
        GroundCode: [
          { required: true, validator: validateAutoGroundCode, trigger: "blur" },
        ],
        ProjectPlanPermission: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !this.form1.ProjectPlanPermission ||
                !this.form1.ProjectPlanPermission.length
              ) {
                callback(new Error("请添加工程规划许可证"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        ProjectLicenceImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ProjectLicenceImg",
                "工程规划许可证件"
              ),
            trigger: "change",
          },
        ],
        PropertyCertificate: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "PropertyCertificate",
                "不动产权证书"
              ),
            trigger: "change",
          },
        ],
        LandContract: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "LandContract",
                "土地出让合同/土地划拨决定书"
              ),
            trigger: "change",
          },
        ],
        BuildingDesgin: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "BuildingDesgin",
                "经备案的建筑设计图"
              ),
            trigger: "change",
          },
        ],
        ChangeDescription: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ChangeDescription",
                "变更说明"
              ),
            trigger: "change",
          },
        ],
        AuthorizationLetter: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "AuthorizationLetter",
                "授权委托书"
              ),
            trigger: "change",
          },
        ],
      },
      // 步骤3
      form3: {
        Data: [],
        PreReport: [],
        CompletionLandArea: '',
        CompletionBuildingArea: '',
      },
      rules3: {
        Data: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Data",
                "测绘成果"
              ),
            trigger: "change",
          },
        ],
        CompletionLandArea: [
          { 
            required: true, 
            message: '请输入竣工用地面积，填写的数值不能为0及负数且最多只能保留4位小数', 
            trigger: 'blur' 
          },
          { 
            pattern: /^(?!0$)(?!0\.0*$)([1-9]\d*|0)(\.\d{1,4})?$/, 
            message: '请输入大于0的数字，最多保留4位小数', 
            trigger: 'blur'
          }
        ],
        CompletionBuildingArea: [
          { 
            required: true, 
            message: '请输入竣工总建筑面积，填写的数值不能为0及负数且最多只能保留4位小数', 
            trigger: 'blur' 
          },
          { 
            pattern: /^(?!0$)(?!0\.0*$)([1-9]\d*|0)(\.\d{1,4})?$/, 
            message: '请输入大于0的数字，最多保留4位小数', 
            trigger: 'blur'
          }
        ],
        // PreReport: [
        //   {
        //     required: true,
        //     validator: (rule, value, callback) =>
        //       validateAttachment(
        //         rule,
        //         value,
        //         callback,
        //         this,
        //         "form3",
        //         "PreReport",
        //         "不动产预核报告"
        //       ),
        //     trigger: "change",
        //   },
        // ],
      },
      // 宗地号、工程规划许可证图例弹窗
      legendDialog: {
        visible: false,
        legendType: "宗地号",
        title: "查看图例",
      },
    };
  },
  destroyed() {
    this.clearTimer(this.resultTimer);
  },
  methods: {
    // 额外处理请求数据
    handleApiData(Data) {
      const { ProjectPlanPermission, BuildingTableInfo, ProjectResultInfo } =
        Data.ContentInfo;
      this.ContentInfo = {
        ...Data.ContentInfo,
        ProjectPlanPermission: ProjectPlanPermission
          ? JSON.parse(ProjectPlanPermission)
          : [],
        BuildingTableInfo: BuildingTableInfo
          ? JSON.parse(BuildingTableInfo)
          : [],
        ProjectResultInfo: ProjectResultInfo
          ? JSON.parse(ProjectResultInfo)
          : [],
      };
      this.ApprovalOpinionTableInfo = this.getApprovalOpinionTableInfo();
      this.FlowStepsInfo = this.getFlowStepsInfo();
    },
    // 处理表单数据
    handleFormData(currentStep) {
      const {
        BaseInfo,
        ContentInfo,
        Attachments,
        mappingCompany,
        getSurveyResultCheckState,
      } = this;
      const { BusinessName } = BaseInfo;
      let { DataCheckID, DataCheckState, GroundCode, ProjectPlanPermission } =
        ContentInfo;
      //第一步从扩展信息获取宗地号
      if (currentStep === 1) {
        GroundCode = this.ExtendInfo[0].ZDDM;
      }

      // 处理附件
      let ProjectLicenceImg = [];
      let PropertyCertificate = [];
      let LandContract = [];
      let BuildingLocationMap = [];
      let SitePlan = [];
      let WorkingDraw = [];
      let BuildingDesgin = [];
      let DoorPlate = [];
      let ChangeDescription = [];
      let AuthorizationLetter = [];
      let Others = [];
      let PreReport = [];
      this.auditFeedback = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "工程规划许可证件":
                ProjectLicenceImg.push(e);
                break;
              case "建筑定位图":
                BuildingLocationMap.push(e);
                break;
              case "不动产权证书":
                PropertyCertificate.push(e);
                break;
              case "土地出让合同/土地划拨决定书":
                LandContract.push(e);
                break;
              case "总平面图":
                SitePlan.push(e);
                break;
              case "全套建筑施工图":
                WorkingDraw.push(e);
                break;
              case "经备案的建筑设计图":
                BuildingDesgin.push(e);
                break;
              case "门牌证明（变更门牌时需上传）":
                DoorPlate.push(e);
                break;
              case "变更说明":
                ChangeDescription.push(e);
                break;
              case "授权委托书":
                AuthorizationLetter.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "项目成果附件") {
            switch (e.AttachmentCategories) {
              case "不动产预核报告":
                PreReport.push(e);
                break;
              case "不动产测绘成果":
                this.surveyResult = e;
                break;
              case "楼盘信息表":
                this.propertyInfo = e;
                break;
              case "不动产预核测绘成果审核反馈":
                this.auditFeedback.push(e);
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
        });
      }

      this.form1 = {
        BusinessName,
        GroundCode,
        ProjectPlanPermission,
        ProjectLicenceImg,
        PropertyCertificate,
        LandContract,
        BuildingLocationMap,
        SitePlan,
        WorkingDraw,
        BuildingDesgin,
        DoorPlate,
        ChangeDescription,
        AuthorizationLetter,
        Others,
      };

      this.form3 = {
        // 判断Data是否有值，DataCheckState： 0 检查中  1 检查通过  2 检查不通过
        Data:
          DataCheckID && DataCheckState > 0
            ? [{ name: "hasResult", status: "success" }]
            : [],
        CompletionLandArea: this.form3.CompletionLandArea,
        CompletionBuildingArea: this.form3.CompletionBuildingArea,
        PreReport,
      };

      // 审核中定时请求接口
      if (currentStep === 3 && mappingCompany) {
        if (DataCheckID && DataCheckState === 0) {
          getSurveyResultCheckState(DataCheckID);
        }
      }
    },
    // 获取工程规划许可证
    // getLicence() {
    //   this.licenceGetDialog = {
    //     visible: true,
    //     type: this.form1.ProjectPlanPermission.length > 0 ? 1 : 0,
    //   };
    // },
    // 工程规划许可证获取成功
    // licenceGetSuccess(params) {
    //   this.form1.ProjectPlanPermission[0] = params;
    //   this.$refs.form1.clearValidate("ProjectPlanPermission");
    // },
    // 工程规划许可证存储成功
    // licenceStoreSuccess(params, index) {
    //   this.form1.ProjectPlanPermission[0] = params;
    //   this.$refs.form1.clearValidate("ProjectPlanPermission");
    // },
    // 显示成果数据
    showResult() {
      const { currentStep, mappingCompany, ContentInfo } = this;

      if (
        currentStep === 3 &&
        mappingCompany &&
        ContentInfo.DataCheckID &&
        ContentInfo.DataCheckState === 1
      ) {
        return true;
      }
      if (currentStep === 4 || currentStep === 5) {
        return true;
      }

      return false;
    },
    // 禁用退回、提交验收和确认通过（测试按钮）
    disabledBack() {
      const { DataCheckID, DataCheckState } = this.ContentInfo;

      // 正在在检查成果
      if (DataCheckID && DataCheckState === 0) {
        return true;
      }
      return false;
    },
    //单独禁用验收按钮
    disabledAccept() {
      const { DataCheckID, DataCheckState } = this.ContentInfo;
      // 正在在检查成果
      if (DataCheckID && DataCheckState === 0) {
        return true;
      }
      if (DataCheckID && DataCheckState === 2) {
        return true;
      }
      return false;
    },
    //查看图例
    viewLegend(name) {
      this.legendDialog.visible = true;
      this.legendDialog.legendType = name;
      this.legendDialog.title = `查看【${name}】图例`;
    },
    //初审意见和外业意见
    getApprovalOpinionTableInfo() {
      const { CurrentAction } = this;
      if (
        CurrentAction != undefined &&
        CurrentAction.ExtendAttr !== null &&
        CurrentAction.ExtendAttr !== "" &&
        CurrentAction.ExtendAttr !== undefined
      ) {
        if (
          CurrentAction.ExtendAttr.XCloudSPYJ !== null &&
          CurrentAction.ExtendAttr.XCloudSPYJ !== "" &&
          CurrentAction.ExtendAttr.XCloudSPYJ !== undefined
        ) {
          if (CurrentAction.ExtendAttr.XCloudSPYJ.Message == '') {            
            return CurrentAction.ExtendAttr.XCloudSPYJ.Data;
          }
        }
      }

      return [];
    },
    //获取云平台流程步骤
    getFlowStepsInfo() {
      const { CurrentAction } = this;
      if (
        CurrentAction != undefined &&
        CurrentAction.ExtendAttr !== null &&
        CurrentAction.ExtendAttr !== "" &&
        CurrentAction.ExtendAttr !== undefined
      ) {
        if (
          CurrentAction.ExtendAttr.XCloudSteps !== null &&
          CurrentAction.ExtendAttr.XCloudSteps !== "" &&
          CurrentAction.ExtendAttr.XCloudSteps !== undefined
        ) {
          if (CurrentAction.ExtendAttr.XCloudSteps.Message == '') {            
            return CurrentAction.ExtendAttr.XCloudSteps.Data;
          }
        }
      }

      return [];
    }
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
.el-icon-question {
  color: $color-primary;
  cursor: pointer;
  padding-left: 2px;
}
.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #E6A23C;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 5px;
  }
}
</style>
