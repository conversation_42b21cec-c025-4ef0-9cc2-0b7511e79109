/* eslint-disable */
import router from './router'
import store from './store'
import { Message } from 'element-ui'
import { getPageTitle } from 'utils'

const whiteList = ['/home', '/about', '/contact', '/reset-password', '/maintain', '/404', '/test'] // 不需要登录的页面

router.beforeEach(async (to, from, next) => {

  // 页面标题
  document.title = getPageTitle(to.meta.title)

  const hasRoles = store.getters.roles && store.getters.roles.length > 0

  if (hasRoles) {
    next();
  }
  // 刷新页面或者首次打开系统
  else {
    try {
      // 获取用户信息，判断用户是否已在邕e登登录
      const userInfo = await store.dispatch('user/getInfo');
      // 用户未登录，跳转登录页
      if (!userInfo) {
        if (whiteList.indexOf(to.path) !== -1) {
          store.dispatch("app/setSysLoading", false);
          next()
        }
        // 重定向到home进行登录
        else {
          store.dispatch('user/logout');
          next('/home');
        }

      }
      else {
        // 获取可通过的路由
        await store.dispatch('permission/generateRoutes', userInfo);

        // 取消系统加载
        store.dispatch("app/setSysLoading", false);

        // hack方法以确保addroutes是完整的
        // 设置replace:true，这样导航就不会留下历史记录
        next({ ...to, replace: true })
      }
    } catch (err) {
      // 重登录
      Message.error(err || '用户信息已失效，请重新登录')
      setTimeout(async() => {
        await store.dispatch('user/logout')
        next('/home');
      }, 500)
    }
  }
})

router.afterEach(() => {
  
})
