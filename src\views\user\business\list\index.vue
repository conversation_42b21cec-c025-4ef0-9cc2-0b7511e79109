<template>
  <div class="business-list-container">
    <ul v-if="businessList.length" class="business-list">
      <li
        v-for="(item, i) in businessList"
        :key="'business-item' + i"
        class="business-list-item"
      >
        <div class="business-list-item__wrapper">
          <div class="business-list-item__title">
            <i :class="`iconfont icon-${item.icon} mr-5`" />
            <span>{{ item.label }}</span>
          </div>
          <div class="business-list-item__content">
            <template v-if="item.children.length">
              <div
                v-for="(business, j) in item.children"
                :key="'business' + j"
                class="business flex"
              >
                <div
                  class="business__title flex"
                  :class="{ disabled: !business.enable }"
                  @click="checkPermission(business)"
                >
                  <i class="iconfont icon-jiantou mr-5" />
                  {{ business.label }}
                  <template
                    v-if="!business.enable"
                  >(即将上线，敬请期待...)</template>
                </div>
                <div class="business__btn ml-10">
                  <el-button
                    type="default"
                    @click="showGuide(business)"
                  >业务指南</el-button>
                  <el-button
                    type="primary"
                    :disabled="!business.enable"
                    @click="checkPermission(business)"
                  >立即办理</el-button>
                </div>
              </div>
            </template>
            <empty v-else />
          </div>
        </div>
      </li>
    </ul>
    <empty v-else />
    <create-business
      :visible.sync="creatBusinessDialog.visible"
      :business="creatBusinessDialog.business"
      :select-list="creatBusinessDialog.selectList"
      @submit="creatBusinessSuccess"
    />
    <get-building
      :visible.sync="getBuildingDialog.visible"
      :business="getBuildingDialog.business"
      @submit="getBuildingSuccess"
    />
    <select-building
      :visible.sync="selectBuildingDialog.visible"
      :business="selectBuildingDialog.business"
      :building-list="selectBuildingDialog.buildingList"
      @submit="selectBuildingSuccess"
    />
    <business-guide
      :visible.sync="businessGuideDialog.visible"
      :business="businessGuideDialog.business"
    />
    <!-- 公告弹窗 -->
    <count-down-dialog
      :visible.sync="countDownDialog.visible"
      :title="countDownDialog.title"
      :count-down-type="countDownDialog.countDownType"
      :count-down-btn="countDownDialog.countDownBtn"
      :count-down-time="countDownDialog.countDownTime"
      :business-class="countDownDialog.businessClass"
      :width="countDownDialog.width"
      :class-name="countDownDialog.className"
      :is-promise="countDownDialog.isPromise"
      @submit="onClickRead"
    />
    <!-- 停止新建不动产预核业务的通知 -->
    <el-dialog
      title="停止新建不动产预核业务的通知"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      custom-class="stop-notice"
      width="500px"
    >
      <span
        style="
          text-indent: 2em;
          display: inline-block;
          font-size: 16px;
          line-height: 26px;
        "
      >从2021年6月7日零时起，不动产预核业务不能再新建业务，于2021年6月7日零时前已经提交的业务“我的业务”继续办理，如有疑问详询南宁市不动产登记中心测绘管理部
        0771-4306662</span>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="dialogVisible = false"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
import CreateBusiness from "./createBusiness";
import GetBuilding from "./getBuilding";
import SelectBuilding from "./selectBuilding";
import BusinessGuide from "./guide/index";
import CountDownDialog from "components/business/CountDownDialog/index.vue";
// vuex
import { mapGetters } from "vuex";
// Api
import Api from "api/business/index.js";

export default {
  name: "BusinessList",
  components: {
    CreateBusiness,
    GetBuilding,
    SelectBuilding,
    BusinessGuide,
    CountDownDialog,
  },
  computed: {
    ...mapGetters([
      "isRealName",
      "companyStateCode",
      "pageLoading",
      "currentEnv",
      "companyType",
      "roles",
    ]),
  },
  data() {
    return {
      baseDataList: [],
      businessList: [],
      iconList: [
        {
          icon: "dataitem",
          label: "测绘数据申请",
        },
        // {
        //   icon: "ditu",
        //   label: "勘测定界"
        // },
        {
          icon: "area",
          label: "项目立项阶段 - 规划定点及不动产权籍调查",
        },
        {
          icon: "xiantuceng",
          label: "项目许可阶段 - 规划验线",
        },
        {
          icon: "shenhexiangmu",
          label: "项目许可阶段 - 规划验线",
        },
      ],
      loading: false,
      // 创建业务弹窗
      creatBusinessDialog: {
        visible: false,
        business: {
          businessClass: null,
        },
      },
      // 业务指南弹窗
      businessGuideDialog: {
        visible: false,
        business: null,
      },
      // 获取楼栋弹窗
      getBuildingDialog: {
        visible: false,
        business: null,
        buildingList: [],
      },
      // 选择楼栋弹窗
      selectBuildingDialog: {
        visible: false,
        business: null,
      },
      // 公告弹窗
      countDownDialog: {
        visible: false,
        businessClass: null,
        title:
          "关于南宁市不动产登记中心综合服务平台开通不动产预核业务即时办结功能的公告",
        countDownType: "公告",
        countDownBtn: "阅知",
        countDownTime: 10,
        width: "1100px",
        className: "notice",
        isPromise: false,
      },
      isRead: false,
      createAutoBussinessInfo: null,
      dialogVisible: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.setPageLoading(true);
      Api.GetBusinessList()
        .then((res) => {
          const { StateCode, Data, Message } = res;
          if (StateCode === 1) {
            this.listFormat(Data);
          } else {
            this.$message.error(Message);
          }
          this.setPageLoading(false);
        })
        .catch((err) => {
          console.log(err);
          this.setPageLoading(false);
        });
    },
    // 页面加载
    setPageLoading(val) {
      this.$store.dispatch("app/setPageLoading", val);
    },
    // 列表格式化
    listFormat(list) {
      if (list.length < 2) return;
      this.businessList = [];
      const { currentEnv } = this;
      list.forEach((e, index) => {
        const iconObj = this.iconList.find((i) => i.label === e.label);
        e.icon = e.icon ? e.icon : iconObj ? iconObj.icon : "dataitem";

        /**
         * 2021-03-02 新需求修改
         * visible为true的业务可见
         */
        e.children = e.children.filter((c) => c.visible === true);
        /* 2021-03-02 新需求修改 */

        this.businessList.push(e);
      });
    },
    // 权限判断
    checkPermission(e) {
      this.createAutoBussinessInfo = e;
      if (!e.enable) {
        return false;
      }

      const {
        isRealName,
        roles,
        companyStateCode,
        $alert,
        creatBusiness,
      } = this;

      const { businessClass } = e;
      console.log(businessClass);
      if (!isRealName) {
        $alert(
          "您还未进行实名认证，请扫描下方“邕e登APP”二维码安装App进行实名认证。"
        ).catch((err) => console.log(err));
        return false;
      }
      // 实名制的用户即可办理“基础数据下载”业务
      if (businessClass === "BaseSurveyDataDownloadFlow") {
        creatBusiness(e);
        return true;
      }

      // 建设单位
      if (roles.indexOf("c-d") >= 0) {
        if (companyStateCode === 0) {
          $alert("检测到您未绑定任何单位，无法办理该业务").catch((err) =>
            console.log(err)
          );
          return false;
        }
        if (companyStateCode !== 3) {
          $alert("单位信息审核通过后才能办理该业务").catch((err) =>
            console.log(err)
          );
          return false;
        }
      }
      console.log(businessClass);
      //不动产预核业务(即时办结)
      if (businessClass === "RealEstatePreCheckSurveyAutoFlow") {
        this.countDownDialog.businessClass = "RealEstatePreCheckSurveyAutoFlow";
        this.countDownDialog.visible = true;
        return false;
      }
      //不动产预核业务
      if (businessClass === "RealEstatePreCheckSurveyFlow") {
        this.dialogVisible = true;
        return false;
      }

      creatBusiness(e);
    },
    // 创建业务
    creatBusiness(business) {
      const { businessClass } = business;
      Api.CheckPermission(businessClass)
        .then((res) => {
          const { StateCode, Data, Message } = res;
          if (StateCode === 1) {
            if (Data) {
              // 楼盘表、成果变更
              if (
                businessClass ===
                  "RealEstatePreSurveyBuildingTableChangeFlow" ||
                businessClass === "RealEstatePreSurveyResultChangeFlow" ||
                businessClass === "RealEstateActualBuildingTableChangeFlow" ||
                businessClass === "RealEstateActualResultChangeFlow"
              ) {
                this.getBuildingDialog = {
                  visible: true,
                  business,
                };
                return false;
              }
              // 创建业务
              else {
                this.creatBusinessDialog = {
                  visible: true,
                  business,
                };
              }
            } else this.$message.error(Message);
          } else {
            this.$message.error(Message);
          }
        })
        .catch((err) => {
          console.log(err);
          return false;
        });
    },
    // 创建业务成功
    creatBusinessSuccess(e, id) {
      console.log(e.url);
      this.$router.push({ path: e.url, query: { id } });
    },
    // 获取楼栋成功
    getBuildingSuccess(business, Data) {
      this.selectBuildingDialog = {
        visible: true,
        business,
        buildingList: Data,
      };
    },
    // 选择楼栋成功
    selectBuildingSuccess(business, selectList) {
      this.creatBusinessDialog = {
        visible: true,
        business,
        selectList,
      };
    },
    // 查看业务指南
    showGuide(business) {
      this.businessGuideDialog = {
        visible: true,
        business,
      };
    },
    //点击阅知
    onClickRead(isRead) {
      this.isRead = isRead;
      if (this.isRead) {
        this.creatBusiness(this.createAutoBussinessInfo);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.business-list {
  display: flex;
  flex-wrap: wrap;

  &-container {
    padding-bottom: 20px;
  }
  &-item {
    width: 50%;
    padding: 10px;

    &__wrapper {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      height: 100%;
    }

    &__title {
      background: #f5f7f7;
      padding: 10px;
      border-radius: 4px;
      font-size: 16px;
      .iconfont {
        font-size: 18px;
      }
    }

    &__content {
      margin-top: 15px;

      .business {
        color: $color-primary;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        cursor: pointer;

        &__title {
          align-items: center;
          &:hover {
            color: #09f;
          }

          &.disabled {
            color: #888;

            &:hover {
              color: #888;
            }
          }
        }
      }
    }
  }
}
/deep/ .el-message-box .el-message-box__message p {
  text-indent: 2em;
}

.base-data {
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin: 10px;

  &__title {
    // color: $color-primary;
    background: #f5f7f7;
    border-radius: 4px;
    width: 100%;
    padding: 10px;
    font-size: 16px;
    cursor: pointer;
    align-items: center;
    &:hover {
      color: #09f;
    }

    &.disabled {
      color: #888;

      &:hover {
        color: #888;
      }
    }

    .iconfont {
      font-size: 18px;
    }
  }

  &__btn {
    // min-width: 210px;
  }
}
/deep/ .stop-notice .el-dialog__body {
  padding: 10px 20px;
}
@media screen and (max-width: 1024px) {
  .business-list-item {
    width: 100%;
  }
}
</style>
