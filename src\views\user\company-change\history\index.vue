<template>
  <!-- eslint-disable -->
  <div>
    <el-card shadow="never" class="overspread-card">
      <!-- 搜索和提示 -->
      <div class="button-container mb-20">
        <el-button type="default" @click="$router.push({ name: 'CompanyInfo' })">返回单位信息</el-button>
      </div>
      <!-- 表格 -->
      <dynamic-table
        v-loading="listLoading"
        element-loading-text="加载中，请稍等..."
        :table-header="tableHeader"
        :table-data="listData"
        :default-props="tableProps"
        :showPagination="true"
        :total="page.total"
        :page-no.sync="page.pageNo"
        :page-size.sync="page.pageSize"
        :page-sizes.sync="page.pageSizes"
        @pagination="getList"
      >
        <el-table-column width="50" label="序号" align="center">
          <template slot-scope="{ $index }">{{ ($index + 1) + page.pageSize * (page.pageNo - 1) }}</template>
        </el-table-column>
        <el-table-column prop="TypeName" label="变更类型" align="center"></el-table-column>
        <el-table-column prop="CreateUserName" label="变更申请人" align="center"></el-table-column>
        <el-table-column prop="CreateUserNo" label="申请人身份证号" width="250" align="center"></el-table-column>
        <el-table-column prop="CreateUserPhone" label="申请人联系电话" width="200" align="center"></el-table-column>
        <el-table-column label="审核状态" width="110" fixed="right" align="center">
          <template slot-scope="{ row }">
            <el-tag v-if="row.StateCode === 1" class="tag-yellow" effect="dark">已提交</el-tag>
            <el-tag v-if="row.StateCode === 2" class="tag-blue" effect="dark">审核中</el-tag>
            <el-tag v-if="row.StateCode === 3" type="success" effect="dark">已通过</el-tag>
            <el-tag v-if="row.StateCode === 4" type="error" effect="dark">审核不通过</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="listData.length"
          prop="action"
          label="操作"
          width="100"
          fixed="right"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-button type="text" icon="el-icon-document" @click="showDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </dynamic-table>
    </el-card>
    <detail-dialog :visible.sync="detailDialog.visible" :row="detailDialog.row" />
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import DynamicTable from "components/common/Table/DynamicTable";
import DetailDialog from "./detail/index.vue";
// mixins
import Page from "mixins/page.js";
import Table from "mixins/table.js";
// Api
import Api from "api/company-change/index.js";

export default {
  name: "CompanyChangeHistoryDialog",
  components: { DynamicTable, DetailDialog },
  mixins: [Page, Table],
  computed: {
    // 获取列表接口
    apiGetList(PageIndex, PageSize) {
      return (PageIndex, PageSize) =>
        Api.SurveyCompanyInfoModifyList(PageIndex, PageSize);
    }
  },
  data() {
    return {
      tableHeader: [],
      detailDialog: {
        visible: false,
        row: {
          ID: null
        }
      }
    };
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.getList(1, this.page.pageSize);
    },
    // 查看详情
    showDetail(row) {
      this.detailDialog = {
        visible: true,
        row
      };
    }
  }
};
</script>

<style lang="scss" scoped>
</style>