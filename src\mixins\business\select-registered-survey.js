/*
 * 模块 : 选择注册测绘师确认验收等相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-11-13
 * 版本 : version 1.0
 */
/* eslint-disable */
// 组件
import SelectRegisteredSurveyor from "components/business/SelectRegisteredSurveyor/index.vue";

export default {
  components: {
    SelectRegisteredSurveyor
  },
  data() {
    return {
      // 选择注册测绘师弹窗配置
      selectRSurveyorDialog: {
        visible: false
      },
    };
  },
  methods: {
    // 提交验收
    async submitAccept(step) {
      this.GLOBAL.logInfo(`提交第${step}步的内容并校验`);
      // 需要验证竣工面积的业务类型
      const needValidateArea = [
        'RealEstateActualSurveyFlow',
        'RealEstateOverallActualSurveyFlow',
        'RealEstatePreSurveyResultChangeFlow',
        'RealEstateActualResultChangeFlow',
        'MeasurePutLinePreCheckFlow',
        'CouncilPlanCheckFlow'
      ];
      // 只有特定业务类型才需要验证竣工面积
      if (needValidateArea.includes(this.businessClass)) {
        // 验证竣工用地面积和竣工总建筑面积
        if (!this.form3.CompletionLandArea || !this.form3.CompletionBuildingArea) {
          this.$message.error('请填写竣工用地面积和竣工总建筑面积');
          return false;
        }
        // 验证数值格式
        const numberPattern = /^(?!0$)(?!0\.0*$)([1-9]\d*|0)(\.\d{1,4})?$/;
        if (!numberPattern.test(this.form3.CompletionLandArea)) {
          this.$message.error('竣工用地面积必须为大于0的数字，最多保留4位小数');
          return false;
        }
        if (!numberPattern.test(this.form3.CompletionBuildingArea)) {
          this.$message.error('竣工总建筑面积必须为大于0的数字，最多保留4位小数');
          return false;
        }
      }
      const valid = await this.checkStepForm(step);
      if (!valid) return false;

      this.setSelectRSurveyorDialogVisible(true);
    },
    // 选择注册测绘师弹窗可见性
    setSelectRSurveyorDialogVisible(val){
      this.selectRSurveyorDialog.visible = val;
    }
  },
};
