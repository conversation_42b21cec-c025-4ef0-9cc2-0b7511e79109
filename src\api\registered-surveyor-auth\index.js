
/* eslint-disable */
import request from 'utils/request';
import qs from "qs";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API

export default {
    // 根据单位ID获取单位注册测绘师列表
	GetSurveyMasterList: (PageIndex, PageSize, q) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetSurveyMasterWithAuthInfoList?PageIndex=${PageIndex}&PageSize=${PageSize}&q=${q}`,
			method: 'get'
		})
    },
    // 提交授权申请/修改授权申请信息
	SaveSurveyMasterAuth: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/SaveSurveyMasterAuth`,
			method: 'post',
			data,
		})
    },
    // 撤销授权申请（测绘师授权前撤销）
	CancelSurveyMasterAuth: (AuthId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/CancelSurveyMasterAuth?id=${AuthId}`,
			method: 'post'
		})
    },
    // 关闭授权申请（测绘师授权后关闭）
	CloseSurveyMasterAuth: (AuthId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/CloseSurveyMasterAuth?id=${AuthId}`,
			method: 'post'
		})
    },
    // 根据业务类型筛选已授权的注册测绘师列表
	GetAuthorizedSurveyMasters: (businessId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetAuthorizedSurveyMasters?id=${businessId}`,
			method: 'get'
		})
    },
    // 根据业务类型筛选已授权的注册测绘师列表
	ConfirmSurveyResultUsingSurveyMasterAuth: (businessId, actionId, authiId, completionLandArea, completionBuildingArea) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/ConfirmSurveyResultUsingSurveyMasterAuth?id=${businessId}&actionId=${actionId}&authid=${authiId}&completionLandArea=${completionLandArea}&completionBuildingArea=${completionBuildingArea}`,
			method: 'post'
		})
	},
}
