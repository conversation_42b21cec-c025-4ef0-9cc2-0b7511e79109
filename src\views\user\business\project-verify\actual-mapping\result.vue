<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="flowStepsInfo && flowStepsInfo.length">
      <div class="result-title mt-0">
        <span>【{{ name }}】审批进度</span>
      </div>
      <flow-steps-info
          :flow-steps-info="flowStepsInfo"
        />
    </template>
    <template v-if="approvalOpinionTableInfo && approvalOpinionTableInfo.length">
      <div class="result-title"
      :class="{ 'mt-0': (!flowStepsInfo || flowStepsInfo.length == 0) }"
      >
        <span>【{{ name }}】审批意见</span>
      </div>
      <approval-opinion-table-info
          :approval-opinion-table-info="approvalOpinionTableInfo"
          :businessClass="businessClass"
        />
    </template>
    <template v-if="completionLandArea || completionBuildingArea">
      <div class="result-title"
      :class="{ 'mt-0': (!flowStepsInfo || flowStepsInfo.length == 0) && (!approvalOpinionTableInfo || approvalOpinionTableInfo.length == 0) }"
      >
        <span>【{{ name }}】竣工面积信息</span>
      </div>
        <el-form>
         <el-row>
            <el-col :span="12">
             <el-form-item label="竣工用地面积（该地块用地面积）：" class="completion-area-item">{{ completionLandArea | isNull }}㎡</el-form-item>
            </el-col>
            <el-col :span="12">
             <el-form-item label="竣工总建筑面积（单体总建筑面积）：" class="completion-area-item">{{ completionBuildingArea | isNull }}㎡</el-form-item>
            </el-col>
         </el-row>
        </el-form>
    </template>
    <template v-if="surveyResult">
      <div class="result-title"
      :class="{ 'mt-0': (!flowStepsInfo || flowStepsInfo.length == 0) && (!approvalOpinionTableInfo || approvalOpinionTableInfo.length == 0) }"
      >
        <span>【{{ name }}】测绘成果</span>
      </div>
      <div class="result-file flex">
        <div>
          {{ surveyResult.AttachmentName + surveyResult.AttachmentExt }}
        </div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(surveyResult)"
          >下载</el-button
        >
      </div>
    </template>
    <template v-if="buildingTableInfo && buildingTableInfo.length">
      <div class="result-title">
        <span>【{{ name }}】测绘成果信息</span>
      </div>
      <building-table-info
        :building-table-info="buildingTableInfo"
        :property-info="propertyInfo"
        :condition-verificate-info="conditionVerificateInfo"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- <template v-if="step >= 4"> -->
      <template v-if="siteOutFacadeImg && siteOutFacadeImg.length && step >= 4">
        <div class="result-title">
          <span>【{{ name }}】现场外立面照片</span>
        </div>
        <file-list
          :file-list="siteOutFacadeImg"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </template>
      <template v-if="verAndMap && verAndMap.length && step >= 4">
        <div class="result-title">
          <span>【{{ name }}】规划条件核实测绘成果</span>
        </div>
        <file-list
          :file-list="verAndMap"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
  </template>
      <template v-if="actualReport && actualReport.length && step >= 4">
        <div class="result-title">
          <span>【{{ name }}】不动产实核报告</span>
        </div>
        <file-list
          :file-list="actualReport"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </template>
    <!-- </template> -->
    <template v-if="auditFeedback && auditFeedback.length">
      <div class="result-title mt-10">
        <span>【{{ name }}】成果备案反馈</span>
      </div>
      <div
        v-for="(item, index) in auditFeedback"
        :key="'auditFeedback' + index"
        class="result-file flex mb-15"
      >
        <div>{{ item.AttachmentName + item.AttachmentExt }}</div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(item)"
          >下载</el-button
        >
      </div>
    </template>
  </div>
</template>

<script>
/* eslint-disable */
import BuildingTableInfo from "components/business/BuildingTableInfo/index.vue";
import ApprovalOpinionTableInfo from "components/business/ApprovalOpinionTableInfo/index.vue";
import FlowStepsInfo from "components/business/FlowStepsInfo/index.vue";
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";
import BusinessResult from "mixins/business/result.js";

export default {
  name: "PreMappingResult",
  mixins: [AttachmentDownload, BusinessResult],
  components: { BuildingTableInfo, ApprovalOpinionTableInfo, FlowStepsInfo },
  props: {
    // 楼盘信息表pdf
    propertyInfo: {
      type: Object,
      default: null,
    },
    // 竣工规划条件核实信息表pdf
    conditionVerificateInfo: {
      type: Object,
      default: null,
    },
    // 现场外立面照片
    siteOutFacadeImg: {
      type: Array,
      default: null,
    },
    //规划条件核实测绘成果
    verAndMap:{
      type:Array,
      default:null,
    },
    // 不动产实核报告
    actualReport: {
      type: Array,
      default: null,
    },
    // 成果反馈附件
    auditFeedback: {
      type: Array,
      default: null,
    },
    // 楼盘信息
    buildingTableInfo: {
      type: Array,
      default: null,
    },
    // 工规许可证信息
    projectResultInfo: {
      type: Array,
      default: null,
    },
    //初审意见和外业意见
    approvalOpinionTableInfo: {
      type: Array,
      default: null,
    },
    //云平台流程步骤
    flowStepsInfo: {
      type: Array,
      default: null,
    },     
    //流程类型
    businessClass: {
      type: String,
      default: null,
    },
    // 竣工土地面积
    completionLandArea: {
      type: String,
      default: null,
    },
    // 竣工建筑面积
    completionBuildingArea: {
      type: String,
      default: null,
    },
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";

.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  .inside {
    border-top: none;
    border-left: none;

    tr:last-child {
      th,
      td {
        border-bottom: none;
      }
    }
  }

  th {
    background: #f8f8f8;
    color: #909399;
    min-width: 130px;
    width: 130px;
    max-width: 130px;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    min-width: 130px;
  }
}

.operate-btn {
  justify-content: center;
  // border-bottom: 1px solid #dfe6ec;
  padding: 20px 0;
}
.completion-area-item {
  padding-left: 85px;
}
</style>
