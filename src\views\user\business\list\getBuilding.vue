<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="650px"
      class="get-building-dialog"
      @close="close"
      p
    >
      <template v-if="business">
        <tip type="default" class="mb-20">
          <p v-if="showBuildingChangeTips" class="mt-0">
            不动产楼盘表维护仅针对变更项目信息及房屋属性信息的变更。
          </p>
          <p v-if="showResultChangeTips" class="mt-0">
            不动产测绘成果变更针对的是涉及到面积变化的变更，包括设计调整、计算错误等原因需要重新出具测绘成果。
          </p>
          <p class="mb-0">
            请输入<a class="link" @click="viewLegend('不动产权证')"
              >不动产单元号（宗地号）</a
            >获取楼栋信息，选择相应楼栋后进行变更操作。
          </p>
        </tip>
      </template>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="0"
        @submit.native.prevent
      >
        <el-form-item label="" prop="GroundCode">
          <el-input
            v-model="form.GroundCode"
            placeholder="宗地号为7位或者19位，如：450103001001GB00001"
            @keyup.enter.native="submitForm('form')"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm('form')"
          :loading="saveLoading"
          >获取楼栋</el-button
        >
      </div>
    </el-dialog>
    <!-- 图例弹窗 -->
    <legend-dialog
      :title="legendDialog.title"
      :visible.sync="legendDialog.visible"
      :licence-type="legendDialog.legendType"
    />
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/business/index.js";
// 校验
import { validateAutoGroundCode } from "utils/form.js";
import LegendDialog from "components/business/legendDialog/index.vue";
// vuex
import { mapGetters } from "vuex";

export default {
  components: {
    LegendDialog,
  },
  name: "GetBuilding",
  computed: {
    ...mapGetters(["userName", "personName"]),
    // 显示楼盘信息表变更提示
    showBuildingChangeTips() {
      const { businessClass } = this.business;
      return (
        businessClass === "RealEstatePreSurveyBuildingTableChangeFlow" ||
        businessClass === "RealEstateActualBuildingTableChangeFlow"
      );
    },
    // 显示成果变更提示
    showResultChangeTips() {
      const { businessClass } = this.business;
      return (
        businessClass === "RealEstatePreSurveyResultChangeFlow" ||
        businessClass === "RealEstateActualResultChangeFlow"
      );
    },
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前业务
    business: {
      type: Object,
      default: () => ({
        GroundCode: null,
      }),
    },
  },
  data() {
    return {
      title: "创建业务",
      // loading
      saveLoading: false,
      // 表单
      form: {
        GroundCode: null,
      },
      // 规则
      rules: {
        GroundCode: [
          { required: true, validator: validateAutoGroundCode, trigger: "blur" },
        ],
      },
      // 宗地号、工程规划许可证图例弹窗
      legendDialog: {
        visible: false,
        legendType: "宗地号",
        title: "查看图例",
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });

      const { business } = this;
      this.title = `【${business.label}】获取楼栋`;
    },
    // 关闭存储弹窗
    close() {
      this.form.GroundCode = null;
      this.saveLoading = false;
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { business, form } = this;
          let chzt;
          const businessClass = business.businessClass;
          this.saveLoading = true;
          // 成功回传父组件
          /* DEMO-start */
          // this.$emit("submit", business, []);
          // this.close();
          /* DEMO-end */
          // Api.CreateNewBusiness(params)
          //   .then((res) => {
          //     const { StateCode, Data, Message } = res;
          //     if (StateCode == 1) {
          //       // 成功回传父组件
          //       this.$emit("submit", business, Data);
          //       this.close();
          //     } else {
          //       this.$message.error(Message);
          //     }
          //     this.saveLoading = false;
          //   })
          //   .catch((err) => {
          //     console.log(err);
          //     this.saveLoading = false;
          //   });

          // console.log(businessClass)
          if (
            businessClass === "RealEstatePreSurveyBuildingTableChangeFlow" ||
            businessClass === "RealEstatePreSurveyResultChangeFlow"
          ) {
            chzt = 1; //预测绘
          }
          if (
            businessClass === "RealEstateActualBuildingTableChangeFlow" ||
            businessClass === "RealEstateActualResultChangeFlow"
          ) {
            chzt = 2; //实测绘
          }
          Api.GetBuildingList(form.GroundCode, chzt)
            .then((res) => {
              const { StateCode, Data, Message } = res;
              if (StateCode == 1) {
                // 成功回传父组件
                this.$emit("submit", business, Data);
                this.close();
              } else {
                this.$message.error(Message);
              }
              this.saveLoading = false;
            })
            .catch((err) => {
              console.log(err);
              this.saveLoading = false;
            });
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
    //查看图例
    viewLegend(name) {
      this.legendDialog.visible = true;
      this.legendDialog.legendType = name;
      this.legendDialog.title = `查看【${name}】图例`;
    },
  },
};
</script>

<style lang="scss" scoped>
.get-building-dialog {
  /deep/ .el-dialog__body {
    padding-bottom: 5px;
  }
  .link {
    color: $color-primary;
    text-decoration: underline;
    cursor: pointer;
    &:hover {
      color: #09f;
    }
  }
}
</style>
