<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="580px"
    @close="close"
    class="get-data-dialog-container"
  >
    <template v-if="!getData">
      <tip type="success" class="mb-20" v-if="codeTip.visible && codeTip.text">{{ codeTip.text }}</tip>
      <tip
        type="default"
        class="mb-20"
        v-else
      >温馨提示：
        <ol class="tip-list">
          <li class="tip-list__item">
            点击下方的“获取提取码”按钮，系统将会把提取码发送至您的手机号【{{ userName }}】
          </li>
          <li class="tip-list__item">
            同一个提取码短信，1分钟内只发送一次，请注意保存
          </li>
          <li class="tip-list__item">
            该提取码可以提取本业务的全部基础数据，无需重复获取
          </li>
        </ol>
      </tip>
      <el-form ref="form" :model="form" :rules="rules" label-width="70px" @submit.native.prevent>
        <el-form-item label="提取码" prop="code">
          <div class="code-input flex">
            <el-input
              v-model.trim="form.code"
              placeholder="请输入提取码（英文字母区分大小写）"
              @keyup.enter.native="submitForm('form')"
            ></el-input>
          </div>
        </el-form-item>
      </el-form>
      <div style="text-align:center">
        <el-button type="primary" @click="getCode()" class="ml-10" :loading="getCodeloading">获取提取码</el-button>
        <el-button type="primary" @click="submitForm('form')" class="ml-10">确认提取</el-button>
      </div>
    </template>
    <template v-else>
      <tip type="default" class="mb-20">
        <i class="el-icon-loading mr-5"></i>数据提取中，请耐心等待，勿关闭窗口
      </tip>
      <el-progress :stroke-width="10" :percentage="downloadPercent" color="rgb(60, 184, 241)"></el-progress>
    </template>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";
// Api
import Api from "api/business/index.js";
// 工具
import { downloadFileByStream } from "utils/index.js";

import request from "utils/request";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  name: "GetDataDialog",
  computed: {
    ...mapGetters(["userName"])
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "提取基础测绘地理信息数据"
    },
    data: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      getCodeloading: false,
      showCodeTip: false,
      codeTip: {
        visible: false,
        text: null
      },
      getData: false,
      // 表单
      form: {
        code: null
      },
      // 规则
      rules: {
        code: [{ required: true, message: "请输入提取码", trigger: "blur" }]
      },
      downloadPercent: 0
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      this.form.code = null;
      this.getData = false;
      this.resetCodeTip();
      this.downloadPercent = 0;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    // 关闭存储弹窗
    close() {
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const { data, form, resetGetData, download } = this;
          const { ID, AttachmentName, AttachmentExt } = data;
          const { code } = form;

          Api.GetBaseDataDownloadState(ID, code)
            .then(res => {
              const { StateCode, Data, Message } = res;
              if (StateCode === 1 && Data === 1) {
                this.getData = true;

                download(ID, code)
                  .then(res => {
                    downloadFileByStream(res, AttachmentName + AttachmentExt);
                    resetGetData();
                    this.$message.success("数据下载成功");
                    this.close();
                  })
                  .catch(err => {
                    console.log(err);
                    resetGetData();
                  });
              } else {
                this.$message.error(Message);
                this.resetGetData();
              }
            })
            .catch(err => {
              console.log(err);
              resetGetData();
              // this.$message.error("服务器繁忙，请稍后重试");
            });
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
    download(id, validateNumber) {
      return request({
        url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/BaseDataDownloadRequest?id=${id}&validateNumber=${validateNumber}`,
        method: "get",
        responseType: "blob",
        headers: {
          "content-type": "application/x-www-form-urlencoded; charset=UTF-8"
        },
        onDownloadProgress: e => {
          if (e.total > 0) {
            e.percent = (e.loaded / e.total) * 100;
          }
          this.downloadPercent = parseInt(e.percent, 10);
        }
      });
    },
    resetGetData() {
      this.getData = false;
      this.downloadPercent = 0;
    },
    resetCodeTip() {
      this.getCodeloading = false;

      this.codeTip = {
        visible: false,
        text: null
      };
    },
    getCode() {
      this.$refs.form.clearValidate();
      this.getCodeloading = true;
      
      Api.GetValidateNumber(this.data.ID)
        .then(res => {
          const { StateCode, Data, Message } = res;
          if (StateCode === 1) {
            this.codeTip = {
              visible: true,
              text: Message
            };
            this.getCodeloading = false;
          } else {
            this.$message.error(Message);
            this.resetCodeTip();
          }
        })
        .catch(err => {
          console.log(err);
          this.resetCodeTip();
          // this.$message.error("服务器繁忙，请稍后重试");
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.code-input {
  justify-content: space-between;
}

.tip{
  &-list {
    margin-left:10px;
        padding: 0;

        &__item {
            list-style-type: decimal;
            padding: 2px 0;
        }
    }
}
</style>