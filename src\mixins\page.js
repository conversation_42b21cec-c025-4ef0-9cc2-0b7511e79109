/*
 * 模块 : 列表页码功能相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-10
 * 版本 : version 1.0
 */
export default {
  data() {
    return {
      // 列表加载
      listLoading: false,
      // 分页配置
      page: {
        // 当前页面
        pageNo: 1,
        // 每页条数
        pageSize: 10,
        // 每页条数选择
        pageSizes: [10, 20, 40, 60],
        // 总数
        total: 0
      }
    }
  },
  methods: {
    /**
     * 获取列表内容
     *
     * @param {*} pageNo 当前页码
     * @param {*} pageSize 每页条数
     */
    getList(pageNo, pageSize) {
      this.listLoading = true
      this.apiGetList(pageNo, pageSize).then(res => {
        if (res.StateCode === 1) {
          const { DataTable, Page } = res.Data
          this.listData = DataTable
          this.setPage(Page)
        } else {
          this.$message.error(res.Message)
        }
        this.listLoading = false
      }).catch(err => {
        this.listLoading = false
        console.log(err)
        // this.$message.warning('服务器繁忙，请稍后重试')
      })
    },
    /**
     * 配置页码
     *
     * @param {*} data 数据
     */
    setPage(Page) {
      this.page.total = Page.Total
      this.page.pageNo = Page.PageIndex
      this.page.pageSize = Page.PageSize
    }
  }
}
