<template>
  <!-- eslint-disable -->
  <div v-if="!loading" class="base-info-container">
    <!-- 预览 -->
    <el-form v-if="disableEdit" :model="form" :label-width="labelWidth">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="变更类型：">{{ form.TypeName | isNull }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="变更申请人：">{{ form.CreateUserName | isNull }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请人身份证号：">{{ form.CreateUserNo | isNull }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请人联系方式：">{{ form.CreateUserPhone | isNull }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="变更说明：">{{ form.ModifyReason | isNull }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 存储 -->
    <template v-else>
      <tip type="default" class="mb-20">请输入您的变更说明，说明请尽可能详细以便审核。例：修改单位名称，修改注册测绘师张三。</tip>
      <el-form ref="changeApplyInfoForm" :model="form" :rules="rules" :label-width="labelWidth">
        <el-form-item label prop="ModifyReason">
          <el-input
            type="textarea"
            v-model="form.ModifyReason"
            placeholder="请输入变更说明"
            :autosize="{ minRows: 6 }"
          ></el-input>
        </el-form-item>
      </el-form>
    </template>
  </div>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";

export default {
  name: "CompanyChangeApplyInfo",
  props: {
    // 当前激活类型
    activeTabName: {
      type: String,
      default: "base"
    },
    // 数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 加载
    loading: {
      type: Boolean,
      default: true
    },
    // 禁止编辑
    disableEdit: {
      type: Boolean,
      default: false
    },
    // 组件使用类型，register 单位注册 info 单位信息
    useType: {
      type: String,
      default: "info"
    }
  },
  data() {
    return {
      labelWidth: "0",
      defaultForm: {
        ModifyReason: null
      },
      form: {},
      rules: {
        ModifyReason: [
          { required: true, message: "请输入变更说明", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    loading(val) {
      if (!val) {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      if (!this.disableEdit) {
        this.$nextTick(() => {
          this.$refs.changeApplyInfoForm.clearValidate();
        });
        this.labelWidth = "0";
      } else {
        this.labelWidth = "130px";
      }

      this.form =
        this.data && Object.keys(this.data).length
          ? { ...this.data }
          : { ...this.defaultForm };
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
