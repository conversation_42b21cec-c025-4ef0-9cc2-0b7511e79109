<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="surveyResult">
      <div class="result-title mt-0">
        <span>【{{ name }}】测绘成果</span>
      </div>
      <div class="result-file flex">
        <div>
          {{ surveyResult.AttachmentName + surveyResult.AttachmentExt }}
        </div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(surveyResult)"
          >下载</el-button
        >
      </div>
    </template>
    <template v-if="buildingTableInfo && buildingTableInfo.length">
      <div class="result-title">
        <span>【{{ name }}】测绘成果信息</span>
      </div>
      <building-table-info
        :building-table-info="buildingTableInfo"
        :property-info="propertyInfo"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <template v-if="step >= 4">
      <template v-if="report && report.length">
        <div class="result-title">
          <span>【{{ name }}】放线报告</span>
        </div>
        <file-list
          :file-list="report"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </template>
      <template v-if="preReport && preReport.length">
        <div class="result-title">
          <span>【{{ name }}】不动产预核报告</span>
        </div>
        <file-list
          :file-list="preReport"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </template>
    </template>
    <template v-if="auditFeedback && auditFeedback.length">
      <div class="result-title mt-10">
        <span>【{{ name }}】成果备案反馈</span>
      </div>
      <div
        v-for="(item, index) in auditFeedback"
        :key="'auditFeedback' + index"
        class="result-file flex mb-15"
      >
        <div>{{ item.AttachmentName + item.AttachmentExt }}</div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(item)"
          >下载</el-button
        >
      </div>
    </template>
  </div>
</template>

<script>
/* eslint-disable */
import BuildingTableInfo from "components/business/BuildingTableInfo/index.vue";
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";
import BusinessResult from "mixins/business/result.js";

export default {
  name: "PreMappingMergeResult",
  mixins: [AttachmentDownload, BusinessResult],
  components: { BuildingTableInfo },
  props: {
    // 楼盘信息表pdf
    propertyInfo: {
      type: Object,
      default: null,
    },
    // 放线报告
    report: {
      type: Array,
      default: null,
    },
    // 不动产预核报告
    preReport: {
      type: Array,
      default: null,
    },
    // 成果反馈附件
    auditFeedback: {
      type: Array,
      default: null,
    },
    // 楼盘信息
    buildingTableInfo: {
      type: Array,
      default: null,
    },
    // 工规许可证信息
    projectResultInfo: {
      type: Array,
      default: null,
    },
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
</style>
