/*
 * 模块 : 蓝线图、拔地定桩模块相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2021-01-05
 * 版本 : version 2.0
 */
/* eslint-disable */
// 校验
import { validateAttachment } from "utils/form.js";
// Api
import Api from "api/business/index.js";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  data() {
    return {
      // 业务内容信息，不同业务内容不同
      ContentInfo: {
        ProjectNumber: [],
      },
      // 步骤3
      form3: {
        ApplyData: [],
        ProjectScope: [],
        Data: [],
        ResultImg: [],
        CalculateAreas: [],
      },
      rules3: {
        Data: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Data",
                "测绘成果数据"
              ),
            trigger: "change",
          },
        ],
        ResultImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "ResultImg",
                "测绘成果图"
              ),
            trigger: "change",
          },
        ],
        CalculateAreas: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "CalculateAreas",
                "面积计算表列"
              ),
            trigger: "change",
          },
        ],
      },
      // 基础数据
      baseDataFiles: [],
      // 测绘成果上传地址
      resultUploadUrl: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostAchievementResult`,
      // 项目范围提取数据定时器
      getDataTimer: null,
    };
  },

  methods: {
    handleApiData(Data) {
      Data.ContentInfo.ApplyData = JSON.parse(Data.ContentInfo.ApplyData);
      this.ContentInfo = Data.ContentInfo;
      return true;
    },
    // 处理要提交的业务数据
    handleBusinessData() {
      const { businessID, form1 } = this;
      const {
        ProjectName,
        ProjectNumber,
        ProjectAddress,
        BusinessName,
        ApplyData,
      } = form1;

      this.BaseInfo.BusinessName = BusinessName;

      const data = {
        BaseInfo: this.BaseInfo,
        ContentInfo: {
          ID: businessID,
          ProjectName,
          ProjectNumber,
          ProjectAddress,
          ApplyData: JSON.stringify(ApplyData),
        },
      };

      return data;
    },
    // 显示成果数据 type=result 成果 type=scope 项目范围
    showResult(type = "result") {
      const { currentStep, mappingCompany, ContentInfo } = this;

      if (currentStep === 3 && mappingCompany) {
        if (
          type === "result" &&
          ContentInfo.AchievementCheckID &&
          ContentInfo.AchievementCheckState === 1
        ) {
          return true;
        }

        if (
          type === "scope" &&
          (ContentInfo.DataCheckState === 1 || ContentInfo.DataCheckState === 3)
        ) {
          return true;
        }
      }
      if (currentStep === 4) {
        return true;
      }

      return false;
    },
    // 禁用退回按钮
    disabledBack() {
      const {
        DataCheckState,
        DownLoadState,
        AchievementCheckID,
        AchievementCheckState,
      } = this.ContentInfo;

      console.log(
        DataCheckState,
        DownLoadState,
        AchievementCheckID,
        AchievementCheckState
      );

      if (
        DataCheckState === -3 ||
        (DataCheckState === 3 && DownLoadState !== 1) ||
        (DataCheckState === 1 && DownLoadState !== 1) ||
        (AchievementCheckID && AchievementCheckState === 0)
      ) {
        return true;
      }
      return false;
    },
    // 禁用提交验收
    disabledSubmitAccept() {
      const {
        DataCheckState,
        DownLoadState,
        AchievementCheckID,
        AchievementCheckState,
      } = this.ContentInfo;

      console.log(
        DataCheckState,
        DownLoadState,
        AchievementCheckID,
        AchievementCheckState
      );

      // 未上传测绘成果
      if (!AchievementCheckID
      ) {
        return true;
      }

      // 正在检查或者系统提取数据
      // 或者检查不通过 2021-10-21新增
      if (
        DataCheckState === -3 ||
        (DataCheckState === 3 && DownLoadState !== 1) ||
        (DataCheckState === 1 && DownLoadState !== 1) ||
        (AchievementCheckID && AchievementCheckState === 0) ||
        DataCheckState === 2
      ) {
        return true;
      }

      return false;
    },
    // 显示成果图和面积计算表列
    showReport() {
      const { currentStep } = this;

      if (currentStep === 4) {
        return true;
      }
      return false;
    },
    // 开始上传项目范围
    projectScopeUploadStart() {
      this.ContentInfo.DataCheckState = -3;
    },
    // 项目范围上传成功
    projectScopeUploadSuccess(Data, fileList) {
      if (Data) {
        this.refreshStep(Data);

        const DataCheckState = Data.ContentInfo.DataCheckState;
        this.ContentInfo.DataCheckState = DataCheckState;

        console.log(DataCheckState);

        if (DataCheckState === 1 || DataCheckState === 3) {
          this.getBaseData();
        }
      } else {
        this.projectScopeUploadFail();
      }
    },
    // 项目范围上传失败
    projectScopeUploadFail() {
      this.ContentInfo.DataCheckState = this.defaultDataCheckState;
    },
    // 获取基础数据
    getBaseData() {
      if (this.getDataTimer) {
        this.clearTimer(this.getDataTimer);
      }

      this.getDataTimer = setInterval(() => {
        Api.GetBusiness(this.businessID)
          .then((res) => {
            const { StateCode, Data, Message } = res;
            if (StateCode === 1) {
              const Attachments = Data.Attachments;

              if (Attachments.length) {
                Attachments.forEach((e) => {
                  if (e.AttachmentType === "基础数据") {
                    this.$message.success("数据提取成功");
                    this.refreshStep(Data);
                    this.clearTimer(this.getDataTimer);
                  }
                });
              }
            } else {
              console.log(res);
              this.clearTimer(this.getDataTimer);
            }
          })
          .catch((err) => {
            console.log(err);
            this.clearTimer(this.getDataTimer);
          });
      }, 10000);
    },
  },
};
