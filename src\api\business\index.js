
/* eslint-disable */
import request from 'utils/request';
import qs from "qs";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
	// 获取业务列表
	GetBusinessList: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetFlowList`,
			method: 'get'
		})
	},
	// 判断用户是否能创建业务
	CheckPermission: (businessClass) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/IsFlowCanCreate?businessClass=${businessClass}`,
			method: 'get'
		})
	},
	// 创建业务
	CreateNewBusiness: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/CreateNewBusiness`,
			method: 'post',
			data
		})
	},
	// 获取业务信息
	GetBusiness: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetBusinessByID?id=${id}`,
			method: 'get'
		})
	},
	// 保存业务信息
	SaveBusiness: (id, data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/SaveBusiness?id=${id}`,
			method: 'post',
			data: qs.stringify(data),
			header: {
				'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
			}
		})
	},
	// 提交业务信息（下一步）
	SubmitBusiness: (id, actionId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/SubmitBusiness?id=${id}&actionId=${actionId}`,
			method: 'post'
		})
	},
	// 关闭业务
	CloseBusiness: (id, closeReason) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/CloseBusinessProject?id=${id}&closeReason=${closeReason}`,
			method: 'post'
		})
	},
	// 退回业务 修改至2021-03-16，请求参数传Body
	BackBusiness: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/BackBusiness`,
			method: 'post',
			data
		})
	},
	// 获取委托测绘单位列表
	GetSurveyCompanyList: (params) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetSurveyCompanyList`,
			method: 'get',
			params
		})
	},
	// 委托测绘单位并提交业务
	SelectSurveyCompany: (businessId, actionId, creditCode) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/EntrustSurveyCompany?businessId=${businessId}&ActionId=${actionId}&creditCode=${creditCode}`,
			method: 'post'
		})
	},
	// 注册测绘师确认测绘成果
	ConfirmSurveyResult: (id, actionId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/ConfirmSurveyResult?id=${id}&actionId=${actionId}`,
			method: 'post'
		})
	},
	// 测绘成果确认验收
	AcceptSurveyProjectResult: (id,actionId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/AcceptSurveyProjectResult?id=${id}&actionId=${actionId}`,
			method: 'post'
		})
	},
	// 获取工程规划许可证信息
	GetProjectPlanPermissionInfo: (code, businessId, businessClass) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetProjectPlanPermissionInfo?code=${code}&businessId=${businessId}&businessClass=${businessClass}`,
			method: 'get'
		})
	},
	// 获取土地用途列表
	GetLandUseList:() => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetLandUseList`,
			method: 'get'
		})
	},
	// 根据单位id查看资质证书副本
	GetQualificationImage: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/ShowQualificationImage?id=${id}`,
			method: 'get'
		})
	},
	GetQualificationImage: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/ShowQualificationImage?id=${id}`,
			method: 'get',
			responseType: "blob",
			headers: {
				'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
			}
		})
	},
	// 获取项目范围坐标
	GetScopeCoordinates: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/GetAttachementFileStr?id=${id}`,
			method: 'get'
		})
	},
	// 判断基础数据附件是否可下载
	GetBaseDataDownloadState: (id, validateNumber) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/GetBaseDataDownloadState?id=${id}&validateNumber=${validateNumber}`,
			method: 'get',
		})
	},
	// 基础数据附件下载
	DownloadBaseData: (id, validateNumber, onUploadProgress) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/BaseDataDownloadRequest?id=${id}&validateNumber=${validateNumber}`,
			method: 'get',
			responseType: "blob",
			headers: {
				'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
			},
			onUploadProgress,
		})
	},
	// 获取基础数据提取码
	GetValidateNumber: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/GetValidateNumber?id=${id}`,
			method: 'get',
		})
	},
	// 重新委托测绘单位
	ReSelectSurveyCompany: (businessId, actionId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/EntrustSurveyCompanyAgain?businessId=${businessId}&actionId=${actionId}`,
			method: 'post'
		})
	},

	// 获取楼盘表信息
	GetBuildingList: (zdh, chzt) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetBuildingTableByZDH?zdh=${zdh}&chzt=${chzt}`,
			method: 'get'
		})
	},

	// 变更流程校验楼幢信息
	validBulidingInfo: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetZRZZT`,
			method: 'post',
			data
		})
	},
}