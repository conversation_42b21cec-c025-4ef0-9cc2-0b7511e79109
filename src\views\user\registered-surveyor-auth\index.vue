<template>
  <!-- eslint-disable -->
  <div>
    <el-card shadow="never" class="overspread-card">
      <!-- 操作按钮 -->
      <div class="operate-btn-container flex mb-20">
        <div class="button-container">
          <el-button
            type="default"
            @click="$router.push({ name: 'CompanyInfo' })"
            class="mr-10"
            >返回单位信息</el-button
          >
        </div>
        <div v-if="companyRole === '单位管理员'" class="search">
          <el-input
            placeholder="请输入姓名、手机号码"
            v-model="key"
            clearable
            @clear="getList(1, page.pageSize)"
            style="width:400px"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="search"
            ></el-button>
          </el-input>
        </div>
      </div>
      <!-- 表格 -->
      <dynamic-table
        v-loading="listLoading"
        element-loading-text="加载中，请稍等..."
        :table-header="tableHeader"
        :table-data="listData"
        :default-props="tableProps"
        :showPagination="companyRole === '单位管理员' ? true : false"
        :total="page.total"
        :page-no.sync="page.pageNo"
        :page-size.sync="page.pageSize"
        :page-sizes.sync="page.pageSizes"
        @pagination="getList"
      >
        <el-table-column width="50" label="序号" align="center">
          <template slot-scope="{ $index }">{{
            $index + 1 + page.pageSize * (page.pageNo - 1)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="PersonName"
          label="姓名"
          width="150"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="BusinessClasses"
          label="授权业务类型"
          width="300"
          align="center"
        >
          <template slot-scope="{ row }">
            <template v-if="row.BusinessClasses && row.BusinessClasses.length">
              <el-tag
                v-for="(tag, index) in formatBusinessClasses(
                  row.BusinessClasses
                )"
                :key="'BusinessClassesTag' + index"
                type="success"
                class="mr-10 mb-5 mt-5"
              >
                {{ tag }}
              </el-tag>
            </template>
          </template>
        </el-table-column>
        <el-table-column
          prop="AuthEndDate"
          label="授权截止日期"
          width="150"
          align="center"
        >
          <template slot-scope="{ row }">
            <span :class="{ red: isExpire(row) }">{{ row.AuthEndDate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="PersonPhone"
          label="手机号码"
          width="150"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="PersonNumber"
          label="身份证号码"
          width="200"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="StateCode"
          label="授权状态"
          width="100"
          fixed="right"
          align="center"
        >
          <template slot-scope="{ row }">
            <template v-if="row.AuthRequest">
              <el-tag
                v-if="row.AuthStateCode === 0"
                type="warning"
                effect="dark"
                >待确认</el-tag
              >
              <el-tag
                v-if="row.AuthStateCode === 1"
                type="success"
                effect="dark"
                >已确认</el-tag
              >
              <el-tag v-if="row.AuthStateCode === 2" type="danger" effect="dark"
                >已过期</el-tag
              >
              <el-tag v-if="row.AuthStateCode === -1" type="info" effect="dark"
                >已撤回</el-tag
              >
            </template>
            <el-tag v-else type="info" effect="plain">未授权</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="listData.length"
          prop="action"
          label="操作"
          width="150"
          fixed="right"
          align="center"
        >
          <template slot-scope="{ row, $index }">
            <template v-if="companyRole === '单位管理员'">
              <el-button
                icon="el-icon-document"
                type="text"
                @click="showInfo(row, $index)"
                >查看信息
              </el-button>
              <br />
              <template v-if="row.AuthRequest">
                <!-- 测绘师已授权或者已过期的，关闭此授权，该操作只能由注册测绘师本人或单位管理员操作 -->
                <el-button
                  v-if="row.AuthStateCode === 1 || row.AuthStateCode === 2"
                  type="text"
                  icon="el-icon-close"
                  @click="closeAuth(row, $index)"
                  >关闭授权</el-button
                >
                <el-button
                  v-if="row.AuthStateCode === 0"
                  type="text"
                  @click="backApply(row, $index)"
                >
                  <i class="iconfont icon-back mr-5"></i>撤回申请
                </el-button>
                <template v-if="row.AuthStateCode === 2">
                  <br />
                  <el-button
                    type="text"
                    icon="el-icon-setting"
                    @click="showApplyDialog(row, $index)"
                    >重新申请</el-button
                  >
                </template>
              </template>
              <el-button
                v-else
                type="text"
                icon="el-icon-setting"
                @click="showApplyDialog(row, $index)"
                >申请授权</el-button
              >
            </template>
            <!-- 测绘师已授权或者已过期的，关闭此授权，该操作只能由注册测绘师本人或单位管理员操作 -->
            <template v-if="row.AuthRequest && row.PersonNumber === personNo">
              <el-button
                v-if="row.AuthStateCode === 1 || row.AuthStateCode === 2"
                type="text"
                icon="el-icon-close"
                @click="closeAuth(row, $index)"
                >关闭授权</el-button
              >
              <!-- 注册测绘师可见 -->
              <div
                v-if="row.AuthStateCode === 0 && row.PersonNumber === personNo"
                style="color:#67c23a"
              >
                请登录邕e登App进入“我的授权”模块进行刷脸确认授权
              </div>
            </template>
          </template>
        </el-table-column>
      </dynamic-table>
    </el-card>
    <!-- 申请授权弹窗 -->
    <apply-dialog
      :visible.sync="applyDialog.visible"
      :row="applyDialog.row"
      :business-options="businessOptions"
      @submit="getList(1, page.pageSize)"
    />
    <!-- 查看注册测绘师信息弹窗 -->
    <detail-dialog
      :visible.sync="detailDialog.visible"
      :row="detailDialog.row"
      :index="detailDialog.index"
      :disable-edit="true"
      dialog-title="注册测绘师个人信息"
    />
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import DynamicTable from "components/common/Table/DynamicTable";
import ApplyDialog from "./apply.vue";
import DetailDialog from "views/user/company-info/member-manage/store.vue";
// mixins
import Page from "mixins/page.js";
import Table from "mixins/table.js";
// Api
import Api from "api/registered-surveyor-auth/index.js";
// Api
import CompanyInfoApi from "api/company-info/index.js";
// import BusinessApi from "api/business/index.js";
// vuex
import { mapGetters } from "vuex";

export default {
  name: "CompanyChangeHistoryDialog",
  components: { DynamicTable, ApplyDialog, DetailDialog },
  mixins: [Page, Table],
  computed: {
    ...mapGetters(["companyID", "companyRole", "personNo"]),
  },
  data() {
    return {
      key: "",
      tableHeader: [],
      // 申请授权弹窗
      applyDialog: {
        visible: false,
        row: {
          ID: null,
        },
      },
      // 业务树形
      businessOptions: [],
      // 存储弹窗配置
      detailDialog: {
        visible: false,
        row: null,
        index: -1,
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化
    async init() {
      this.listLoading = true;

      /* 获取业务列表取业务类型-start */
      // this.businessOptions = [];
      // const businessRes = await BusinessApi.GetBusinessList();

      // const { StateCode, Data } = businessRes;

      // if (StateCode === 1 && Data) {
      //   Data.forEach((e) => {
      //     const { children } = e;
      //     if (children && children.length) {
      //       children.forEach((c) => {
      //         const { label, businessClass, enable } = c;
      //         // 手动过滤“测绘数据申请”和禁用项
      //         if (businessClass !== "BaseSurveyDataDownloadFlow" && enable) {
      //           this.businessOptions.push({ label, value: businessClass });
      //         }
      //       });
      //     }
      //   });
      // } else {
      //   console.log(businessRes);
      // }
      /* 获取业务列表取业务类型-end */

      /**
       * 2021-03-02 新需求修改
       * 通过单位信息获取可用的多测合一业务类型
       */
      const res = await CompanyInfoApi.GetCompanyDetailsInfo(this.companyID);
      this.businessOptions =
        res.Data.DetailInfo.CompanyQualification.BusinessRangeJSON;
      /* 2021-03-02 新需求修改 */

      this.getList(1, this.page.pageSize);
    },
    getList(pageNo, pageSize) {
      this.listLoading = true;
      Api.GetSurveyMasterList(pageNo, pageSize, this.key)
        .then((res) => {
          const { Data, Message } = res;
          if (res.StateCode === 1) {
            const { DataTable, Page } = Data;

            this.listData = DataTable.map((e) => {
              const { AuthRequest } = e;
              if (AuthRequest) {
                const { BusinessClasses, AuthEndDate, StateCode } = AuthRequest;
                e.BusinessClasses = BusinessClasses;
                e.AuthEndDate = AuthEndDate.substring(
                  0,
                  AuthEndDate.indexOf("00:00:00")
                );
                e.AuthStateCode = StateCode;
              } else {
                e.BusinessClasses = [];
                e.AuthEndDate = null;
                e.AuthStateCode = 0;
              }
              return e;
            });
            this.setPage(Page);
          } else {
            this.$message.error(Message);
          }
          this.listLoading = false;
        })
        .catch((err) => {
          this.listLoading = false;
          console.log(err);
        });
    },
    // 授权配置
    showApplyDialog(row, index) {
      this.applyDialog = {
        visible: true,
        row,
        index,
      };
    },
    // 是否过期
    isExpire(row, index) {
      return row.AuthRequest && row.AuthStateCode === 2;
    },
    // 退回申请
    backApply(row, index) {
      const { AuthRequest } = row;
      if (!AuthRequest || !AuthRequest.ID) {
        console.log("授权id不存在");
        return;
      }

      this.$confirm("确认撤回该申请吗？此操作不可逆，请谨慎处理", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          Api.CancelSurveyMasterAuth(AuthRequest.ID)
            .then((res) => {
              const { StateCode, Data, Message } = res;

              this.loading = false;

              if (StateCode === 1) {
                this.$message.success("撤销成功");
                this.getList(this.page.pageNo, this.page.pageSize);
              } else {
                this.$message.error(Message);
              }
            })
            .catch((err) => {
              console.log(err);
              this.loading = false;
            });
        })
        .catch((err) => console.log(err));
    },
    // 关闭授权
    closeAuth(row, index) {
      const { AuthRequest } = row;
      if (!AuthRequest || !AuthRequest.ID) {
        console.log("授权id不存在");
        return;
      }

      this.$confirm(`确认关闭该授权吗？此操作不可逆，请谨慎处理`, "温馨提示", {
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          Api.CloseSurveyMasterAuth(AuthRequest.ID)
            .then((res) => {
              const { StateCode, Data, Message } = res;

              this.loading = false;

              if (StateCode === 1) {
                this.$message.success("关闭成功");
                this.getList(this.page.pageNo, this.page.pageSize);
              } else {
                this.$message.error(Message);
              }
            })
            .catch((err) => {
              console.log(err);
              this.loading = false;
            });
        })
        .catch((err) => console.log(err));
    },
    // 业务类型格式化
    formatBusinessClasses(BusinessClasses) {
      let classes = [];
      BusinessClasses.forEach((e) => {
        const businessClass = this.businessOptions.find(
          (c) => c.BusinessClass === e.BusinessClass
        );

        if (businessClass && businessClass.BusinessName) {
          classes.push(businessClass.BusinessName);
        }
      });

      return classes;
    },
    // 弹出注册测绘师信息窗
    showInfo(row, index) {
      this.detailDialog = {
        visible: true,
        row,
        index,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.operate-btn-container {
  justify-content: space-between;
  align-items: center;

  .hint {
    font-size: 12px;
    color: #f07057;
  }
}
</style>
