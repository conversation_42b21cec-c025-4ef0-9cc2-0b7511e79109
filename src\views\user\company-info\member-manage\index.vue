<template>
  <!-- eslint-disable -->
  <div
    class="member-container"
    v-loading="listLoading"
    element-loading-text="加载中，请稍等..."
  >
    <!-- 操作按钮 -->
    <div v-if="!remberDisableEdit" class="operate-btn-container flex mb-20">
      <div style="min-width: 200px">
        <el-button class="mr-10" type="primary" @click="showStore(null, -1)"
          >添加</el-button
        >
        <el-popconfirm title="确认删除?" @onConfirm="del()">
          <el-button
            slot="reference"
            type="warning"
            :disabled="selectList.length === 0"
            >批量删除</el-button
          >
        </el-popconfirm>
      </div>
      <div class="hint">
        <i class="el-icon-warning mr-5"></i
        >温馨提示：您可以自行添加、编辑或删除单位的
        <!-- 测绘单位 -->
        <template v-if="mappingCompany"
          >“测绘人员”和“单位管理员”，若想变更“注册测绘师”，请申请变更。</template
        >
        <!-- 建设单位 -->
        <template v-else
          >“报建员”，若想变更“单位管理员”，请到不动产登记中心重新申请备案。</template
        >
      </div>
    </div>
    <!-- 表格 -->
    <dynamic-table
      v-if="!listLoading"
      ref="memberTable"
      class="table-container"
      :table-header="tableHeader"
      :table-data="listData"
      :default-props="tableProps"
      :showPagination="true"
      :total="page.total"
      :page-no.sync="page.pageNo"
      :page-size.sync="page.pageSize"
      :page-sizes.sync="page.pageSizes"
      @pagination="getList"
      @selection-change="getSelectList"
    >
      <el-table-column
        v-if="!remberDisableEdit"
        type="selection"
        width="55"
        fixed="left"
      ></el-table-column>
      <el-table-column width="50" label="序号" align="center">
        <template slot-scope="{ $index }">{{
          $index + 1 + page.pageSize * (page.pageNo - 1)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="PersonName"
        label="姓名"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="PersonNumber"
        label="身份证号码"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="PersonPhone"
        label="手机号码"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="PersonRole"
        label="角色"
        width="120"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.PersonRole === '单位管理员'">{{
            row.PersonRole
          }}</el-tag>
          <el-tag v-if="row.PersonRole === '报建员'" type="success">{{
            row.PersonRole
          }}</el-tag>
          <el-tag v-if="row.PersonRole === '测绘人员'" type="success">{{
            row.PersonRole
          }}</el-tag>
          <el-tag v-if="row.PersonRole === '注册测绘师'" type="warning">{{
            row.PersonRole
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="listData.length"
        prop="action"
        label="操作"
        width="165"
        fixed="right"
        align="center"
      >
        <template slot-scope="{ row, $index }">
          <template v-if="showEditableBtn(row) && row.UserType !== '1'">
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="showStore(row, $index)"
              >编辑</el-button
            >
          </template>
          <template v-else>
            <el-button
              type="text"
              icon="el-icon-document"
              @click="showStore(row, $index)"
              >查看详情</el-button
            >
          </template>
          <template v-if="row.PersonNumber === personNo && row.UserType !== '1'">
            <el-button
              type="text"
              icon="el-icon-delete"
              style="margin-left: 5px"
              @click="removeSelf(row, $index)"
              >离职</el-button
            >
          </template>
          <!-- 普通删除按钮 -->
          <template
            v-if="showEditableBtn(row) && row.PersonNumber !== personNo && row.UserType !== '1'"
          >
            <el-popconfirm
              title="确认删除?"
              class="ml-5"
              @onConfirm="delRow(row)"
            >
              <el-button slot="reference" type="text" icon="el-icon-delete"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
          <!-- 注册测绘师删除按钮 -->
          <template v-if="showDelRegisteredSurveyorBtn(row)">
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="delRegisteredSurveyor(row)"
              >删除</el-button
            >
          </template>
        </template>
      </el-table-column>
    </dynamic-table>
    <!-- 存储弹窗 -->
    <store-dialog
      :visible.sync="storeDialog.visible"
      :row="storeDialog.row"
      :index="storeDialog.index"
      :disable-edit="storeDialog.disableEdit"
      @submit="storeSuccess"
    />
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import DynamicTable from "components/common/Table/DynamicTable";
import StoreDialog from "./store.vue";
// mixins
import Page from "mixins/page.js";
import Table from "mixins/table.js";
// Api
import Api from "api/company-info/index.js";
import UserApi from "api/user";
// vuex
import { mapGetters } from "vuex";
import { getToken } from "utils/auth";

export default {
  name: "CompanyMember",
  components: { DynamicTable, StoreDialog },
  mixins: [Page, Table],
  computed: {
    ...mapGetters([
      "companyID",
      "companyName",
      "companyRole",
      "companyType",
      "personNo",
      "currentEnv",
      "roles",
      "isRealName",
      "personName",
      "userName",
    ]),
    // 获取列表接口
    apiGetList(PageIndex, PageSize) {
      return (PageIndex, PageSize) =>
        Api.GetMemberList(this.companyID, PageIndex, PageSize);
    },
    // 删除操作接口
    apiDelete(ids) {
      return (ids) => Api.DeleteMember(ids);
    },
    // 添加操作接口
    apiAdd(data) {
      return (data) => Api.StoreMember(data);
    },
    // 编辑操作接口
    apiEdiit(data, id) {
      return (data) => Api.StoreMember(data);
    },
    // 测绘单位
    mappingCompany() {
      return this.companyType === '测绘单位';
    }
  },
  props: {
    // 当前激活类型
    activeTabName: {
      type: String,
      default: "member",
    },
    // 数据
    data: {
      type: Array,
      default: () => [],
    },
    // tab页加载
    loading: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableHeader: [],
      // 存储弹窗配置
      storeDialog: {
        disableEdit: false,
        visible: false,
        row: null,
        index: -1,
      },
      remberDisableEdit: false,
      // 默认单位信息
      defaultCompany: {
        stateCode: 0,
        companyType: null,
        companyName: null,
        companyID: null,
      },
    };
  },
  watch: {
    activeTabName(val) {
      this.clearSelect();
      if (this.$refs.memberTable) {
        this.$refs.memberTable.$refs.table.clearSelection();
      }
    },
    companyID(val) {
      // 解决因为本地项目因注销后token不存在，接口报错401的问题
      if (val) {
        this.init();
      }
    },
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.setTableHeight(595);
      this.getList(1, this.page.pageSize);
      this.setRemberEditable();
    },
    // 设置人员信息可编辑性
    setRemberEditable() {
      this.remberDisableEdit = this.companyRole === "单位管理员" ? false : true;
    },
    // 显示编辑、删除按钮
    showEditableBtn(row) {
      const { listData, remberDisableEdit, mappingCompany } = this;
      if (listData.length <= 1 || remberDisableEdit) {
        return false;
      }
      // 测绘单位
      if (mappingCompany && row.PersonRole === "注册测绘师") {
        return false;
      }
      // 建设单位
      if (!mappingCompany && row.PersonRole === "单位管理员") {
        return false;
      }

      return true;
    },
    // 显示注册测绘师删除按钮
    showDelRegisteredSurveyorBtn(row) {
      const { listData, remberDisableEdit, personNo } = this;
      if (listData.length <= 1 || remberDisableEdit) {
        return false;
      }
      if (row.PersonRole !== "注册测绘师" || row.PersonNumber === personNo) {
        return false;
      }

      return true;
    },
    // 弹出添加/编辑窗
    showStore(row, index) {
      const { showEditableBtn } = this;

      let disableEdit = false;
      if (!row) {
        disableEdit = false;
      } else {
        disableEdit = showEditableBtn(row) ? false : true;
      }

      this.storeDialog = {
        disableEdit,
        visible: true,
        row,
        index,
      };
    },
    // 存储操作成功
    // type: 1 编辑 0 添加
    storeSuccess(type, params, index) {
      this.getList(type ? this.page.pageNo : 1, this.page.pageSize);
    },
    // 获取选项id
    getSelectList(selection) {
      // 清空选项列表
      this.clearSelect();
      this.selectList = selection;
    },
    // 删除一行
    delRow(row) {
      this.selectList = [row];
      this.del();
    },
    // 删除
    del() {
      const {
        selectList,
        page,
        personNo,
        personName,
        mappingCompany,
        userName,
        apiDelete,
        getList,
        clearSelect,
        $message,
      } = this;

      if (!selectList.length) {
        $message.warning("请选择要删除的选项");
        return false;
      }

      let ids = "";
      for (let i = 0; i < selectList.length; i++) {
        const { PersonNumber, PersonRole, ID } = selectList[i];

        if (PersonNumber === personNo) {
          $message.warning(
            `不可删除当前账号：${personName}(${userName})，请重新选择`
          );
          return false;
        }
        // 建设单位
        else if (!mappingCompany && PersonRole === "单位管理员") {
          $message.warning("不可删除【单位管理员】，请重新选择");
          return false;
        }
        // else if (PersonRole === "注册测绘师") {
        //   $message.warning("不可删除【注册测绘师】，请重新选择");
        //   return false;
        // }

        ids += ID + ",";
      }

      ids = ids.slice(0, ids.length - 1);

      apiDelete(ids)
        .then(async (res) => {
          if (res.StateCode === 1) {
            $message.success(res.Message);
            getList(1, page.pageSize);
          } else $message.error(res.Message);
          // 删除成功要清除selectList
          clearSelect();
        })
        .catch((err) => {
          console.log(err);
          $message.warning("服务器繁忙，请稍后重试");
          // 删除成功要清除selectList
          clearSelect();
        });
    },
    // 删除注册测绘师
    delRegisteredSurveyor(row) {
      this.$confirm(
        `确认删除该注册测绘师吗？删除后，该注册测绘师将无权对你单位的测绘成果及授权进行成果确认，请谨慎处理！`,
        "温馨提示",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
        }
      )
        .then((res) => {
          this.selectList = [row];
          this.del();
        })
        .catch((err) => console.log(err));
    },
    // 自行离职
    removeSelf(row) {
      const {
        companyID,
        companyName,
        changeCompanyAndRole,
        clearSelect,
        $confirm,
        $message,
      } = this;

      $confirm(
        row.PersonRole === "注册测绘师"
          ? `您将从【${companyName}】离任，将无权对该单位的测绘成果进行确认及授权，请谨慎处理！`
          : `是否确认离职？您将与【${companyName}】解除关系绑定。此操作不可逆，请谨慎处理！`,
        "温馨提示",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
        }
      )
        .then((res) => {
          Api.LeaveTheCompany(companyID)
            .then(async (res) => {
              if (res.StateCode === 1) {
                await changeCompanyAndRole();
              } else $message.error(res.Message);
            })
            .catch((err) => {
              console.log(err);
              $message.warning("服务器繁忙，请稍后重试");
            });

          // 离职成功要清除selectList
          clearSelect();
        })
        .catch((err) => console.log(err));
    },
    // 变更单位信息和角色权限
    changeCompanyAndRole() {
      const {
        companyName,
        defaultCompany,
        isRealName,
        mappingCompany,
        $store,
        $message,
        $router
      } = this;

      this.listLoading = true;

      return new Promise(async (resolve, reject) => {
        try {
          // 修改单位信息
          let company = { ...defaultCompany };
          let stateCode = 0;
          // 没有绑定任何单位
          let newRoles = isRealName ? ["1"] : ["0", "1"];
          let companyList = [];

          // 建设单位
          if (!mappingCompany) {
            const companyInfo = await UserApi.GetCompanyInfoByUserInfo();

            if (companyInfo.StateCode === 1 && companyInfo.Data) {
              const { CompanyList, CompanyType } = companyInfo.Data;
              companyList = CompanyList;

              if (companyList.length) {
                let currentCompany = companyList[0];

                const { CID, CName, CType, CRole, State } = currentCompany;
                stateCode = State;

                company = {
                  stateCode,
                  companyType: CType,
                  companyName: CName,
                  companyID: CID,
                  companyRole: CRole,
                };
                // 切换其他建设单位角色
                newRoles = isRealName ? ["c-d"] : ["0"];

                stateCode === 3 ? newRoles.push("2") : newRoles.push("1");
              }
            }
          }

          await $store.dispatch("user/setCompany", company);
          await $store.dispatch("user/setCompanyList", companyList);

          // 修改路由权限
          await this.$store.dispatch("permission/generateRoutes", {
            roles: newRoles,
          });

          // 修改角色
          await $store.dispatch("user/setRoles", newRoles);

          $message.success(
            `操作成功，您已成功离职，并与【${companyName}】解除关系绑定`
          );

          this.listLoading = false;

          // 离职后返回用户中心
          $router.push({ name: "UserCenter" });

          resolve(true);
        } catch (err) {
          console.log(err);
          this.listLoading = false;
          this.$message.error(`操作失败，请稍后重试`);
          reject(false);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.member-container {
  min-height: 200px;
}
.operate-btn-container {
  justify-content: space-between;
  align-items: center;

  .hint {
    font-size: 12px;
    color: #f07057;
  }
}
</style>
