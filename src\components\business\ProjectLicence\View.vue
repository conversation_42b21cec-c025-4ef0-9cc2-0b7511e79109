<template>
  <!-- eslint-disable -->
  <div>
    <div class="licence-title">【{{ data.Code }}】</div>
    <table class="table" cellpadding="0" cellspacing="0">
      <tr>
        <th colspan="2">建设单位</th>
        <td colspan="7">{{ data.ConstructCompany }}</td>
        <th colspan="2">建设地址</th>
        <td colspan="4">{{ data.Address }}</td>
      </tr>
      <tr>
        <th colspan="2">项目名称</th>
        <td colspan="7">{{ data.ProjectName }}</td>
        <th colspan="2">附图编号</th>
        <td colspan="4">{{ data.AppendixImgNumber }}</td>
      </tr>
      <tr>
        <th rowspan="2" class="th-fixed-width">建筑类别</th>
        <th rowspan="2" class="th-fixed-width">
          <div>面积</div>
          <div class="mt-5">(㎡)</div>
        </th>
        <th rowspan="2">建筑性质</th>
        <th rowspan="2">建筑结构</th>
        <th colspan="2">
          <div>总建筑面积</div>
          <div class="mt-5">(㎡)</div>
        </th>
        <th rowspan="2">
          <div>基地面积</div>
          <div class="mt-5">(㎡)</div>
        </th>
        <th colspan="2">层数</th>
        <th rowspan="2">
          <div>建筑总高度</div>
          <div class="mt-5">(m)</div>
        </th>
        <th rowspan="2">栋数</th>
        <th rowspan="2">
          <div>投资</div>
          <div class="mt-5">(万元)</div>
        </th>
        <!-- <th colspan="3">套型</th> -->
        <th colspan="3">其他</th>
      </tr>
      <tr>
        <th>地上</th>
        <th>地下</th>
        <th>地上</th>
        <th>地下</th>
        <th class="th-fixed-width">名称</th>
        <th class="th-fixed-width">层数</th>
        <th class="th-fixed-width">
          <div>面积</div>
          <div class="mt-5">(㎡)</div>
        </th>
      </tr>
      <tr>
        <td colspan="2" style="padding: 0">
          <div
            class="table-div"
            v-for="(item, index) in data.Buildings"
            :key="'BuildingType' + index"
          >
            <div class="th-fixed-width">{{ item.Type }}</div>
            <div class="th-fixed-width">{{ item.Area }}</div>
          </div>
        </td>
        <td>{{ data.BuildingNature }}</td>
        <td>{{ data.BuildingStructure }}</td>
        <td>{{ data.TotalArea.Aboveground }}</td>
        <td>{{ data.TotalArea.Underground }}</td>
        <td>{{ data.BaseArea }}</td>
        <td>{{ data.Floors.Aboveground }}</td>
        <td>{{ data.Floors.Underground }}</td>
        <td>{{ data.BuildingHeight }}</td>
        <td>{{ data.BuildingBlockNumber }}</td>
        <td>{{ data.Invest }}</td>
        <!-- <td colspan="3" style="padding: 0">
        <div class="table-div" v-for="(item, index) in data.SetType" :key="'BuildingType' + index">
          <div class="th-fixed-width">{{ item.Name }}</div>
          <div class="th-fixed-width">{{ item.Number }}</div>
          <div class="th-fixed-width">{{ item.Area }}</div>
        </div>
        </td>-->
        <td>{{ data.Others.Name }}</td>
        <td>{{ data.Others.Number }}</td>
        <td>{{ data.Others.Area }}</td>
      </tr>
      <tr>
        <th colspan="2">备注</th>
        <td colspan="13">{{ data.Remark }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "ProjectLicenceView",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false
    },
    // 记录数据
    row: {
      type: Object,
      default: () => ({})
    },
    // 当前记录索引
    index: {
      type: Number,
      default: -1
    },
    // 工规证类型
    licenceType: {
      type: String,
      default: "放线",
    },
  },
  watch: {
    row(val) {
      this.init();
    }
  },
  data() {
    return {
      data: {
        // 建设单位
        ConstructCompany: null,
        // 建设地址
        Address: null,
        // 项目名称
        ProjectName: null,
        // 附图编号
        AppendixImgNumber: null,
        // 不同建筑类别
        Buildings: [
          {
            // 建筑类别
            Type: null,
            // 面积(㎡)
            Area: null
          }
        ],
        // 建筑性质
        BuildingNature: null,
        // 建筑结构
        BuildingStructure: null,
        // 建筑总面积
        TotalArea: {
          //地上
          Aboveground: null,
          // 地下
          Underground: null
        },
        // 基地面积
        BaseArea: null,
        // 层数
        Floors: {
          // 地上
          Aboveground: null,
          // 地下
          Underground: null
        },
        // 建筑总高度
        BuildingHeight: null,
        // 栋数
        BuildingBlockNumber: null,
        // 投资（万元）
        Invest: null,
        // 套型
        SetType: [
          {
            // 套型
            Name: null,
            // 套数
            Number: null,
            // 面积
            Area: null
          }
        ],
        // 其他
        Others: {
          // 名称
          Name: null,
          // 层数
          Number: null,
          // 面积
          Area: null
        },
        // 备注
        Remark: null
      }
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      // console.log(this.row);
      this.data = this.row;
    },
    // 关闭弹窗
    close() {
      this.$emit("close");
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  th {
    background: #f8f8f8;
    color: #909399;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    word-break: break-all;
  }

  td {
    .table-div:last-child {
      div {
        border-bottom: none;
      }
    }
  }

  .th-fixed-width {
    min-width: 78px;
    width: 78px;
    max-width: 78px;
  }
}

.table-div {
  display: table;
  line-height: 1;
  min-height: 35px;

  div {
    display: table-cell;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    padding: 10px 3px;

    &:last-child {
      border-right: none;
    }
  }
}

.licence-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
}
</style>