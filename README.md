# SDCPCWeb

## 目录结构

```
│  .editorconfig
│  .env.development // 开发环境配置
│  .env.production // 生产环境配置
│  .env.release // release正式库环境配置
│  .env.staging // stage正式库环境配置
│  .eslintrc.js
│  .gitignore
│  babel.config.js // babel配置
│  jsconfig.json
│  package.json
│  postcss.config.js // CSS转换配置
│  vue.config.js // 配置文件
│  README.md
│
├─src
│  │  main.js // 项目入口js
│  │  permission.js // 路由权限判断
│  │  App.vue // 根组件
│  │
│  ├─assets // 资源目录，这里的资源会被wabpack构建
│  ├─api // api接口文件
│  ├─store  // 应用级数据（state）
│  ├─styles  // 样式
│  │
│  ├─filters // 过滤器
│  │    index.js
│  │
│  ├─components // 功能组件
|  |  | CountDownDialog // 承诺书
│  │
│  ├─layout // 布局
│  │  │  
│  │  ├─ default // 基础布局
│  │  │   │  index.vue
│  │  │   │
│  │  │   └─components // 布局组件
│  │  │      │  index.js
│  │  │      │
│  │  │      ├─AppTop // 顶部  
│  │  │      ├─AppFooter // 底部
│  │  │      ├─AppMain // 主页面
│  │  │      ├─Breadcrumb // 面包屑
│  │  │      └─Navbar // 导航
│  │  │
│  │  ├─ router-view // 配置三级以上路由时使用
│  │  │
│  │  └─ fullscreen // 全屏布局
│  │
│  ├─views // 视图
|  |  |-user
|  |     |-business
|  |         |-list
|  |            |-guide //业务指南
|  |            |-createBusiness.vue // 创建业务弹窗
│  │
│  ├─mock // json数据
|  |   | bussinessList.json // 业务页面路由，当点击查看详情无响应时，需要检查这个文件有无配置
│  │
│  ├─mixins // 混合模块
│  │   │ resize.js // 窗口改变大小
│  │   │ scroll.js // 页面滚动
│  │   │ page.js // 页码配置
│  │   │ table.js // 表格相关操作
│  │   │ switch-company.js // 切换单位
│  │   │  
│  │   ├─attachment // 附件相关模块   
│  │   ├─business // 业务相关模块  
│  │   ├─company-info // 单位信息相关模块  
│  │   └─my-business // 我的业务相关模块
│  │
│  ├─utils // 通用工具函数
│  │    index.js
│  │    form.js // 表单
│  │    auth.js // 读写token和companyID
│  │    get-page-title.js // 页面标题
│  │    request.js // 请求配置
│  │    scroll-to.js // 页面滚动
│  │    validate.js // 验证
│  │
│  └─router  // 路由配置
│     │  index.js //用户中心各菜单列表路由配置
│     │  
│     └─modules // 分模块的动态路由
│        user.js //各表单页路由配置
│
└─pubilc
   │ favicon.ico // 图标
   │ index.html // 首页入口文件
   │  
   └─static // 静态资源，不会被wabpack构建
      | pdf // 承诺书等模版文件
```

## 本地运行
1. 安装前台依赖
> npm install
2. 运行前台项目
> npm run dev
3. 访问地址：http://localhost:9090

## 项目打包
1. 联调版本
> npm run build:practice 
2. 正式版本
> npm run build:release
3. 待指定版本
> npm run build:stageing