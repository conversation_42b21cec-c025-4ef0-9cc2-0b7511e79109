<template>
  <business-layout
    class="actual-mapping-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="prevBtn"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @prev="prev"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1 && developer">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="140px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业主单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业主单位地址：" prop="CompanyAddress">
                  <el-input
                    v-model.trim="form1.CompanyAddress"
                    placeholder="请输入业主单位地址"
                    :disabled="BaseInfo.StateCode === 4"
                    maxlength="165"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="法定代表人姓名：" prop="LegalPersonName">
                  <el-input
                    v-model.trim="form1.LegalPersonName"
                    placeholder="请输入法定代表人姓名"
                    :disabled="BaseInfo.StateCode === 4"
                    maxlength="6"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="法定代表人身份证号：" prop="LegalPersonNumber">
                  <el-input
                    v-model.trim="form1.LegalPersonNumber"
                    placeholder="请输入法定代表人身份证号"
                    :disabled="BaseInfo.StateCode === 4"
                    maxlength="18"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="法定代表人联系电话：" prop="LegalPersonPhone">
                  <el-input
                    v-model.trim="form1.LegalPersonPhone"
                    placeholder="请输入法定代表人联系电话"
                    :disabled="BaseInfo.StateCode === 4"
                    maxlength="11"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="规划条件编号：" prop="PlanConditionNumber">
                  <el-input
                    v-model.trim="form1.PlanConditionNumber"
                    placeholder="请输入规划条件编号（建设项目规划设计条件通知书的审批号）"
                    :disabled="BaseInfo.StateCode === 4"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="项目类型：" prop="ProjectType">
                  <el-select
                    v-model="form1.ProjectType"
                    placeholder="请选择项目类型"
                    style="width: 100%"
                    :disabled="BaseInfo.StateCode === 4"
                  >
                    <el-option
                      v-for="item in projectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="是否自建房：" prop="IsZJ">
                  <el-select
                    v-model="form1.IsZJ"
                    placeholder="请选择是否自建房"
                    style="width: 100%"
                    :disabled="BaseInfo.StateCode === 4"
                  >
                    <el-option
                      v-for="item in zjOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="申请人类型：" prop="PersonType">
                  <el-select
                    v-model="form1.PersonType"
                    placeholder="请选择申请人类型"
                    style="width: 100%"
                    :disabled="BaseInfo.StateCode === 4"
                  >
                    <el-option
                      v-for="item in personTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="统一项目代码：" prop="UnifiedProjectCode">
                  <el-input
                    v-model.trim="form1.UnifiedProjectCode"
                    placeholder="请输入统一项目代码（发改产项批文中的项目代码）"
                    :disabled="BaseInfo.StateCode === 4"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item
                label="土地用途："
                prop="ProjectLandUse"
              >
                <dynamic-table
                  ref="landuseTable"
                  class="table-container"
                  :table-header="landuseTableHeader"
                  :table-data="form1.ProjectLandUse"
                  :default-props="tableProps"
                  :show-pagination="false"
                >
                  <el-table-column
                    prop="action"
                    label="操作"
                    width="180"
                    fixed="right"
                    align="center"
                  >
                    <template slot-scope="{ row, $index }">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="storeLandUse(row, $index)"
                      >编辑</el-button>
                      <el-popconfirm
                        v-if="BaseInfo.StateCode !== 4"
                        title="确认删除?"
                        @onConfirm="delLandUse(row, $index)"
                      >
                        <el-button
                          slot="reference"
                          size="mini"
                          type="text"
                          icon="el-icon-delete"
                          class="ml-10"
                        >删除</el-button>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </dynamic-table>
                <div
                  v-if="BaseInfo.StateCode !== 4"
                  class="table-add-btn"
                  @click="setLandUseStoreDialogVisible(true)"
                >
                  <i class="el-icon-plus" />添加土地用途
                </div>
              </el-form-item>
            </el-row>
          </description>
          <description title="业务信息" style="margin-top: -10px">
            <el-form-item
              label-width="190px"
              label="工程规划许可证："
              prop="ProjectPlanPermission"
            >
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="form1.ProjectPlanPermission"
                :default-props="tableProps"
                :show-pagination="false"
              >
                <el-table-column
                  prop="action"
                  label="操作"
                  width="180"
                  fixed="right"
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index)"
                    >查看</el-button>
                    <el-button
                      v-if="row.Add && BaseInfo.StateCode !== 4"
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="storeLicence(row, $index)"
                    >编辑</el-button>
                    <el-popconfirm
                      v-if="BaseInfo.StateCode !== 4"
                      title="确认删除?"
                      @onConfirm="delLicence(row, $index)"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        class="ml-10"
                      >删除</el-button>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </dynamic-table>
              <div
                v-if="BaseInfo.StateCode !== 4"
                class="table-add-btn"
                @click="setLicenceGetDialogVisible(true)"
              >
                <i class="el-icon-plus" />添加工程规划许可证
              </div>
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="不动产单元号（宗地号）："
              prop="GroundCode"
            >
              <el-input
                v-model.trim="form1.GroundCode"
                placeholder="宗地号为19位宗地统一代码，如：450103001001GB00001"
              />
            </el-form-item>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item
              label-width="190px"
              label="工程规划许可证件："
              prop="ProjectLicenceImg"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.ProjectLicenceImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '工程规划许可证件',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ProjectLicenceImg')"
                @delete="del($event, 'form1', 'ProjectLicenceImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="不动产权属证书："
              prop="Cert"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.Cert"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '不动产权属证书',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Cert')"
                @delete="del($event, 'form1', 'Cert')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="不动产权证书："
              prop="PropertyCertificate"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.PropertyCertificate"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '不动产权证书',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'PropertyCertificate')"
                @delete="del($event, 'form1', 'PropertyCertificate')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="土地出让合同/土地划拨决定书："
              prop="LandContract"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.LandContract"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '土地出让合同/土地划拨决定书',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'LandContract')"
                @delete="del($event, 'form1', 'LandContract')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
              <div class="upload-tip">
                <i class="el-icon-warning-outline" />
                <span>温馨提示：二选一，只需要上传土地出让合同或者土地划拨决定书</span>
              </div>
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="规划审批版方案文本："
              prop="SchemeText"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.SchemeText"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '规划审批版方案文本',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SchemeText')"
                @delete="del($event, 'form1', 'SchemeText')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="经规划审批的总平图："
              prop="SitePlan"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.SitePlan"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '经规划审批的总平图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SitePlan')"
                @delete="del($event, 'form1', 'SitePlan')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="经规划审批的总平图扫描件："
              prop="SitePlanScan"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.SitePlanScan"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '经规划审批的总平图扫描件',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SitePlanScan')"
                @delete="del($event, 'form1', 'SitePlanScan')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="各单体初步核实证明："
              prop="VerificationCert"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.VerificationCert"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '各单体初步核实证明',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'VerificationCert')"
                @delete="del($event, 'form1', 'VerificationCert')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="城市基础设施配套费结清证明材料："
              prop="CityConstruction"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.CityConstruction"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '城市基础设施配套费结清证明材料',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'CityConstruction')"
                @delete="del($event, 'form1', 'CityConstruction')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="土地用途及占比相关佐证材料（国有建设用地出让合同、划拨决定书、不动产权证、土地使用证等）："
              prop="LandUse"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.LandUse"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '土地用途',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'LandUse')"
                @delete="del($event, 'form1', 'LandUse')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="园林绿化主管部门出具的工程质量监督报告"
              prop="QualityControl"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.QualityControl"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '园林绿化主管部门出具的工程质量监督报告',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'QualityControl')"
                @delete="del($event, 'form1', 'QualityControl')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="管网核实成果图"
              prop="ResultMap"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.ResultMap"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '管网核实成果图',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ResultMap')"
                @delete="del($event, 'form1', 'ResultMap')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="项目跟踪卡："
              prop="ProjectTrackCard"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.ProjectTrackCard"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '项目跟踪卡',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ProjectTrackCard')"
                @delete="del($event, 'form1', 'ProjectTrackCard')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="规划设计条件通知书："
              prop="PlanNotice"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.PlanNotice"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '规划设计条件通知书',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'PlanNotice')"
                @delete="del($event, 'form1', 'PlanNotice')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="建设工程质量竣工预验收结论意见："
              prop="AcceptComment"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.AcceptComment"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '建设工程质量竣工预验收结论意见',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'AcceptComment')"
                @delete="del($event, 'form1', 'AcceptComment')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="190px"
              label="有违法建筑经处理后的，提交处罚决定书及发票"
              prop="PunishmentDecision"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.PunishmentDecision"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '处罚决定书及发票',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'PunishmentDecision')"
                @delete="del($event, 'form1', 'PunishmentDecision')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label-width="190px" label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar / dwg"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2 && developer">
        <!-- 委托测绘单位 -->
        <mapping-company-list
          class="mb-20"
          :business-class="BaseInfo.BusinessClass"
          :business-id="businessID"
          :current-action-id="CurrentAction.ID"
          :flow-name="FlowInfo.FlowName"
          @select="refreshStep"
        />
      </div>
      <div v-if="currentStep === 3">
        <!-- 上传测绘成果 -->
        <tip
          v-if="developer"
          class="mb-20 font-20 text-center bold"
        >等待测绘单位汇交并确认测绘成果...</tip>
        <el-form
          v-if="mappingCompany"
          ref="form3"
          class="mb-40"
          :model="form3"
          :rules="rules3"
          label-width="100px"
        >
          <!-- 提示语 -->
          <tip
            v-if="!ContentInfo.DataCheckID"
            class="mb-20"
          >请上传测绘成果</tip>
          <template v-else>
            <div v-if="ContentInfo.DataCheckState === 0" class="mb-20">
              <tip type="default" class="mb-20">
                测绘成果已上传完成，
                <i
                  class="el-icon-loading mr-5"
                />系统正在对成果进行检查，检查大概需要5分钟，请耐心等待结果
              </tip>
              <timing-progress-bar :is-finished="surveyResultCheckFinished" />
              <!-- 检查项DEMO-start -->
              <div class="mt-20">
                <el-collapse>
                  <el-collapse-item title="点击此处展开可查看检查项" name="1">
                    <table
                      class="table"
                      style="width: 100%"
                      cellpadding="0"
                      cellspacing="0"
                    >
                      <tr>
                        <th>
                          <div>项目名称</div>
                        </th>
                        <th>检查状态</th>
                      </tr>
                      <template v-if="checkData.checkOne.length">
                        <tr
                          v-for="(item, index) in checkData.checkOne"
                          :key="'checkOne' + index"
                        >
                          <td>
                            <div>{{ item.name }}</div>
                          </td>
                          <td>
                            <div class="status-font">
                              <i class="el-icon-loading mr-5" /><span>检查中...</span>
                            </div>
                          </td>
                        </tr>
                      </template>
                      <tr v-else>
                        <td colspan="2">暂检查项</td>
                      </tr>
                    </table>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <!-- 检查项DEMO-end -->
            </div>
            <div v-if="ContentInfo.DataCheckState === 1" class="mb-20">
              <tip type="success">
                您上传的测绘成果符合南宁市不动产测绘成果格式要求，可<template
                  v-if="surveyBusinessManager || surveyAdmin"
                >联系本单位注册测绘师</template>登录邕e登App进入“我的授权”模块刷脸确认测绘成果。若想修改，请重新上传测绘成果。
              </tip>
              <!-- 检查项DEMO-start -->
              <div class="mt-20">
                <el-collapse>
                  <el-collapse-item title="点击此处展开可查看检查项" name="1">
                    <table
                      class="table"
                      style="width: 100%"
                      cellpadding="0"
                      cellspacing="0"
                    >
                      <tr>
                        <th>
                          <div>项目名称</div>
                        </th>
                        <th>检查状态</th>
                      </tr>
                      <template v-if="checkData.checkOne.length">
                        <tr
                          v-for="(item, index) in checkData.checkOne"
                          :key="'checkOne' + index"
                        >
                          <td>
                            <div>{{ item.name }}</div>
                          </td>
                          <td>
                            <div class="status-font">
                              <i class="el-icon-circle-check mr-5" /><span>通过</span>
                            </div>
                          </td>
                        </tr>
                      </template>
                      <tr v-else>
                        <td colspan="2">暂检查项</td>
                      </tr>
                    </table>
                  </el-collapse-item>
                </el-collapse>
              </div>
              <!-- 检查项DEMO-end -->
            </div>

            <tip
              v-if="ContentInfo.DataCheckState === 2"
              type="error"
              class="mb-20"
            >
              您上传的测绘成果未能通过检查，请
              <span
                class="link"
                @click="downloadSurveyResultErrorReport('不动产实核测绘')"
              >点击此处</span>下载成果检查报告，待整改后重新上传
            </tip>
          </template>
          <!-- 附件信息 -->
          <el-collapse value="1" class="mb-20">
            <el-collapse-item title="申请信息附件" name="1">
              <attachment-info
                v-if="mappingCompany"
                class="mt-10"
                :base-info="BaseInfo"
                :step="currentStep"
                :step-list="stepList"
                :attachment-data="form1"
                :actions-infos="ActionsInfos"
                :current-action="CurrentAction"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-collapse-item>
          </el-collapse>
          <!-- 上传按钮 -->
          <el-row class="mb-20">
            <el-col :xs="24" :sm="11" :md="11" :lg="11" :xl="11">
              <el-form-item label-width="260px" label="竣工用地面积（该地块用地面积）：" prop="CompletionLandArea">
                <el-input
                  v-model="form3.CompletionLandArea"
                  placeholder="请输入竣工用地面积"
                  style="width: 90%"
                >
                  <template slot="append">㎡</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="11" :md="11" :lg="11" :xl="11" :offset="2">
              <el-form-item label-width="260px" label="竣工总建筑面积（单体总建筑面积）：" prop="CompletionBuildingArea">
                <el-input
                  v-model="form3.CompletionBuildingArea"
                  placeholder="请输入竣工总建筑面积"
                  style="width: 90%"
                >
                  <template slot="append">㎡</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-if="
              !ContentInfo.DataCheckID ||
                (ContentInfo.DataCheckID && ContentInfo.DataCheckState > 0)
            "
            :gutter="12"
          >
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label-width="145px" label="测绘成果：" prop="Data">
                <mdb-upload
                  :id="businessID"
                  :file-list="form3.Data"
                  :file-size="102400"
                  :upload-url="resultUploadUrl"
                  @upload-success="resultUpload"
                />
              </el-form-item>
            </el-col>
            <el-col
              class="example-container"
              :xs="24"
              :sm="12"
              :md="12"
              :lg="12"
              :xl="12"
            >
              <p class="example-hint">温馨提示：</p>
              <ol class="example-list">
                <li class="example-list-item">
                  所上传的测绘成果须符合
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment(
                        '全面核实工程数据汇交标准.docx'
                      )
                    "
                  >《南宁市不动产全面核实工程数据汇交标准》</a>
                </li>
                <li class="example-list-item">
                  所上传的测绘成果文件参考样例：
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment(
                        '规划条件全面核实业务（测绘成果样例）.mdb'
                      )
                    "
                  >规划条件全面核实业务（测绘成果样例）.mdb</a>
                </li>
                <li class="example-list-item">
                  如有疑问，请联系南宁市不动产登记中心测绘管理部，联系电话：
                  <a class="example-list-item__phone">4306662</a>
                </li>
              </ol>
            </el-col>
          </el-row>
          <!-- 现场照片 -->
          <tip
            v-if="!form3.SiteOutFacadeImg.length"
            class="mb-20"
          >请上传现场照片</tip>
          <tip
            v-else
            type="success"
            class="mb-20"
          >您的现场照片已上传完成</tip>
          <el-form-item
            label-width="145px"
            label="现场照片："
            prop="SiteOutFacadeImg"
          >
            <list-upload
              v-if="!isReplaceMdb()"
              file-format="png / jpg / gif / bmp / pdf"
              :file-list="form3.SiteOutFacadeImg"
              :file-size="102400"
              :data="{
                BusinessType: BaseInfo.BusinessType,
                BusinessID: businessID,
                AttachmentType: '项目成果附件',
                AttachmentCategories: '现场照片',
              }"
              :on-check-format="checkImgAndPDF"
              @upload-success="upload($event, 'form3', 'SiteOutFacadeImg')"
              @delete="del($event, 'form3', 'SiteOutFacadeImg')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
            <file-list
              v-else
              :file-list="form3.SiteOutFacadeImg"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-form-item>
          <!-- 全面核实报告 -->
          <tip
            v-if="!form3.ActualReport.length"
            class="mb-20"
          >请上传全面核实报告</tip>
          <tip
            v-else
            type="success"
            class="mb-20"
          >您的全面核实报告已上传完成</tip>
          <el-form-item
            label-width="145px"
            label="全面核实报告："
            prop="ActualReport"
          >
            <list-upload
              file-format="pdf"
              :file-list="form3.ActualReport"
              :file-size="102400"
              :data="{
                BusinessType: BaseInfo.BusinessType,
                BusinessID: businessID,
                AttachmentType: '项目成果附件',
                AttachmentCategories: '全面核实报告',
              }"
              :on-check-format="checkPDF"
              @upload-success="upload($event, 'form3', 'ActualReport')"
              @delete="del($event, 'form3', 'ActualReport')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-form-item>

        </el-form>
      </div>
      <div v-if="currentStep === 4 && mappingCompany" class="mb-20">
        <tip
          class="font-20 text-center bold"
        >测绘成果已确认通过，等待业主单位验收成果...</tip>
      </div>
      <div v-if="currentStep === 5 && BaseInfo.StateCode !== 2" class="mb-20">
        <tip class="font-20 text-center bold">
          成果验收完成，
          <i
            class="el-icon-loading mr-5"
          />已提交南宁市不动产登记中心检查（8个工作日）
        </tip>
      </div>
      <!-- 测绘成果下载 -->
      <result
        v-if="showResult()"
        class="mb-20"
        :name="FlowInfo.FlowName"
        :survey-result="surveyResult"
        :property-info="propertyInfo"
        :condition-verificate-info="!isOnlySC() ? conditionVerificateInfo : null"
        :site-out-facade-img="form3.SiteOutFacadeImg"
        :actual-report="form3.ActualReport"
        :audit-feedback="auditFeedback"
        :building-table-info="ContentInfo.BuildingTableInfo"
        :project-result-info="ContentInfo.ProjectResultInfo"
        :step="currentStep"
        :approval-opinion-table-info="ApprovalOpinionTableInfo"
        :flow-steps-info="FlowStepsInfo"
        :business-class="businessClass"
        :completion-land-area="ContentInfo.CompletionLandArea"
        :completion-building-area="ContentInfo.CompletionBuildingArea"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep == 3">
        <el-button
          v-if="developer && BaseInfo.StateCode === 0"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="ContentInfo.DataCheckState === -3"
          @click="reSelectSurveyCompany()"
        >
          <i class="iconfont icon-back mr-5" />重新委托测绘单位
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="disabledBack() || isReplaceMdb()"
          @click="setReasonDialogVisible('退回业务', true)"
        >
          <i class="iconfont icon-back mr-5" />退回业务
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          type="success"
          :disabled="disabledAccept()"
          @click="submitAccept(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交验收
        </el-button>
      </template>
      <template
        v-if="currentStep === 4 && developer && BaseInfo.StateCode === 1"
      >
        <el-button
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('验收不通过', true)"
        >
          <i class="iconfont icon-back mr-5" />验收不通过
        </el-button>
        <el-button
          class="ml-10"
          :loading="acceptBtn.loading"
          type="success"
          @click="confirmAccept()"
        >
          <i class="el-icon-check mr-5" />{{ acceptText }}
        </el-button>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2 || (currentStep === 1 && mappingCompany)"
        :base-info="BaseInfo"
        :content-info="ContentInfo"
        :attachment-data="form1"
        :name="FlowInfo.FlowName"
        :step="currentStep"
        :step-list="stepList"
        :actions-infos="ActionsInfos"
        :current-action="CurrentAction"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
        @view-licence="viewLicence"
      />
    </template>
    <!-- 额外内容 -->
    <template slot="extra">
      <!-- 工程规划许可证预览 -->
      <el-dialog
        title="查看工程规划许可证"
        :visible="licenceViewDialog.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
        width="1100px"
        @close="licenceViewDialog.visible = false"
      >
        <project-licence-view
          :row="licenceViewDialog.row"
          :index="licenceViewDialog.index"
        />
      </el-dialog>
      <!-- 获取工程规划许可证 -->
      <project-licence-get-dialog
        :visible.sync="licenceGetDialog.visible"
        :list="form1.ProjectPlanPermission"
        :business-id="businessID"
        :business-class="BaseInfo.BusinessClass"
        @add-lience="storeLicence(null, -1)"
        @submit="licenceGetSuccess"
        @close="setLicenceGetDialogVisible(false)"
      />
      <!-- 存储工程规划许可证 -->
      <project-licence-store-dialog
        :visible.sync="licenceStoreDialog.visible"
        :row="licenceStoreDialog.row"
        :index="licenceStoreDialog.index"
        :list="form1.ProjectPlanPermission"
        @submit="licenceStoreSuccess"
        @close="setLicenceStoreDialogVisible(false)"
      />
      <!-- 存储土地用途 -->
      <project-land-use-store-dialog
        :visible.sync="landuseStoreDialog.visible"
        :row="landuseStoreDialog.row"
        :index="landuseStoreDialog.index"
        :list="form1.ProjectLandUse"
        @submit="landuseStoreSuccess"
        @close="setLandUseStoreDialogVisible(false)"
      />
      <!-- 提交验收，选择注册测绘师弹窗 -->
      <select-registered-surveyor
        :visible.sync="selectRSurveyorDialog.visible"
        :business-id="BaseInfo.ID"
        :action-id="CurrentAction.ID"
        :flow-name="FlowInfo.FlowName"
        :completion-land-area="form3.CompletionLandArea"
        :completion-building-area="form3.CompletionBuildingArea"
        @select="getBusiness"
        @close="setSelectRSurveyorDialogVisible(false)"
      />
      <!-- 承诺书弹窗 -->
      <count-down-dialog
        :visible.sync="countDownDialog.visible"
        :title="countDownDialog.title"
        :count-down-type="countDownDialog.countDownType"
        :count-down-btn="countDownDialog.countDownBtn"
        :count-down-time="countDownDialog.countDownTime"
        :business-class="countDownDialog.businessClass"
        :width="countDownDialog.width"
        :class-name="countDownDialog.className"
        :is-promise="countDownDialog.isPromise"
        @submit="confirmPromiseInfo"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import TimingProgressBar from "components/business/TimingProgressBar/index.vue";
import ViewInfo from "./view-info/index.vue";
import AttachmentInfo from "./view-info/attachmentInfo.vue";
import Result from "./result.vue";
import CountDownDialog from "components/business/CountDownDialog/index.vue";
// mixins
import BusinessMixin from "mixins/business/index.js";
import CheckLineMixin from "mixins/business/check-line.js";
import SurveyResultMixin from "mixins/business/survey-result.js";
import SelectMappingCompanyMixin from "mixins/business/select-mapping-company.js";
import SelectRegisteredSurveyorMixin from "mixins/business/select-registered-survey.js";
// 校验
import {
  validateBusinessName,
  validateCompanyAddress,
  validateLegalPersonName,
  validateLegalPersonNumber,
  validateLegalPersonPhone,
  validatePlanConditionNumber,
  validateProjectType,
  validatePersonType,
  validateIsZJ,
  validateAttachment,
  validateGroundCode,
} from "utils/form.js";

export default {
  name: "OverallActualMapping",
  components: {
    TimingProgressBar,
    ViewInfo,
    AttachmentInfo,
    Result,
    CountDownDialog,
  },
  mixins: [
    BusinessMixin,
    CheckLineMixin,
    SurveyResultMixin,
    SelectMappingCompanyMixin,
    SelectRegisteredSurveyorMixin,
  ],
  data() {
    return {
      //初审意见和外业意见
      ApprovalOpinionTableInfo:[],
      //云平台步骤数据
      FlowStepsInfo:[],
      //流程类型
      businessClass: null,

      //项目类型选项
      projectTypeOptions: [
        {
          value: "建筑类",
          label: "建筑类",
        },

        {
          value: "工程管线类",
          label: "工程管线类",
        },
        {
          value: "交通类",
          label: "交通类",
        }
      ],

      //申请人类型选项
      personTypeOptions: [
        {
          value: "法人",
          label: "法人",
        },

        {
          value: "个人",
          label: "个人",
        }
      ],

      //是否自建房选项
      zjOptions: [
        {
          value: "否",
          label: "否",
        },

        {
          value: "是",
          label: "是",
        }
      ],      
      
      // 承诺书弹窗
      countDownDialog: {
        visible: false,
        businessClass: null,
        title: "承诺书",
        countDownType: "承诺书",
        countDownBtn: "我承诺",
        countDownTime: process.env.VUE_APP_ENV === "development" ||  process.env.VUE_APP_ENV === "practice" ? 3 : 15,
        width: "600px",
        className: "promise",
        isPromise: true,
      },
      isConfirm: false,
      acceptText: "验收完成",
      // 业务内容信息，不同业务内容不同
      ContentInfo: {
        ProjectPlanPermission: [],
      },
      // 步骤1
      form1: {
        BusinessName: null,
        CompanyAddress: null,
        LegalPersonName: null,
        LegalPersonNumber: null,
        LegalPersonPhone: null,
        PlanConditionNumber: null,
        UnifiedProjectCode: null,
        ProjectType: null,
        PersonType: null,
        IsZJ: null,        
        GroundCode: null,
        ProjectPlanPermission: [],
        ProjectLandUse:[],
        QualityControl:[],
        ResultMap:[],
        ProjectLicenceImg: [],
        ProjectLicenceImg: [],
        PropertyCertificate: [],
        SitePlan: [],
        SitePlanScan: [],
        Cert: [],
        ProjectTrackCard: [],
        PlanNotice:[],
        AcceptComment: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],


        CompanyAddress: [
          { required: true, validator: validateCompanyAddress, trigger: "blur" },
        ],
        LegalPersonName: [
          { required: true, validator: validateLegalPersonName, trigger: "blur" },
        ],
        LegalPersonNumber: [
          { required: true, validator: validateLegalPersonNumber, trigger: "blur" },
        ],
        LegalPersonPhone: [
          { required: true, validator: validateLegalPersonPhone, trigger: "blur" },
        ],
        PlanConditionNumber: [
          { required: true, validator: validatePlanConditionNumber, trigger: "blur" },
        ],
        ProjectType: [
          { required: true, validator: validateProjectType, trigger: "change" },
        ],
        PersonType: [
          { required: true, validator: validatePersonType, trigger: "change" },
        ],
        IsZJ: [
          { required: true, validator: validateIsZJ, trigger: "change" },
        ],

        GroundCode: [
          { required: true, validator: validateGroundCode, trigger: "blur" },
        ],
        ProjectPlanPermission: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !this.form1.ProjectPlanPermission ||
                !this.form1.ProjectPlanPermission.length
              ) {
                callback(new Error("请添加工程规划许可证"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        ProjectLandUse: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !this.form1.ProjectLandUse ||
                !this.form1.ProjectLandUse.length
              ) {
                callback(new Error("请添加土地用途"));
              } else {
                  var total = 0;
                  $.each(this.form1.ProjectLandUse, function(index, item){
                    total += parseInt(item.percent);
                  });
                  if (total !== 100) {
                    callback(new Error("土地用途总占用比例必须等于100"));
                  }
                  else{
                    callback();
                  }                
              }
            },
            trigger: "change",
          },
        ],
        QualityControl: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "QualityControl",
                "园林绿化主管部门出具的工程质量监督报告"
              ),
            trigger: "change",
          },
        ],
        ResultMap: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ResultMap",
                "管网核实成果图"
              ),
            trigger: "change",
          },
        ],
        ProjectLicenceImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ProjectLicenceImg",
                "工商许可证件"
              ),
            trigger: "change",
          },
        ],
        PropertyCertificate: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "PropertyCertificate",
                "不动产权证书"
              ),
            trigger: "change",
          },
        ],
        LandContract: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "LandContract",
                "土地出让合同/土地划拨决定书"
              ),
            trigger: "change",
          },
        ],
        SitePlan: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "SitePlan",
                "经规划审批的总平图"
              ),
            trigger: "change",
          },
        ],
        SitePlanScan: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "SitePlanScan",
                "经规划审批的总平图扫描件"
              ),
            trigger: "change",
          },
        ],
        VerificationCert: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "VerificationCert",
                "各单体初步核实证明"
              ),
            trigger: "change",
          },
        ],
        Cert: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "Cert",
                "不动产权属证书"
              ),
            trigger: "change",
          },
        ],
        SchemeText: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "SchemeText",
                "规划审批版方案文本"
              ),
            trigger: "change",
          },
        ],
        CityConstruction: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "CityConstruction",
                "城市基础设施配套费结清证明材料"
              ),
            trigger: "change",
          },
        ],
        LandUse: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "LandUse",
                "土地用途及占比相关佐证材料"
              ),
            trigger: "change",
          },
        ],
      },
      // 步骤3
      form3: {
        Data: [],
        SiteOutFacadeImg: [],
        ActualReport: [],
        CompletionLandArea: '',
        CompletionBuildingArea: '',
      },
      rules3: {
        Data: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Data",
                "测绘成果"
              ),
            trigger: "change",
          },
        ],
        CompletionLandArea: [
          { 
            required: true, 
            message: '请输入竣工用地面积，填写的数值不能为0及负数且最多只能保留4位小数', 
            trigger: 'blur' 
          },
          { 
            pattern: /^(?!0$)(?!0\.0*$)([1-9]\d*|0)(\.\d{1,4})?$/, 
            message: '请输入大于0的数字，最多保留4位小数', 
            trigger: 'blur'
          }
        ],
        CompletionBuildingArea: [
          { 
            required: true, 
            message: '请输入竣工总建筑面积，填写的数值不能为0及负数且最多只能保留4位小数', 
            trigger: 'blur' 
          },
          { 
            pattern: /^(?!0$)(?!0\.0*$)([1-9]\d*|0)(\.\d{1,4})?$/, 
            message: '请输入大于0的数字，最多保留4位小数', 
            trigger: 'blur'
          }
        ],
        SiteOutFacadeImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "SiteOutFacadeImg",
                "现场照片"
              ),
            trigger: "change",
          },
        ],
        ActualReport: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "ActualReport",
                "全面核实报告"
              ),
            trigger: "change",
          },
        ],
      },
      conditionVerificateInfo: null,
      /* 检查项DEMO-start */
      checkData: {
        checkOne: [
          {
            name: "绿地信息属性表是否符合数据汇交标准",
          },
          {
            name: "停车位属性表是否符合数据汇交标准",
          },
          {
            name: "规划用途属性表是否符合数据汇交标准",
          },
          {
            name: "用地红线属性表是否符合数据汇交标准",
          },
          {
            name: "净用地红线属性表是否符合数据汇交标准",
          },
          {
            name: "公共服务设施属性表是否符合数据汇交标准",
          },
          {
            name: "标高标注属性表是否符合数据汇交标准",
          },
          {
            name: "四至尺寸标注属性表是否符合数据汇交标准",
          },
          {
            name: "车行出入口信息属性表是否符合数据汇交标准",
          },
          {
            name: "人行出入口信息属性表是否符合数据汇交标准",
          },
          {
            name: "地下室出入口信息属性表是否符合数据汇交标准",
          },
          {
            name: "垃圾收集点信息属性表是否符合数据汇交标准",
          },
          {
            name: "FC_自然幢信息属性表是否符合数据汇交标准",
          },
          {
            name: "外墙线属性表是否符合数据汇交标准",
          },
          {
            name: "规划许可基本信息表是否符合数据汇交标准",
          },
          {
            name: "场地标高信息属性表是否符合数据汇交标准",
          },
          {
            name: "总平图项目指标信息是否符合数据汇交标准",
          },
          {
            name: "小区核实信息表是否符合数据汇交标准",
          },
          {
            name: "全面指标核实是否符合数据汇交标准",
          },
        ]
      },
      /* 检查项DEMO-end */     
    };
  },
  destroyed() {
    this.clearTimer(this.resultTimer);
  },
  methods: {
    // 额外处理请求数据
    handleApiData(Data) {
      const { ProjectPlanPermission, BuildingTableInfo, ProjectResultInfo, ProjectLandUse,
              CompanyAddress, LegalPersonName, LegalPersonNumber, LegalPersonPhone, PlanConditionNumber,
              UnifiedProjectCode, ProjectType, PersonType, IsZJ } =
        Data.ContentInfo;
      this.ContentInfo = {
        ...Data.ContentInfo,
        ProjectPlanPermission: ProjectPlanPermission
          ? JSON.parse(ProjectPlanPermission)
          : [],
        ProjectLandUse: ProjectLandUse
          ? JSON.parse(ProjectLandUse)
          : [],
        BuildingTableInfo: BuildingTableInfo
          ? JSON.parse(BuildingTableInfo)
          : [],
        ProjectResultInfo: ProjectResultInfo
          ? JSON.parse(ProjectResultInfo)
          : [],  
        CompanyAddress: CompanyAddress,
        LegalPersonName: LegalPersonName,
        LegalPersonNumber: LegalPersonNumber,
        LegalPersonPhone: LegalPersonPhone,
        PlanConditionNumber: PlanConditionNumber,
        UnifiedProjectCode: UnifiedProjectCode,
        ProjectType: ProjectType,
        PersonType: PersonType,
        IsZJ: IsZJ
      };
      this.ApprovalOpinionTableInfo = this.getApprovalOpinionTableInfo();
      this.FlowStepsInfo = this.getFlowStepsInfo();
      this.businessClass = this.BaseInfo.BusinessClass;      
    },
    // 处理表单数据
    handleFormData(currentStep) {
      const {
        BaseInfo,
        ContentInfo,
        Attachments,
        mappingCompany,
        getSurveyResultCheckState,
      } = this;
      const { BusinessName } = BaseInfo;
      const { DataCheckID, DataCheckState, GroundCode, ProjectPlanPermission, ProjectLandUse,
              CompanyAddress, LegalPersonName, LegalPersonNumber, LegalPersonPhone, PlanConditionNumber, 
              UnifiedProjectCode, ProjectType, PersonType, IsZJ } =
        ContentInfo;

      // 处理附件
      let ProjectLicenceImg = [];
      let PropertyCertificate = [];
      let LandContract = [];
      let SitePlan = [];
      let SitePlanScan = [];
      let Cert = [];
      let ProjectTrackCard = [];
      let PlanNotice=[];
      let AcceptComment = [];
      let SchemeText = [];
      let VerificationCert = [];
      let CityConstruction = [];
      let PunishmentDecision = [];
      let LandUse = [];
      let QualityControl=[];
      let ResultMap=[];
      let Others = [];
      let SiteOutFacadeImg = [];
      let ActualReport = [];
      this.auditFeedback = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "工程规划许可证件":
                ProjectLicenceImg.push(e);
                break;
              case "经规划审批的总平图":
                SitePlan.push(e);
                break;
              case "经规划审批的总平图扫描件":
                SitePlanScan.push(e);
                break;
              case "不动产权属证书":
                Cert.push(e);
                break;
              case "不动产权证书":
                PropertyCertificate.push(e);
                break;
              case "土地出让合同/土地划拨决定书":
                LandContract.push(e);
                break;
              case "项目跟踪卡":
                ProjectTrackCard.push(e);
                break;
              case "规划设计条件通知书":
                PlanNotice.push(e);
                break;
              case "建设工程质量竣工预验收结论意见":
                AcceptComment.push(e);
                break;
              case "规划审批版方案文本":
                SchemeText.push(e);
                break;
              case "各单体初步核实证明":
                VerificationCert.push(e);
                break;
              case "城市基础设施配套费结清证明材料":
                CityConstruction.push(e);
                break;
              case "处罚决定书及发票":
                PunishmentDecision.push(e);
                break;
              case "土地用途":
                LandUse.push(e);
                break;
              case "园林绿化主管部门出具的工程质量监督报告":
                QualityControl.push(e);
                break;
              case "管网核实成果图":
                ResultMap.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "项目成果附件") {
            switch (e.AttachmentCategories) {
              case "现场照片":
                SiteOutFacadeImg.push(e);
                break;
              case "全面核实报告":
                ActualReport.push(e);
                break;
              case "不动产实核测绘成果":
              case "不动产实核业务成果":
              case "不动产测绘成果":
                this.surveyResult = e;
                break;
              case "竣工规划条件核实信息表":
                this.conditionVerificateInfo = e;
                break;
              case "不动产实核测绘成果审核反馈":
                this.auditFeedback.push(e);
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
        });
      }

      this.form1 = {
        BusinessName,
        CompanyAddress,
        LegalPersonName,
        LegalPersonNumber,
        LegalPersonPhone,
        PlanConditionNumber,
        UnifiedProjectCode,
        ProjectType,
        PersonType,
        IsZJ,
        GroundCode,
        ProjectPlanPermission,
        ProjectLandUse,
        QualityControl,
        ResultMap,
        ProjectLicenceImg,
        PropertyCertificate,
        LandContract,
        SitePlan,
        SitePlanScan,
        Cert,
        ProjectTrackCard,
        PlanNotice,
        AcceptComment,
        SchemeText,
        VerificationCert,
        CityConstruction,
        PunishmentDecision,
        LandUse,
        Others,
      };

      this.form3 = {
        // 判断Data是否有值，DataCheckState： 0 检查中  1 检查通过  2 检查不通过
        Data:
          DataCheckID && DataCheckState > 0
            ? [{ name: "hasResult", status: "success" }]
            : [],
        CompletionLandArea: this.form3.CompletionLandArea,
        CompletionBuildingArea: this.form3.CompletionBuildingArea,
        SiteOutFacadeImg,
        ActualReport,
      };

      // 审核中定时请求接口
      if (currentStep === 3 && mappingCompany) {
        if (DataCheckID && DataCheckState === 0) {
          getSurveyResultCheckState(DataCheckID);
        }
      }
    },
    // 显示成果数据
    showResult() {
      const { currentStep, mappingCompany, ContentInfo } = this;

      if (
        currentStep === 3 &&
        mappingCompany &&
        ContentInfo.DataCheckID &&
        ContentInfo.DataCheckState === 1
      ) {
        return true;
      }
      if (currentStep === 4 || currentStep === 5) {
        return true;
      }

      return false;
    },
    // 禁用退回、提交验收和确认通过（测试按钮）
    disabledBack() {
      const { DataCheckID, DataCheckState } = this.ContentInfo;

      // 正在在检查成果
      if (DataCheckID && DataCheckState === 0) {
        return true;
      }
      return false;
    },
    //单独禁用验收按钮
    disabledAccept() {
    const { DataCheckID, DataCheckState } = this.ContentInfo;
    // 正在在检查成果
    if (DataCheckID && DataCheckState === 0) {
      return true;
    }
    if (DataCheckID && DataCheckState === 2) {
      return true;
    }
    return false;
  },
    //验收完成
    confirmAccept() {
      if (this.isConfirm) {
        this.accept();
      } else {
        this.countDownDialog.businessClass = this.BaseInfo.BusinessClass;
        this.countDownDialog.visible = true;
      }
    },
    //承诺书
    confirmPromiseInfo(isConfirm) {
      this.isConfirm = isConfirm;
      if (this.isConfirm) {
        this.acceptText = "提交备案";
      }
    },
    //判断当前流程是否属于mdb替换退回
    isReplaceMdb() {
      const { CurrentAction } = this;
      if (
        CurrentAction.ExtendAttr !== null &&
        CurrentAction.ExtendAttr !== "" &&
        CurrentAction.ExtendAttr !== undefined
      ) {
        if (
          CurrentAction.ExtendAttr.UploadMDBOnly !== null &&
          CurrentAction.ExtendAttr.UploadMDBOnly !== "" &&
          CurrentAction.ExtendAttr.UploadMDBOnly !== undefined
        ) {
          return CurrentAction.ExtendAttr.UploadMDBOnly;
        }
      }
      return false;
    },
    //初审意见和外业意见
    getApprovalOpinionTableInfo() {
      const { CurrentAction } = this;
      if (
        CurrentAction != undefined &&
        CurrentAction.ExtendAttr !== null &&
        CurrentAction.ExtendAttr !== "" &&
        CurrentAction.ExtendAttr !== undefined
      ) {
        if (
          CurrentAction.ExtendAttr.XCloudSPYJ !== null &&
          CurrentAction.ExtendAttr.XCloudSPYJ !== "" &&
          CurrentAction.ExtendAttr.XCloudSPYJ !== undefined
        ) {
          if (CurrentAction.ExtendAttr.XCloudSPYJ.Message == '') {            
            return CurrentAction.ExtendAttr.XCloudSPYJ.Data;
          }
        }
      }

      return [];
    },
    //获取云平台流程步骤
    getFlowStepsInfo() {
      const { CurrentAction } = this;
      if (
        CurrentAction != undefined &&
        CurrentAction.ExtendAttr !== null &&
        CurrentAction.ExtendAttr !== "" &&
        CurrentAction.ExtendAttr !== undefined
      ) {
        if (
          CurrentAction.ExtendAttr.XCloudSteps !== null &&
          CurrentAction.ExtendAttr.XCloudSteps !== "" &&
          CurrentAction.ExtendAttr.XCloudSteps !== undefined
        ) {
          if (CurrentAction.ExtendAttr.XCloudSteps.Message == '') {            
            return CurrentAction.ExtendAttr.XCloudSteps.Data;
          }
        }
      }

      return [];
    },
    //是否仅实测
    isOnlySC() {
      const { CurrentAction } = this;
      if (
        CurrentAction != undefined &&
        CurrentAction.ExtendAttr !== null &&
        CurrentAction.ExtendAttr !== "" &&
        CurrentAction.ExtendAttr !== undefined
      ) {
        if (
          CurrentAction.ExtendAttr.IsOnlySC !== null &&
          CurrentAction.ExtendAttr.IsOnlySC !== "" &&
          CurrentAction.ExtendAttr.IsOnlySC !== undefined
        ) {
          return CurrentAction.ExtendAttr.IsOnlySC;
        }
      }
      return false;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}

/* 检查项DEMO-start */
.check-items {
  padding: 0 10px;
  background: #fff;
  border-radius: 4px;

  /deep/ .el-collapse {
    border: none;
  }
  /deep/ .el-collapse-item__header {
    border: none;
  }
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #E6A23C;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 5px;
  }
}

.status-font {
  color: $green;
}

.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;
  margin-bottom: 15px;

  .inside {
    border-top: none;
    border-left: none;

    tr:last-child {
      th,
      td {
        border-bottom: none;
      }
    }
  }

  th {
    text-align: center;
    background: #f8f8f8;
    color: #909399;
    min-width: 130px;
    width: 130px;
    max-width: 130px;
  }
  th,
  td {
    padding: 16px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    min-width: 130px;
  }

  .title {
    font-weight: bold;
    text-align: left;
  }
}
/* 检查项DEMO-end */
</style>
