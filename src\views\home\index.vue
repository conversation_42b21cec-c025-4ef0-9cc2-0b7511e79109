<template>
  <!-- eslint-disable -->
  <div class="home-container">
    <h1 class="home-title">
      <strong class="mr-10">欢迎使用</strong>{{ systemTitle }}
    </h1>
    <p class="home-subtitle">
      为了实现对各委办局相关业务部门提供正确、高效的测绘成果服务，
      我们在此为广大群众提供在线服务，欢迎大家使用，并对我们的服务提出宝贵的意见。
    </p>
    <div class="home-content">
      <div class="banner-user">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18">
            <!-- 轮播图 -->
            <div class="banner-container">
              <div style="position: relative">
                <div class="tp-banner">
                  <ul>
                    <li
                      v-for="(item, index) in bannerList"
                      :key="index"
                      data-transition="fade"
                      data-slotamount="7"
                      data-masterspeed="300"
                    >
                      <img
                        :src="item.imgUrl"
                        :data-lazyload="item.imgUrl"
                        data-fullwidthcentering="on"
                      />
                      <div
                        class="tp-caption medium_text lft"
                        data-x="90"
                        data-y="160"
                        data-speed="300"
                        data-start="500"
                        data-easing="easeOutExpo"
                      >
                        {{ item.title }}
                      </div>

                      <div
                        class="tp-caption large_text lfb"
                        data-x="90"
                        data-y="202"
                        data-speed="300"
                        data-start="800"
                        data-easing="easeOutExpo"
                      >
                        <div>{{ item.desc1 }}</div>
                        <div style="margin-top: 5px">{{ item.desc2 }}</div>
                      </div>

                      <div
                        class="tp-caption lfb"
                        data-x="90"
                        data-y="340"
                        data-speed="300"
                        data-start="1100"
                        data-easing="easeOutExpo"
                      >
                        <a
                          :href="`${baseUrl}/account/register`"
                          target="_blank"
                          class="link-btn"
                          >{{ item.linkText }}</a
                        >
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
            <!-- 登录部分 -->
            <div class="login-container">
              <div class="login-title">
                {{ personName ? "欢迎您" : "用户登录" }}
              </div>
              <template v-if="personName">
                <div class="login-content">
                  <p>尊敬的{{ personName }}：</p>
                  <p class="welcome">您已登录{{ systemTitle }}，欢迎使用。</p>
                </div>
                <div class="login-btn-container">
                  <el-button
                    type="primary"
                    @click="$router.push({ path: '/user/info' })"
                    >修改密码</el-button
                  >
                  <el-button type="warning" @click="logout">退出登录</el-button>
                </div>
              </template>
              <template v-else>
                <div class="login-content">
                  <el-form
                    :model="loginForm"
                    ref="loginForm"
                    :rules="rules"
                    label-width="70px"
                  >
                    <div class="from-label">手机号码</div>
                    <el-form-item label-width="0px" prop="username">
                      <el-input
                        v-model.trim="loginForm.username"
                        placeholder="请输入手机号码"
                        @keyup.enter.native="submitForm('loginForm')"
                      ></el-input>
                    </el-form-item>
                    <div class="from-label">登录密码</div>
                    <el-form-item label-width="0px" prop="password">
                      <el-input
                        type="password"
                        v-model.trim="loginForm.password"
                        placeholder="请输入密码"
                        @keyup.enter.native="submitForm('loginForm')"
                        >></el-input
                      >
                    </el-form-item>
                  </el-form>
                  <div class="password-container flex">
                    <el-checkbox v-model="remeberPwd" class="password-remember"
                      >记住密码</el-checkbox
                    >
                    <span
                      class="password-forget"
                      @click="$router.push({ path: '/reset-password' })"
                      >忘记密码？</span
                    >
                  </div>
                </div>
                <div class="login-btn-container">
                  <el-button
                    type="primary"
                    @click="submitForm('loginForm')"
                    :loading="loginLoading"
                    >登录</el-button
                  >
                  <el-button type="warning" @click="toPage('/account/register')"
                    >注册</el-button
                  >
                </div>
              </template>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 操作指南 -->
      <div class="operation-guide">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="flex">
            <div class="guide-container">
              <h4><i class="iconfont icon-book"></i>操作手册下载地址</h4>
              <div>
                <a target="_blank" type="primary" @click="openOperationManual()"
                  >{{ systemTitle }}用户手册</a
                >
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="flex">
            <div class="browser-container">
              <h4>
                <i class="iconfont icon-book"></i>网页版推荐使用谷歌浏览器
              </h4>
              <div>
                <a
                  href="https://www.google.cn/intl/zh-CN/chrome/?standalone=1"
                  target="_blank"
                  >下载谷歌浏览器</a
                >
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <hr />
    </div>
  </div>
</template>

<script>
/* eslint-disable */
// 工具
import { encrypt, decrypt } from "utils";
import Cookies from "js-cookie";
// vuex
import { mapGetters } from "vuex";
// Api方法
import Api from "api/user";

const urlPrefix = process.env.VUE_APP_URL_PREFIX;
const baseUrl = process.env.VUE_APP_BASE_URL;

export default {
  name: "Home",
  computed: {
    ...mapGetters(["token", "personName"]),
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_URL,
      bannerList: [
        {
          imgUrl: require("@/assets/banner-images/1.jpg"),
          title: "快捷服务",
          desc1: "网上实名注册用户",
          desc2: "在线办理业务",
          linkText: "现在就尝试一下",
        },
        {
          imgUrl: require("@/assets/banner-images/2.jpg"),
          title: "信息互动",
          desc1: "通过实时手机短信",
          desc2: "让您时刻掌握业务的办理情况",
          linkText: "马上注册",
        },
        {
          imgUrl: require("@/assets/banner-images/3.jpg"),
          title: "快速递交资料",
          desc1: "在线提交业务材料照片",
          desc2: "省去您来回奔走的麻烦",
          linkText: "在这里注册",
        },
      ],
      loginForm: {
        username: "",
        password: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
          {
            pattern: /^1(3|4|5|7|8|9)\d{9}$/,
            message: "手机号码格式有误",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入登录密码", trigger: "blur" },
        ],
      },
      loginLoading: false,
      remeberPwd: false,
      systemTitle: process.env.VUE_APP_SYSYTEM_TITLE,
    };
  },
  watch: {
    remeberPwd(val) {
      if (!val) {
        if (Cookies.get("username")) {
          Cookies.remove("username");
          Cookies.remove("password");
        }
      }
    },
  },
  mounted() {
    $(".tp-banner").revolution({
      delay: 9000,
      startwidth: 1170,
      startheight: 475,
    });
  },
  created() {
    // 判断本地存储用户名是否存在
    if (Cookies.get("username")) {
      // 获取本地存储的用户名和密码
      this.loginForm.username = Cookies.get("username");
      this.loginForm.password = decrypt(Cookies.get("password"));
      this.remeberPwd = true;
    }
  },
  methods: {
    toPage(url) {
      const returnUrl = window.location.href;
      window.location.href = `${this.baseUrl}${url}?returnUrl=${returnUrl}`;
    },
    submitForm(formName) {
      this.loginLoading = true;
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          this.loginForm.grant_type = "password";

          try {
            const userInfo = await this.$store.dispatch(
              "user/login",
              this.loginForm
            );

            if (!userInfo) {
              this.$message.error("登录失败，请稍后重试");
              return false;
            }

            // 判断是否记住密码
            if (this.remeberPwd) {
              // 本地存储用户名和密码
              Cookies.set("username", this.loginForm.username, {
                expires: 7,
              });
              Cookies.set("password", encrypt(this.loginForm.password), {
                expires: 7,
              });
            }

            // 获取可通过的路由
            await this.$store.dispatch("permission/generateRoutes", userInfo);

            this.loginLoading = false;

            this.$message.success(
              `尊敬的${userInfo.personName}，欢迎使用${this.systemTitle}`
            );

            this.$router.push({
              path: "/user/index",
            });
          } catch (err) {
            this.loginLoading = false;
            if (err.response) {
              if (err.response.data.error_description === "该用户尚未注册") {
                this.$message.error("用户名或密码不正确");
              }else{
                this.$message.error(err.response.data.error_description);
              }
              if (
                err.response.data.error_uri !== null &&
                err.response.data.error_uri !== undefined &&
                err.response.data.error_uri !== ""
              ) {
                this.$confirm('当前密码设置过于简单，为提高你账户的安全性，请您修改密码！', '温馨提示', {
                  confirmButtonText: '立即修改',
                  showCancelButton: false,
                  showClose:false,
                  closeOnClickModal:false,
                  closeOnPressEscape:false
                }).then(async() => {
                  window.open(`${baseUrl}${err.response.data.error_uri}`);
                })
                
              }
            }
            console.log(err);
          }
        } else {
          this.loginLoading = false;
          this.$message.error("信息填写有误，请检查");
          return false;
        }
      });
    },
    logout() {
      this.$confirm("确认退出该系统？", "温馨提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      })
        .then(async () => {
          this.loginForm = {
            username: "",
            password: "",
          };
          try {
            await this.$store.dispatch("user/logout");
            await this.$store.dispatch("permission/generateRoutes", null);

            this.$message.success("您已退出该系统");
            this.$router.push({ name: "Home" });
          } catch (err) {
            console.log(err);
            production
              ? (window.location.href = process.env.VUE_APP_BASE_URL)
              : this.$router.push({ name: "Home" });
          }
        })
        .catch((err) => console.log(err));
    },
    openOperationManual() {
      window.open(
        `${urlPrefix}/static/pdf/南宁市多测合一信息化管理用户手册.pdf`,
        "_blank"
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.home {
  &-title {
    font-weight: normal;
    text-align: center;
    font-size: 42px;
    margin-bottom: 0;
    margin-top: 20px;
  }

  &-subtitle {
    text-align: center;
    font-size: 16px;
  }

  &-content {
    margin-top: 30px;
    .banner-user {
      // background: #e2e6e9;
      // padding: 10px 10px 20px 10px;
      // height: 358px;
      justify-content: space-between;
      overflow: hidden;

      // /deep/ .el-row{
      //   display: flex;
      // }
    }

    .banner-container {
      width: 100%;
      overflow: hidden;
      margin-right: 10px;
      height: 410px;
      // margin-bottom: 20px;

      /deep/ .tp-banner {
        background: #fff;
        border-radius: 4px;
      }

      /deep/ .tp-bgimg {
        border-radius: 4px;
      }

      /deep/ .tp-bullets {
        bottom: 10px !important;
      }

      .link-btn {
        padding: 12px 32px;
        background: $color-primary;
        color: #fff;
        border-radius: 4px;
        font-size: 16px;
      }
    }

    .login {
      &-container {
        width: 100%;
        background: #fff;
        height: 355px;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        margin-bottom: 20px;
      }

      &-title {
        text-align: center;
        padding: 10px 15px;
        background: $color-primary;
        color: #fff;
      }

      &-content {
        padding: 10px 20px;

        .welcome {
          text-indent: 2em;
          color: #7e8998;
          line-height: 1.5;
        }

        .from-label {
          padding: 10px 0;
          color: #2e363f;
        }
      }

      &-btn-container {
        position: absolute;
        text-align: center;
        width: 100%;
        bottom: 20px;
      }
    }

    .password {
      &-container {
        justify-content: center;
        padding-top: 10px;
      }

      &-remember {
        margin-right: 20px;
      }

      &-forget {
        color: $color-primary;
        cursor: pointer;
        margin-top: -1px;

        &:hover {
          color: #09f;
        }
      }
    }

    .operation-guide {
      width: 900px;
      margin: auto;
      margin-top: -40px;

      /deep/ .el-col {
        justify-content: center;
      }
    }

    .guide-container,
    .browser-container {
      margin-bottom: 20px;

      h4 {
        font-size: 20px;
        font-weight: normal;
      }

      .iconfont {
        font-size: 22px;
        margin-right: 5px;
      }

      a {
        color: $color-primary;
        &:hover {
          color: #09f;
        }
      }
    }
  }
}

hr {
  height: 1px;
  margin: 10px 0;
  background-color: #e2e2e2;
  clear: both;
  border: 0;
  margin: 15px 0;
  border-top: #ddd 1px solid;
  border-bottom: #fff 1px solid;
}

@media screen and (max-width: 1000px) {
  .home-content {
    .login {
      &-container {
        margin-top: 20px;
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .home-content {
    .banner-container {
      height: auto;
    }

    // .login-container{
    //   height: 100%;
    // }

    .operation-guide {
      width: auto;
      margin-top: 0;

      /deep/ .el-col {
        justify-content: flex-start;
      }
    }
  }
}
</style>
