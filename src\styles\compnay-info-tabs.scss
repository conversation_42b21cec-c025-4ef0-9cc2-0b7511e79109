.company-info-container {
  padding: 20px 0;
  position: relative;
}

.operate-btn-container {
  position: absolute;
  top: 16px;
  right: 2px;
  z-index: 10;
}

.tabs {
  &-title {
    color: #000;

    /deep/ .el-tabs__header {
      margin-bottom: 0;
    }

    /deep/ .el-tabs__nav {
      overflow: hidden;
    }

    /deep/ .el-tabs__item {
      &.is-active {
        background: #ffffff;
        outline: none !important;
      }
    }
  }

  /deep/ .el-tabs__content {
    padding: 20px;
    min-height: calc(100vh - 497px);
    background: #ffffff;
    border-left: 1px solid #dfe4ed;
    border-right: 1px solid #dfe4ed;
    border-bottom: 1px solid #dfe4ed;
    border-radius: 0 0 4px 4px;
  }

  &.has-tips {
    /deep/ .el-tabs__content {
      min-height: calc(100vh - 548px);
    }
  }

  &.has-agreement {
    /deep/ .el-tabs__content {
      min-height: calc(100vh - 665px) !important;
    }
  }
}
// pad和手机
@media only screen and (max-width: 980px) {
  .tabs{
    /deep/ .el-tabs__content {
      min-height: calc(100vh - 679px);
    }

    &.has-tips {
      /deep/ .el-tabs__content {
        min-height: calc(100vh - 730px);
      }
    }
  
    &.has-agreement {
      /deep/ .el-tabs__content {
        min-height: calc(100vh - 847px) !important;
      }
    }
  }
}

// 处理按钮
@media only screen and (max-width: 800px) {
  .operate-btn-container{
    position: relative;
    top:0;
    margin-bottom: 20px;
  }
}