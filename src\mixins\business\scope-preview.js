/*
 * 模块 : 从txt内容中读取坐标预览项目范围
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-09-22
 * 版本 : version 1.0
 */
/* eslint-disable */

// Api
import Api from "api/business/index.js";

export default {
  data() {
    return {
      // 范围弹窗
      scopePreviewDialog: {
        visible: false,
        loading: false
      },
      // 坐标值
      coordinateList: []
    };
  },
  methods: {
    // 预览范围
    previewScope(e) {
      this.scopePreviewDialog.loading = true;
      Api.GetScopeCoordinates(e.ID)
        .then(res => {
          const { Data, StateCode } = res;
          if (StateCode === 1) {
            this.coordinateList = [];
            this.handleCoordinate(Data)
          } else {
            console.log(res);
            this.scopePreviewDialog.loading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.scopePreviewDialog.loading = false;
        });
    },
    // 处理坐标值
    handleCoordinate(text, file) {
      const lines = text.split("\n");

      if (!lines.length) {
        this.$message.error("获取数据有误，请稍后重试");
        this.cancelUpload(file);
        return false;
      }
      lines.forEach(e => {
        const arr = e.split(",");
        if (arr.length >= 3) {
          // 字符串要转成成浮点数，不然会报错 "coordinates must be finite numbers"
          this.coordinateList.push([parseFloat(arr[2]), parseFloat(arr[1])]);
        }
      });

      if (!this.coordinateList.length || this.coordinateList.length < 3) {
        this.$confirm(
          `获取数据有误，无法生成预览图，请稍后重试`,
          "温馨提示",
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: "确认",
            cancelButtonText: "取消"
          }
        )
          .then(() => {
            this.cancelUpload(file);
            this.closeScopeDialog();
          })
          .catch(() => {
            this.cancelUpload(file);
            this.closeScopeDialog();
          });
        return false;
      }

      this.scopePreviewDialog = {
        visible: true,
        loading: false
      }
    },
    // 关闭范围弹窗
    closeScopeDialog() {
      this.scopePreviewDialog = {
        visible: false,
        loading: false
      }
    }
  },
};
