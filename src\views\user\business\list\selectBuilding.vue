<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="650px"
      class="select-building-dialog"
      @close="close"
    >
      <template v-if="business">
        <tip type="default" class="mb-20">请选择您要进行变更的楼栋</tip>
      </template>
      <div>
        <!-- 功能建设中，敬请期待... -->
        <!-- 表格 -->
        <dynamic-table
          v-if="!listLoading"
          ref="buildingTable"
          class="table-container"
          :table-header="tableHeader"
          :table-data="buildingList"
          :default-props="tableProps"
        >
          <el-table-column width="50" label="选项" align="center">
            <template slot-scope="scope">
              <el-radio
                v-model="radioSelect"
                :label="scope.row.ZRZGUID"
                @change.native.stop="(e) => handleRadioChange(e, scope.row)"
                >&nbsp;</el-radio
              >
            </template>
          </el-table-column>
          <!-- <el-table-column
            type="selection"
            width="55"
            fixed="left"
          ></el-table-column> -->
          <el-table-column width="50" label="序号" align="center">
            <template slot-scope="{ $index }">{{
              $index + 1 + page.pageSize * (page.pageNo - 1)
            }}</template>
          </el-table-column>
          <el-table-column
            prop="ZL"
            label="坐落"
            align="center"
          ></el-table-column>
        </dynamic-table>
      </div>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm()" :loading="saveLoading"
          >选择楼栋</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/business/index.js";
// 校验
import { validateGroundCode } from "utils/form.js";
// vuex
import { mapGetters } from "vuex";

import DynamicTable from "components/common/Table/DynamicTable";
// mixins
import Page from "mixins/page.js";
import Table from "mixins/table.js";

export default {
  name: "GetBuilding",
  components: { DynamicTable },
  mixins: [Page, Table],
  computed: {
    ...mapGetters(["userName", "personName"]),
    // 显示楼盘信息表变更提示
    showBuildingChangeTips() {
      const { businessClass } = this.business;
      return (
        businessClass === "RealEstatePreSurveyBuildingTableChangeFlow" ||
        businessClass === "RealEstateActualBuildingTableChangeFlow"
      );
    },
    // 显示成果变更提示
    showResultChangeTips() {
      const { businessClass } = this.business;
      return (
        businessClass === "RealEstatePreSurveyResultChangeFlow" ||
        businessClass === "RealEstateActualResultChangeFlow"
      );
    },
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前业务
    business: {
      type: Object,
      default: () => {},
    },
    // 当前楼盘表buildingList
    buildingList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      radioSelect: null, //表格单选框
      tableHeader: [],
      title: "创建业务",
      // loading
      saveLoading: false,
      // 表单
      // form: {
      //   GroundCode: null,
      // },
      // 规则
      rules: {
        GroundCode: [
          { required: true, validator: validateGroundCode, trigger: "blur" },
        ],
      },
      selectList: [],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      const { business } = this;
      this.title = `【${business.label}】选择楼栋`;
      this.radioSelect = null;
      this.clearSelect();
    },
    // 关闭存储弹窗
    close() {
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm() {
      //是否选择楼栋
      if (this.selectList.length !== 0) {
        // 成功回传父组件
        this.$emit("submit", this.business, JSON.stringify(this.selectList));
        this.close();
        /**去掉楼栋校验--start */
        //校验楼幢信息
        // const { businessClass } = this.business;
        // var validData = this.selectList.map((e) => {
        //   const { ZRZGUID, ZL } = e;
        //   return {
        //     ZRZGUID: ZRZGUID,
        //     ZL: ZL,
        //   };
        // });
        // var validInfo = { BusinessClass: businessClass, Data: validData };
        // Api.validBulidingInfo(validInfo)
        //   .then((res) => {
        //     const { StateCode, Data, Message } = res;
        //     if (StateCode == 1) {
        //       // 成功回传父组件
        //       this.$emit(
        //         "submit",
        //         this.business,
        //         JSON.stringify(this.selectList)
        //       );
        //       this.close();
        //     } else {
        //       this.$message.error(Message);
        //     }
        //     this.saveLoading = false;
        //   })
        //   .catch((err) => {
        //     console.log(err);
        //     this.saveLoading = false;
        //   });
        /**去掉楼栋校验--end */
      } else {
        this.$message.error("楼栋不能为空，请选择");
      }
    },
    //监听单选单选
    handleRadioChange(e, row) {
      e.stopPropagation();
      this.clearSelect();
      this.selectList.push(row);
    },
  },
};
</script>

<style lang="scss" scoped>
.select-building-dialog {
  /deep/ .el-dialog__body {
    padding-bottom: 5px;
  }
  /deep/ .el-radio__label {
    display: none;
  }
}
</style>
