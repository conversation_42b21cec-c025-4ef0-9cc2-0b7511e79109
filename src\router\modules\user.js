import LayoutDefault from '@/layout/default'
import LayoutRouterView from '@/layout/router-view'

const router = {
  path: '/user',
  component: LayoutDefault,
  redirect: '/user/index',
  meta: {
    title: '用户中心',
    // icon: 'el-icon-user-solid',
    directLink: true
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/user/index'),
      name: 'UserCenter',
      hidden: true
    },
    {
      path: 'info',
      component: () => import('@/views/user/info/index'),
      name: 'UserInfo',
      meta: {
        title: '用户信息',
        roles: ['3'],
      }
    },
    {
      path: 'business',
      component: LayoutRouterView,
      redirect: '/user/business/index',
      meta: {
        title: '办理业务'
      },
      children: [
        {
          path: 'index',
          component: () => import('@/views/user/business/list/index'),
          name: 'BusinessList',
          meta: {
            activeMenu: '/user/business'
          },
          hidden: true
        },
        {
          path: 'base-data',
          component: () => import('@/views/user/business/base-data/index'),
          name: 'BaseData',
          hidden: true,
          meta: {
            title: '测绘数据申请',
            roles: ['1', '2'],
            switchCompanyToUserCenter: true
          }
        },
        // 逻辑上是，只有建设单位的人员才能申请这些业务，测绘单位只是在平台承接流转
        // {
        //   path: 'blue-line',
        //   component: () => import('@/views/user/business/survey-demarcation/blue-line/index'),
        //   name: 'BlueLine',
        //   hidden: true,
        //   meta: {
        //     title: '勘测定界-蓝线图',
        //     roles: ['2']
        //   }
        // },
        {
          path: 'staking',
          component: () =>
            import('@/views/user/business/planning-point/staking/index'),
          name: 'Staking',
          hidden: true,
          meta: {
            title: '规划定点及不动产权籍调查-拨地定桩',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        {
          path: 'setout-survey',
          component: () =>
            import('@/views/user/business/check-line/setout-survey/index'),
          name: 'SetoutSurvey',
          hidden: true,
          meta: {
            title: '规划验线-放线测量',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        {
          path: 'pre-mapping',
          component: () =>
            import('@/views/user/business/check-line/pre-mapping/index'),
          name: 'PreMapping',
          hidden: true,
          meta: {
            title: '规划验线-不动产预测绘',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        {
          path: 'pre-mapping-merge',
          component: () =>
            import('@/views/user/business/check-line/pre-mapping-merge/index'),
          name: 'PreMappingMerge',
          hidden: true,
          meta: {
            title: '规划验线-不动产预核业务',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        {
          path: 'planning-check',
          component: () =>
            import('@/views/user/business/check-line/planning-check/index'),
          name: 'PlanningCheck',
          hidden: true,
          meta: {
            title: '规划验线-规划放线测量与验线',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        {
          path: 'actual-mapping',
          component: () =>
            import('@/views/user/business/project-verify/actual-mapping/index'),
          name: 'ActualMapping',
          hidden: true,
          meta: {
            title: '不动产规划核实及实测-不动产实核业务',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        // 放线测量与规划验线（市政工程）
        {
          path: 'council-planning-check',
          component: () =>
            import('@/views/user/business/check-line/council-planning-check/index'),
          name: 'CouncilPlanningCheck',
          hidden: true,
          meta: {
            title: '规划验线-放线测量与规划验线（市政工程）',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        // 不动产预核业务即时办理
        {
          path: 'pre-mapping-merge-auto',
          component: () =>
            import('@/views/user/business/check-line/pre-mapping-merge-auto/index'),
          name: 'PreMappingMergeAuto',
          hidden: true,
          meta: {
            title: '规划验线-不动产预核业务（即时办结）',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        // 市政工程建设竣工规划核实
        {
          path: 'council-plan-check',
          component: () =>
            import('@/views/user/business/project-verify/council-plan-check/index'),
          name: 'CouncilPlanCheck',
          hidden: true,
          meta: {
            title: '不动产规划核实及实测-市政工程建设竣工规划核实流程',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        // 变更流程
        {
          path: 'change',
          component: LayoutRouterView,
          redirect: '/user/business/index',
          hidden: true,
          meta: {
            title: '变更流程'
          },
          children: [
            {
              path: 'pre-merge-building',
              component: () =>
                import('@/views/user/business/change/pre-merge-building/index'),
              name: 'PreChangeBuilding',
              hidden: true,
              meta: {
                title: '规划验线-不动产预测绘楼盘表维护',
                roles: ['2'],
                switchCompanyToUserCenter: true
              }
            },
            {
              path: 'pre-merge-result',
              component: () =>
                import('@/views/user/business/change/pre-merge-result/index'),
              name: 'PreChangeResult',
              hidden: true,
              meta: {
                title: '规划验线-不动产预测绘成果变更',
                roles: ['2'],
                switchCompanyToUserCenter: true
              }
            },
            {
              path: 'actual-mapping-building',
              component: () =>
                import('@/views/user/business/change/actual-mapping-building/index'),
              name: 'ActualChangeBuilding',
              hidden: true,
              meta: {
                title: '不动产规划核实及实测-不动产实测绘楼盘表维护',
                roles: ['2'],
                switchCompanyToUserCenter: true
              }
            },
            {
              path: 'actual-mapping-result',
              component: () =>
                import('@/views/user/business/change/actual-mapping-result/index'),
              name: 'ActualChangeResult',
              hidden: true,
              meta: {
                title: '不动产规划核实及实测-不动产实测绘成果变更',
                roles: ['2'],
                switchCompanyToUserCenter: true
              }
            }
          ]
        },
        //不动产全面核实业务
        {
          path: 'overall-actual-mapping',
          component: () =>
            import('@/views/user/business/project-verify/overall-actual-mapping/index'),
          name: 'OverallActualMapping',
          hidden: true,
          meta: {
            title: '不动产规划核实及实测-规划条件全面核实业务',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        },
        //征地拆迁业务
        {
          path: 'land-survey-mapping',
          component: () =>
            import('@/views/user/business/land-survey-mapping/index'),
          name: 'LandSurveyMapping',
          hidden: true,
          meta: {
            title: '征地拆迁-测绘数据汇交',
            roles: ['2'],
            switchCompanyToUserCenter: true
          }
        }
      ]
    },
    // {
    //   path: process.env.VUE_APP_BASE_URL,
    //   component: () => import("@/views/home/<USER>"),
    //   name: "BDCHome",
    //   meta: {
    //     title: "办理不动产登记",
    //     // icon: "el-icon-s-home"
    //   },
    // },
    {
      path: 'my-business',
      component: () => import('@/views/user/my-business/index'),
      name: 'MyBusiness',
      redirect: '/user/my-business/in-progress',
      meta: {
        title: '我的业务'
      },
      children: [
        {
          path: 'in-progress',
          component: () => import('@/views/user/my-business/in-progress/index'),
          name: 'BusinessInProgress',
          meta: {
            title: '在办业务'
          }
        },
        {
          path: 'done',
          component: () => import('@/views/user/my-business/done/index'),
          name: 'BusinessDone',
          meta: {
            title: '已办业务'
          }
        }
      ]
    },
    {
      path: 'company-register',
      component: LayoutRouterView,
      redirect: '/user/company-register/index',
      children: [
        {
          path: 'index',
          component: () => import('@/views/user/company-register/index'),
          name: 'CompanyRegister',
          meta: {
            title: '测绘单位注册',
            roles: ['1']
          }
        },
        {
          path: 'info',
          component: () => import('@/views/user/company-register/info/index'),
          name: 'CompanyRegisterInfo',
          hidden: true,
          meta: {
            title: '单位注册信息',
            roles: ['1']
          }
        }
      ]
    },
    {
      path: 'company-info',
      component: () => import('@/views/user/company-info/index'),
      name: 'CompanyInfo',
      meta: {
        title: '单位信息',
        roles: ['2']
      }
    },
    {
      path: 'company-change',
      component: LayoutRouterView,
      hidden: true,
      redirect: '/user/company-change/mapping',
      children: [
        {
          path: 'mapping',
          component: () => import('@/views/user/company-change/mapping/index'),
          name: 'MappingCompanyChange',
          meta: {
            title: '单位信息变更申请',
            roles: ['2']
          }
        },
        {
          path: 'hidstory',
          component: () => import('@/views/user/company-change/history/index'),
          name: 'CompanyChangeHistory',
          hidden: true,
          meta: {
            title: '单位信息变更记录',
            roles: ['2']
          }
        }
      ]
    },
    {
      path: 'registered-surveyor-auth',
      component: () => import('@/views/user/registered-surveyor-auth/index'),
      name: 'RegisteredSurveyorAuth',
      hidden: true,
      meta: {
        title: '注册测绘师授权',
        roles: ['2']
      }
    },
    {
      path: 'transfer-business',
      component: () => import('@/views/user/transfer-business/index'),
      name: 'TransferBusiness',
      meta: {
        title: '业务移交',
        roles: ['2-a']
      }
    },
    {
      path: 'company-withdraw',
      component: LayoutRouterView,
      hidden: true,
      redirect: '/user/company-withdraw',
      children: [
        {
          path: 'company-withdraw',
          component: () => import('@/views/user/company-withdraw/index'),
          name: 'CompanyWithDraw',
          meta: {
            title: '注销测绘单位',
            roles: ['2']
          }
        }
      ]
    }
  ]
}

export default router
