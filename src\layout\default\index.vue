<template>
  <!-- eslint-disable -->
  <div class="app-wrapper">
    <div class="main-container">
      <header class="header-container">
        <app-top />
        <navbar />
        <div class="header-shadow"></div>
      </header>
      <breadcrumb v-if="breadcrumbVisible" class="breadcrumb-container" />
      <app-main
        v-loading="pageLoading"
        element-loading-text="加载中，请稍等..."
        element-loading-background="rgb(244, 245, 247)"
      />
      <app-footer />
    </div>
    <back-top :visible="backTopvisible" />
  </div>
</template>

<script>
/* eslint-disable */
import {
  AppMain,
  Navbar,
  AppFooter,
  AppTop,
  Breadcrumb,
  BackTop
} from "./components";
import { mapGetters } from "vuex";
import ScrollMixin from "mixins/scroll.js";

const whiteList = [];

export default {
  name: "LayoutDefault",
  mixins: [ScrollMixin],
  components: {
    App<PERSON>ain,
    Navbar,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>crumb,
    BackTop
  },
  computed: {
    ...mapGetters(["pageLoading"])
  },
  data() {
    return {
      breadcrumbVisible: true,
      backTopvisible: false
    };
  },
  mounted() {
    this.init(this.$route);
    this.$_initScrollEvent();
  },
  watch: {
    $route(route) {
      this.init(route);
    }
  },
  methods: {
    init(route) {
      const headerH = 133;
      const breadcrumbH = 61;
      const footerH = 222;
      
      if (whiteList.indexOf(route.name) >= 0) {
        this.breadcrumbVisible = false;
        appMain.style.minHeight = `calc(100vh - ${headerH + footerH}px)`;
      } else {
        this.breadcrumbVisible = true;
        appMain.style.minHeight = `calc(100vh - ${headerH +
          breadcrumbH +
          footerH}px)`;
      }

      let docW = document.body.clientWidth;
      if(docW <= 980){
        appMain.style.minHeight = `calc(100vh - ${headerH +
          breadcrumbH +
          404}px)`;
      }
    },
    $_scrollHandler() {
      const { documentElement, body } = document;
      let scrollTop = documentElement.scrollTop || body.scrollTop;
      // let clientHeight = documentElement.clientHeight;
      // let scrollHeight = body.scrollHeight;
      this.backTopvisible = scrollTop > 94 ? true : false;

      if (scrollTop > 0) {
        $(".header-shadow")
          .stop()
          .animate({ top: "80px", opacity: 0 }, 300);
        // $(".header-shadow").hide();
        return;
      } else {
        // $(".header-shadow").show();
        $(".header-shadow")
          .stop()
          .animate({ top: "133px", opacity: 1 }, 300);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  // height: 100%;
  // width: 100%;
}

.main-container {
  padding-top: 133px;
}

.header-container {
  .header-shadow {
    background: url(../../assets/images/header_shadow.png) no-repeat top center;
    position: absolute;
    top: 133px;
    left: 0;
    right: 0;
    height: 60px;
    z-index: 0;
  }
}

@media screen and (max-width: 768px) {
  .main-container {
    padding-top: 112px;
  }

  .header-container {
    .header-shadow {
      display: none;
    }
  }
}
</style>
