/*
 * 模块 : 预览申请信息相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-08-17
 * 版本 : version 1.0
 */
/* eslint-disable */
import DynamicTable from "components/common/Table/DynamicTable";
import ListUpload from "components/common/AttachmentUpload/ListUpload.vue";
// mixins
import AttachmentMixin from "mixins/attachment/index.js";
import CheckAttachmentMixin from "mixins/attachment/check.js";
// vuex
import { mapGetters } from "vuex";

export default {
  components: { DynamicTable, ListUpload },
  mixins: [AttachmentMixin, CheckAttachmentMixin],
  computed: {
    ...mapGetters(["companyRole", "personNo", "roles"]),
    // 是否已完成
    isFinished() {
      const { step, stepList, baseInfo } = this;
      return step === stepList.length && baseInfo.StateCode === 2;
    },
    // 当前申请人
    isCurrentApplicant() {
      const { personNo, baseInfo } = this;
      return baseInfo.CreatePersonNo === personNo;
    },
    // 测绘单位
    mappingCompany() {
      const { roles } = this;
      return roles.indexOf("c-m") >= 0;
    },
    // 建设单位
    developer() {
      const { roles } = this;
      return roles.indexOf("c-d") >= 0;
    },
    //是否是最后一步
    isLastStep() {
      const { step, stepList, isFinished } = this;
      return step === stepList.length && !isFinished;
    },
    //判断当前流程是否属于mdb替换退回
    isReplaceMdb() {
      const { currentAction } = this;
      return currentAction.ExtendAttr.UploadMDBOnly;
    }
  },
  props: {
    // 基本信息
    baseInfo: {
      type: Object,
      default: () => ({
        BusinessNumber: null,
        BusinessType: null,
        CreatePersonName: null,
        CreatePersonPhone: null,
        DeveloperName: null,
        SurveyCompanyName: null,
        StateCode: 0,
      }),
    },
    // 流程
    stepList: {
      type: Array,
      default: () => [],
    },
    // 业务内容信息，不同业务内容不同
    contentInfo: {
      type: Object,
      default: () => ({
        GroundCode: null,
      }),
    },
    // 附件信息
    attachmentData: {
      type: Object,
      default: () => ({
        ScopeDiagram: [],
        SurveyNotification: [],
        Others: [],
      }),
    },
    // 步骤
    step: {
      type: Number,
      default: 1,
    },
    // 步骤信息
    actionsInfos: {
      type: Array,
      default: () => [],
    },
    //当前步骤
    currentAction: {
      type: Object,
      default: () => ({
        ExtendAttr: {UploadMDBOnly:false},
      }),
    },
  },
  data() {
    return {
      activeNames: "1",
      form: {
        Others: [],
      },
    };
  },
  watch: {
    attachmentData(attachments) {
      this.form = { ...attachments };
    },
  },
  created() {
    this.form = { ...this.attachmentData };
  },
  methods: {
    downloadStart(file) {
      this.$emit("download-start", file);
    },
    downloadEnd(file) {
      this.$emit("download-end", file);
    },
    downloadFail(file) {
      this.$emit("download-fail", file);
    },
    preview(file) {
      this.$emit("preview", file);
    },
    // 是否成果备案环节
    isRecordAction(recordActionId = 5) {
      const { actionsInfos, isCurrentApplicant, isFinished } = this;
      const length = actionsInfos.length;
      if (!length || length < 2) {
        return false;
      }
      const currentAction = actionsInfos[length - 1];
      return currentAction.ActionId === recordActionId && isCurrentApplicant && !isFinished;
    },
    // 成果备案环节退回且为当前申请人
    isRecordBack(recordActionId = 5) {
      const { actionsInfos, isCurrentApplicant } = this;

      const length = actionsInfos.length;
      if (!length || length < 2) {
        return false;
      }

      const lastAction = actionsInfos[length - 2];

      // 成果备案退回
      return lastAction.ActionId === recordActionId && isCurrentApplicant;
    },
    
  },
};
