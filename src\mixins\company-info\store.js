/*
 * 模块 : 单位信息-表单功能相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-18
 * 版本 : version 1.0
 */
/* eslint-disable */
export default {
    props: {
        // 弹窗可见性
        visible: {
            type: Boolean,
            default: false
        },
        // 记录数据
        row: {
            type: Object,
            default: () => ({})
        },
        // 记录列表
        list: {
            type: Array,
            default: () => []
        },
        // 当前记录索引
        index: {
            type: Number,
            default: -1
        },
        // 禁止编辑
        disableEdit: {
            type: <PERSON>olean,
            default: false
        },
        // 单位类型
        companyInfoType: {
            type: String,
            default: "测绘单位"
        },
        // 单位名称
        companyInfoName: {
            type: String,
            default: ""
        },
        // 组件使用类型，register 单位注册 info 单位信息
        useType: {
            type: String,
            default: "info"
        }
    },
    data() {
        return {
            submit: false
        }
    },
    methods: {
        // 初始化
        init(name) {
            // 解决后端接口出错时表单校验问题
            if (this.$refs.form) {
                this.$refs.form.clearValidate();
            }

            if (!this.disableEdit) {
                this.$nextTick(() => {
                    this.$refs.form.clearValidate();
                });
            }

            this.submit = false;

            if (this.disableEdit) {
                this.title = `查看${name}详情`;
            } else {
                this.title = `${this.row ? "编辑" : "添加"}${name}`;
            }

            this.setForm();
        },
        // 关闭存储弹窗
        close() {
            this.reset();
            if (!this.submit) {
                this.$emit("cancel", this.row ? 1 : 0, this.row, this.index);
            }
            this.$emit("update:visible", false);
        },
        // 提交表单
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const params = { ...this.form };
                    // 成功回传父组件
                    this.$emit("submit", this.row ? 1 : 0, params, this.index);
                    this.submit = true;
                    this.$emit("update:visible", false);
                } else {
                    this.$message.error("信息填写有误，请检查");
                }
            });
        },
    }
}
