# current environment
VUE_APP_ENV = 'development'

# request base api
VUE_APP_SERVER_API = 'https://practice.nngeo.com'

# base url
VUE_APP_BASE_URL = 'https://practice.nngeo.com'

# static file url prefix
VUE_APP_URL_PREFIX = ''

# system title
VUE_APP_SYSYTEM_TITLE = '南宁市多测合一信息化管理'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
