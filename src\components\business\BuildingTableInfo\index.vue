<template>
  <!-- eslint-disable -->
  <div v-if="buildingTableInfo.length">
    <div
      v-for="(item, index) in buildingTableInfo"
      :key="'buildingTableInfo' + index"
    >
      <table
        class="table"
        cellpadding="0"
        cellspacing="0"
        style="margin-top: -1px"
      >
        <tr>
          <th>
            <div>规划总建筑面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
          <td>{{ item.ZongGHJZMJ | isNull }}</td>
          <th>
            <div>规划总计容面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
          <td>{{ item.ZongGHJRMJ | isNull }}</td>
        </tr>
        <tr>
          <th>
            <div>地上规划建筑面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
          <td>{{ item.DiSGHJZMJ | isNull }}</td>
          <th>
            <div>地下规划建筑面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
          <td>{{ item.DiXGHJZMJ | isNull }}</td>
        </tr>
        <tr>
          <th>规划计算面积</th>
          <td colspan="3" style="padding: 0">
            <table class="table inside" cellpadding="0" cellspacing="0">
              <tr>
                <th style="width: 25%">规划用途</th>
                <th style="width: 25%">规划建筑面积 (㎡)</th>
                <th style="width: 25%">规划套内建筑面积 (㎡)</th>
                <th style="width: 25%">规划分摊建筑面积 (㎡)</th>
              </tr>
              <tr v-for="(guiH, index) in item.GuiH" :key="'GuiH' + index">
                <td>{{ guiH.GuiHYT | isNull }}</td>
                <td>{{ guiH.GuiHJZMJ | isNull }}</td>
                <td>{{ guiH.GuiHTNMJ | isNull }}</td>
                <td>{{ guiH.GuiHFTMJ | isNull }}</td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <th>房产计算面积</th>
          <td colspan="3" style="padding: 0">
            <table class="table inside" cellpadding="0" cellspacing="0">
              <tr>
                <th style="width: 25%">建筑类别</th>
                <th style="width: 25%">建筑面积 (㎡)</th>
                <th style="width: 25%">套内建筑面积 (㎡)</th>
                <th style="width: 25%">分摊建筑面积 (㎡)</th>
              </tr>
              <tr v-for="(fangC, index) in item.FangC" :key="'fangC' + index">
                <td>{{ fangC.JianZLB | isNull }}</td>
                <td>{{ fangC.JianZMJ | isNull }}</td>
                <td>{{ fangC.TaoNMJ | isNull }}</td>
                <td>{{ fangC.FenTMJ | isNull }}</td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <div class="operate-btn flex">
        <el-button type="primary" icon="el-icon-view" @click="view(item)"
          >查看楼盘信息</el-button
        >
        <el-button
          v-if="propertyInfo"
          type="primary"
          icon="el-icon-download"
          @click="download(propertyInfo)"
          >下载楼盘信息表</el-button
        >
        <el-button
            v-if="conditionVerificateInfo"
            type="primary"
            icon="el-icon-download"
            @click="download(conditionVerificateInfo)"
          >下载竣工规划条件核实及土地核验信息表</el-button>
      </div>
    </div>
    <property-info-table-dialog
      :visible.sync="propertyDialog.visible"
      :info="propertyDialog.info"
      :file="propertyInfo"
    />
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import PropertyInfoTableDialog from "components/business/PropertyInfoTableDialog/index.vue";
// Api
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";

export default {
  name: "BuildingTableInfo",
  mixins: [AttachmentDownload],
  components: { PropertyInfoTableDialog },
  props: {
    // 楼盘信息表pdf
    propertyInfo: {
      type: Object,
      default: null,
    },
    // 竣工规划条件核实信息表pdf
    conditionVerificateInfo: {
      type: Object,
      default: null,
    },
    // 楼盘信息
    buildingTableInfo: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      propertyDialog: {
        visible: false,
        info: null,
      },
    };
  },
  methods: {
    downloadStart(file) {
      this.$emit("download-start", file);
    },
    downloadEnd(file) {
      this.$emit("download-end", file);
    },
    downloadFail(file) {
      this.$emit("download-fail", file);
    },
    view(info) {
      this.propertyDialog = {
        visible: true,
        info,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  .inside {
    border-top: none;
    border-left: none;

    tr:last-child {
      th,
      td {
        border-bottom: none;
      }
    }
  }

  th {
    background: #f8f8f8;
    color: #909399;
    min-width: 130px;
    width: 130px;
    max-width: 130px;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    min-width: 130px;
  }
}

.operate-btn {
  justify-content: center;
  // border-bottom: 1px solid #dfe6ec;
  padding: 20px 0;
}
</style>