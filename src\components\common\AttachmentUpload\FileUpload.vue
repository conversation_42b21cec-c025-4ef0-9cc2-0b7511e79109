<template>
  <!-- eslint-disable -->
  <div class="file-upload-container">
    <div class="file-upload">
      <el-upload
        class="file-upload__btn"
        ref="fileUpload"
        drag
        multiple
        :action="action"
        :file-list="fileList"
        :headers="headers"
        :data="data"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadFail"
      >
        <div slot="default">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </div>
        <div slot="file" slot-scope="{file}">
          <div class="file-upload-text" v-if="file.status === 'ready'">准备上传...</div>
          <div class="file-upload-text" v-if="file.status === 'fail'">上传失败</div>
          <div class="file-item flex" v-if="file.status === 'success'">
            <div class="file-item__left mr-10 flex">
              <template v-if="row.AttachmentExt">
                <div class="file-icon mr-10">
                  <img
                    v-if="isImg(file)"
                    class="file-thumbnail"
                    :src="file.ID ? `${thumbnailUrl}?id=${file.ID}` : defaultImg"
                  />
                  <span v-else>
                    <template v-if="file.AttachmentExt">
                      <i
                        v-if="(file.AttachmentExt).indexOf('.doc') >= 0"
                        class="iconfont icon-word"
                      ></i>
                      <i
                        v-else-if="(file.AttachmentExt).indexOf('.ppt') >= 0"
                        class="iconfont icon-ppt"
                      ></i>
                      <i
                        v-else-if="(file.AttachmentExt).indexOf('.pdf') >= 0"
                        class="iconfont icon-pdf"
                      ></i>
                      <i
                        v-else-if="(file.AttachmentExt).indexOf('.xls') >= 0"
                        class="iconfont icon-excel"
                      ></i>
                      <i
                        v-else-if="(file.AttachmentExt).indexOf('.zip') >= 0"
                        class="iconfont icon-zip"
                      ></i>
                      <i v-else class="iconfont icon-file"></i>
                    </template>
                  </span>
                </div>
                <div class="file-name">{{ file.AttachmentName + file.AttachmentExt }}</div>
                <span class="file-size">（{{ getSize(file.AttachmentLength / 1024) }}）</span>
              </template>
              <template v-else>
                <div class="file-name">找不到该文件，请删除后重新上传</div>
              </template>
            </div>
            <div class="file-item__right file-actions">
              <span v-if="canDownload" @click="download(file)">
                <i class="el-icon-download"></i>
              </span>
              <span @click="del(file)">
                <i class="el-icon-delete"></i>
              </span>
            </div>
          </div>
          <div v-if="file.status && file.status === 'uploading'" style="position:relative">
            <!-- 原本el组件的名称 -->
            <div>
              <div class="file-name">{{ file.name }}</div>
              <i class="el-icon-close" @click="cancelUpload(file)"></i>
            </div>
            <el-progress
              class="mt-10"
              :stroke-width="2"
              :percentage="parsePercentage(file.percentage)"
            ></el-progress>
          </div>
        </div>
        <div slot="tip">
          <!-- 提示 -->
          <div class="file-upload__tip">
            <span v-if="fileFormat">文件格式：{{ fileFormat }}，</span>
            <template v-if="showFileSize">
              文件大小：
              <span>{{ getSize(fileSize) }}</span>以内
            </template>
          </div>
          <div v-if="fileList.length" class="mt-10">
            <span class="file-list-title">上传文件列表：</span>
          </div>
        </div>
      </el-upload>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
// mixins
import AttachmentUpload from "mixins/attachment/upload.js";
import AttachmentDownload from "mixins/attachment/download.js";

function noop() {
  return true;
}

export default {
  name: "FileUpload",
  mixins: [AttachmentUpload, AttachmentDownload],
  props: {
    // 所有文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 是否显示文件大小提示文本
    showFileSize: {
      type: Boolean,
      default: true
    },
    // 文件大小限制，单位为KB
    fileSize: {
      type: Number,
      default: 150
    },
    // 是否可下载
    canDownload: {
      type: Boolean,
      default: true
    },
    // 上传要附带的额外参数
    data: {
      type: Object,
      default: () => {}
    },
    // 文件格式
    fileFormat: {
      type: String,
      default: ""
    },
    // 检查上传文件格式
    onCheckFormat: {
      type: Function,
      default: noop
    },
  },
  data() {
    return {
      imgReg: /\.(bmp|jpg|jpeg|png|gif|webp|JPG|PNG|GIF)$/
    };
  },
  methods: {
    // 是否是图片格式
    isImg(file) {
      if (this.imgReg.test(file.AttachmentExt)) {
        return true;
      }
      return false;
    },
    // 删除
    del(file) {
      let list = [...this.fileList];
      if (!list.length) return;
      const index = list.indexOf(file);
      list.splice(index, 1);
      this.$emit("delete", { file, index, list });
    }
  }
};
</script>
<style scoped lang="scss">
@import "~@/styles/file-upload.scss";
</style>

