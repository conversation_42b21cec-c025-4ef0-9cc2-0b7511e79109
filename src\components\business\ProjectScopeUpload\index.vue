<template>
  <!-- eslint-disable -->
  <div class="file-upload-container">
    <!-- 不能通过v-if判断隐藏整个项目范围上传组件，会导致$emit("upload-success")无效，所以只隐藏上传按钮 -->
    <div class="file-upload" v-if="dataCheckState !== -3">
      <el-upload
        class="file-upload__btn"
        ref="scopeUpload"
        drag
        :action="`${uploadAction}?id=${id}&applydata=${uploadData}`"
        :headers="headers"
        :on-change="getTxtContent"
        :on-success="uploadSuccess"
        :on-error="uploadFail"
        :auto-upload="false"
      >
        <div slot="trigger">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </div>
        <div slot="file" slot-scope="{file}">
          <div
            v-if="file.status && file.status === 'uploading' && parsePercentage(file.percentage) <= 100"
            style="position:relative"
          >
            <div>
              <div class="file-name">{{ file.name }}</div>
            </div>
            <el-progress
              class="mt-10"
              :stroke-width="2"
              :percentage="parsePercentage(file.percentage)"
            ></el-progress>
          </div>
        </div>
        <div slot="tip">
          <!-- 提示 -->
          <div class="file-upload__tip">
            <p>文件格式：txt，文件大小：{{ getSize(this.fileSize) }}以内</p>
          </div>
        </div>
      </el-upload>
    </div>
    <scope-preview-dialog
      title="范围确认"
      :visible.sync="scopePreviewDialog.visible"
      :loading="scopePreviewDialog.loading"
      :coordinates="coordinateList"
      map-id="scopeContainer"
      confirm-text="确认上传范围"
      @cancel="closeScopeDialog()"
      @confirm="upload()"
    />
  </div>
</template>
<script>
/* eslint-disable */
// mixins
import AttachmentUpload from "mixins/attachment/upload.js";
import ScopePreview from "mixins/business/scope-preview.js";
// 组件
import FileList from "components/common/FileList/index.vue";
import ScopePreviewDialog from "components/business/ScopePreview/index.vue";
// 授权
import { getToken } from "utils/auth";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  name: "ListUpload",
  components: { FileList, ScopePreviewDialog },
  mixins: [AttachmentUpload, ScopePreview],
  props: {
    // 是否显示文件大小提示文本
    showFileSize: {
      type: Boolean,
      default: true
    },
    // 文件大小限制，单位为KB
    fileSize: {
      type: Number,
      default: 1024
    },
    // 业务id
    id: {
      type: String | Number,
      default: null
    },
    // 检查状态 -3 上传并检查中（前端定义） 0 未上传 1 通过 2不通过 3 需要人工检查
    dataCheckState: {
      type: Number,
      default: 0
    },
    // 申请数据类型
    applyData: {
      type: Array,
      default: () => []
    },
    // 业务流程类型
    businessClass: {
      type: String,
      default: "BaseSurveyDataDownloadFlow"
    }
  },
  data() {
    return {
      file: null,
      fileList: [],
      // 上传地址
      uploadAction: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostProjectScopeResult`,
      confirmUpload: false,
      uploadData: {}
    };
  },
  created() {
    this.createUpload = false;
  },
  methods: {
    // 检查上传文件格式
    onCheckFormat(file) {
      const fileName = file.name;
      const suffix = fileName.substring(fileName.length - 5);

      // 文件格式
      if (file.type === "text/plain") return true;
      if (suffix.indexOf(".txt") >= 0) return true;

      this.$message.warning("文件只能是txt格式");
      return false;
    },
    // 上传前
    beforeUpload(file) {
      const { $message, fileSize, applyData, cancelUpload } = this;

      if (!applyData || !applyData.length) {
        this.$message.warning("请先选择申请提取数据");
        cancelUpload(file);
        return false;
      }

      this.uploadData = JSON.stringify(applyData);

      // 中文和标点替换成3个英文字符（数据库中文代表3个字节）判断文件名长度是否超过300
      const fileName = file.name.replace(
        /[\u4e00-\u9fa5|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g,
        "aaa"
      );
      if (fileName.length > 300) {
        $message.warning(`文件名称过长，请修改后重新上传`);
        return false;
      }

      const format = this.onCheckFormat(file);
      if (!format) return false;

      // 控制文件大小
      if (file.size / 1024 > fileSize) {
        $message.warning(`文件大小不能超过${this.getSize(fileSize)}`);
        return false;
      }

      return true;
    },
    // 从txt中读取数据
    getTxtContent(e, fileList) {
      const check = this.beforeUpload(e);
      if (!check) return false;

      if (!this.$refs.scopeUpload) {
        return false;
      }

      this.$refs.scopeUpload.uploadFiles = [e];
      this.confirmUpload = false;

      if (e.status !== "ready") {
        return false;
      }

      const file = e.raw;

      if (typeof FileReader === "undefined") {
        // 浏览器是否支持 FileReader
        this.$confirm(
          `您的浏览器不支持文件读取，推荐使用谷歌浏览器进行预览`,
          "温馨提示",
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: "下载谷歌浏览器",
            cancelButtonText: "取消"
          }
        )
          .then(res => {
            this.cancelUpload(file);
            window.open(
              "https://www.google.cn/intl/zh-CN/chrome/?standalone=1",
              "_blank"
            );
          })
          .catch(err => {
            this.cancelUpload(file);
          });

        return false;
      }

      this.coordinateList = [];
      const reader = new FileReader();
      reader.onload = e => this.handleCoordinate(e.target.result, file);
      // 以DataURL的形式读取文件
      reader.readAsText(file);
    },
    // 处理坐标值
    handleCoordinate(text, file) {
      const lines = text.split("\n");

      if (!lines.length) {
        this.$message.error("无法从上传的文件中读取坐标值，请重新上传");
        this.cancelUpload(file);
        return false;
      }
      lines.forEach(e => {
        const arr = e.split(",");
        if (arr.length >= 3) {
          // 字符串要转成成浮点数，不然会报错 "coordinates must be finite numbers"
          this.coordinateList.push([parseFloat(arr[2]), parseFloat(arr[1])]);
        }
      });

      if (!this.coordinateList.length || this.coordinateList.length < 3) {
        this.$confirm(
          `文件中内容有误，无法生成预览图，请检查后重新上传`,
          "温馨提示",
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: "确认",
            cancelButtonText: "取消"
          }
        )
          .then(() => {
            this.cancelUpload(file);
            this.closeScopeDialog();
          })
          .catch(() => {
            this.cancelUpload(file);
            this.closeScopeDialog();
          });
        return false;
      }

      this.scopePreviewDialog.visible = true;
    },
    // 取消上传
    cancelUpload(file) {
      if (file) {
        this.$refs.scopeUpload.abort(file);
      }

      this.$refs.scopeUpload.uploadFiles = [];
      this.fileList = [];
    },
    // 关闭范围弹窗
    closeScopeDialog() {
      if (!this.confirmUpload) {
        this.cancelUpload();
      }
      this.scopePreviewDialog.visible = false;
    },
    // 确认上传范围
    upload() {
      // 手动上传文件
      this.$refs.scopeUpload.submit();
      this.confirmUpload = true;
      this.scopePreviewDialog.visible = false;
      this.$emit("upload-start");
    },
    // 上传成功
    uploadSuccess(res, file, fileList) {
      const { StateCode, Data, Message } = res;

      if (StateCode === 1) {
        // this.$message.success("文件已上传");
        this.$emit("upload-success", Data, fileList);
      } else {
        this.$emit("upload-fail", res);
        this.$message.error(Message);
        return false;
      }
    },
    // 上传失败
    uploadFail(res, file, fileList) {
      console.log(res);
      this.$message.error("服务器繁忙，请稍后重试");
      this.$refs.scopeUpload.uploadFiles = [];
      this.$emit("upload-fail", res);
    },
    // 进度处理
    parsePercentage(val) {
      const percent = parseInt(val, 10);
      return setTimeout(() => {
        return percent;
      }, 100);
    }
  }
};
</script>
<style scoped lang="scss">
@import "~@/styles/file-upload.scss";
/deep/ .el-upload-list__item {
  position: relative;
  transition: none !important;
  line-height: 25px !important;
  padding: 5px 10px;
  outline: none;

  &.is-success {
    display: none;
  }
}
.scope-dialog-container {
  /deep/ .el-dialog__body {
    padding: 5px 20px 5px 20px;
  }
}
#scopeContainer {
  height: 500px;
  text-align: center;
}
.example {
    &-container {
        line-height: 1.5;
    }

    &-hint {
        color: $color-primary;
    }

    &-list {
        margin-left: 20px;
        padding: 0;

        &-item {
            list-style-type: decimal;
            padding: 2px 0;
            
            &__link {
                color: $color-primary;
                text-decoration: underline;

                &:hover {
                    color: #09f;
                }
            }

            &__phone{
                color: $color-primary;
                &:hover {
                    color: #09f;
                }
            }
        }
    }
}
</style>

