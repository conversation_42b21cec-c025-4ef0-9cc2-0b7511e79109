<template>
  <!-- eslint-disable -->
  <el-table ref="table" class="list-upload-table" :data="fileList" border>
    <el-table-column
      type="index"
      label="序号"
      width="50"
      align="center"
      header-align="center"
    ></el-table-column>
    <el-table-column prop="name" label="文件名称" header-align="center">
      <template slot-scope="{ row }">{{
        row.AttachmentExt
          ? row.AttachmentName + row.AttachmentExt
          : `${canDelete ? "找不到该文件，请删除后重新上传" : "找不到该文件"}`
      }}</template>
    </el-table-column>
    <el-table-column
      v-if="canDownload || canDelete || canPreview"
      prop="action"
      label="操作"
      align="center"
      header-align="center"
      width="180"
    >
      <template slot-scope="{ row, $index }">
        <template v-if="canPreview">
          <el-button
            v-if="isImg(row) || isPdf(row) || isXls(row)"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="preview(row)"
            >预览</el-button
          >
        </template>
        <el-button
          v-if="canDownload"
          size="mini"
          type="text"
          icon="el-icon-download"
          @click="download(row)"
          >下载</el-button
        >
        <el-popconfirm title="确认删除?" @onConfirm="del(row, $index)">
          <el-button
            v-if="canDelete"
            slot="reference"
            size="mini"
            type="text"
            icon="el-icon-delete"
            class="ml-10"
            >删除</el-button
          >
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";

export default {
  name: "FileList",
  mixins: [AttachmentDownload],
  props: {
    // 文件列表
    fileList: {
      type: Array,
      default: () => [],
    },
    // 是否可删除
    canDelete: {
      type: Boolean,
      default: false,
    },
    // 是否可下载
    canDownload: {
      type: Boolean,
      default: true,
    },
    // 是否可预览
    canPreview: {
      type: Boolean,
      default: true,
    },
    // 最大显示个数
    limit: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      list: [],
      imgReg: /\.(bmp|jpg|jpeg|png|gif|webp|JPG|PNG|GIF)$/,
    };
  },
  methods: {
    // 是否是图片格式
    isImg(file) {
      if (this.imgReg.test(file.AttachmentExt)) {
        return true;
      }
      return false;
    },
    // 是否是pdf
    isPdf(file) {
      if (file.AttachmentExt && file.AttachmentExt.indexOf(".pdf") >= 0) {
        return true;
      }
      return false;
    },
    //是否是xls
    isXls(file) {
      if (file.AttachmentExt && file.AttachmentExt.indexOf(".xls") >= 0) {
        return false;
      }
      return false;
    },
    // 预览图片
    preview(file) {
      this.$emit("preview", file);
    },
    // 删除
    del(file, index) {
      let list = [...this.fileList];
      if (!list.length) return;
      list.splice(index, 1);
      this.$emit("delete", { file, index, list });
    },
  },
};
</script>
<style scoped lang="scss">
.list-upload-table {
  line-height: 1 !important;
}
</style>
