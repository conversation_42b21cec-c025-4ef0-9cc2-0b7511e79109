<template>
  <!-- eslint-disable -->
  <div class="view-info-container" v-loading="downLoading" element-loading-text="文件下载中，请稍等...">
    <div class="business-info-title">
      <span>申请信息</span>
    </div>
    <el-card shadow="never">
      <el-form ref="form" :model="form" label-width="170px">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务编号：">{{ baseInfo.BusinessNumber | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务类型：">{{ baseInfo.BusinessType | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务名称：" prop="BusinessName">{{ baseInfo.BusinessName | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人：">{{ baseInfo.CreatePersonName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机号：">{{ baseInfo.CreatePersonPhone | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位：">{{ baseInfo.DeveloperName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测绘单位：">{{ baseInfo.SurveyCompanyName | isNull }}</el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="contentInfo" title="业务信息" name="2">
            <el-form-item label="工程建设项目编号：">{{ contentInfo.ProjectNumber | isNull }}</el-form-item>
            <el-form-item label="项目名称：">{{ contentInfo.ProjectName | isNull }}</el-form-item>
            <el-form-item label="项目详细地址：">{{ contentInfo.ProjectAddress | isNull }}</el-form-item>
          </el-collapse-item>
          <el-collapse-item title="附件" name="3">
            <el-form-item label="项目用地坐标文件材料：">
              <file-list
                :file-list="attachmentData.BlueLineImg"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label="测量通知书：">
              <file-list
                :file-list="attachmentData.SurveyNotification"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label="其他：">
              <list-upload
                v-if="isLastStep && isCurrentApplicant"
                file-format="png / jpg / gif / bmp / pdf / zip / rar"
                :file-list="form.Others"
                :file-size="102400"
                :data="{ BusinessType: baseInfo.BusinessType, BusinessID: baseInfo.ID, AttachmentType: '申请材料附件', AttachmentCategories: '其他附件' }"
                :on-check-format="checkOthers"
                :disabled="baseInfo.StateCode === 4"
                @upload-success="upload($event, 'form', 'Others')"
                @delete="del($event, 'form', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
              <file-list
                v-else
                :file-list="attachmentData.Others"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "StakingViewInfo",
  mixins: [ViewInfo],
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";
/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content{
  padding-bottom: 0;
}
</style>
