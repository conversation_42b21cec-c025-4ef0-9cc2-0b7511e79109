<template>
  <!-- eslint-disable -->
  <div class="base-info-container">
    <el-form :model="form" :label-width="labelWidth">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="审核状态：">
            <template v-if="form.StateCode >= 1 && form.StateCode <= 4">
              <el-tag v-if="form.StateCode === 1" class="tag-yellow" effect="dark">已提交</el-tag>
              <el-tag v-if="form.StateCode === 2" class="tag-blue" effect="dark">审核中</el-tag>
              <el-tag v-if="form.StateCode === 3" type="success" effect="dark">已通过</el-tag>
              <el-tag v-if="form.StateCode === 4" type="error" effect="dark">审核不通过</el-tag>
            </template>
            <span v-else>-</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审核人：">{{ form.AuditPerson ? form.AuditPerson : "-" }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="审核说明：">{{ form.ResponseReason ? form.ResponseReason : "-" }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签收时间：">{{ form.SignTime ? form.SignTime : "-" }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关闭时间：">{{ form.CloseTime ? form.CloseTime : "-" }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "CompanyAuditInfo",
  props: {
    // 当前激活类型
    activeTabName: {
      type: String,
      default: "base"
    },
    // 数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 加载
    loading: {
      type: Boolean,
      default: true
    },
    // 禁止编辑
    disableEdit: {
      type: Boolean,
      default: false
    },
    // 组件使用类型，register 单位注册 info 单位信息
    useType: {
      type: String,
      default: "info"
    }
  },
  data() {
    return {
      labelWidth: "100px",
      defaultForm: {
        SateCode: 0,
        ResponsePerson: null,
        ResponseMessage: null
      },
      form: {},
      operateText: "审核"
    };
  },
  watch: {
    loading(val) {
      if (!val) {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      // 解决后端接口出错时表单校验问题
      this.form =
        this.data && Object.keys(this.data).length
          ? { ...this.data }
          : { ...this.defaultForm };
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
