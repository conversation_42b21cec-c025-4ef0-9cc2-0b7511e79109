<template>
  <business-layout
    class="staking-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="prevBtn"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @prev="prev"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1 && developer">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="业务信息" style="margin-top: -10px">
            <el-form-item
              label-width="180px"
              label="工程建设项目编号："
              prop="ProjectNumber"
            >
              <el-input
                v-model.trim="form1.ProjectNumber"
                placeholder="请输入工程建设项目编号"
                :disabled="BaseInfo.StateCode === 4"
              />
            </el-form-item>
            <el-form-item
              label-width="180px"
              label="项目名称："
              prop="ProjectName"
            >
              <el-input
                v-model.trim="form1.ProjectName"
                placeholder="请输入项目名称"
                :disabled="BaseInfo.StateCode === 4"
              />
            </el-form-item>
            <el-form-item
              label-width="180px"
              label="项目详细地址："
              prop="ProjectAddress"
            >
              <el-input
                v-model.trim="form1.ProjectAddress"
                placeholder="请输入项目详细地址"
                :disabled="BaseInfo.StateCode === 4"
              />
            </el-form-item>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item
              label-width="180px"
              label="项目用地坐标文件材料："
              prop="BlueLineImg"
            >
              <list-upload
                file-format="pdf"
                :file-list="form1.BlueLineImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '项目用地坐标文件材料',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BlueLineImg')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
                @preview="preview"
                @delete="del($event, 'form1', 'BlueLineImg')"
              />
            </el-form-item>
            <el-form-item
              label-width="180px"
              label="测量通知书："
              prop="SurveyNotification"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.SurveyNotification"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '测量通知书',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SurveyNotification')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
                @preview="preview"
                @delete="del($event, 'form1', 'SurveyNotification')"
              />
            </el-form-item>
            <el-form-item label-width="180px" label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar /dwg"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2 && developer">
        <!-- 委托测绘单位 -->
        <mapping-company-list
          class="mb-20"
          :business-class="BaseInfo.BusinessClass"
          :business-id="businessID"
          :current-action-id="CurrentAction.ID"
          :flow-name="FlowInfo.FlowName"
          @select="refreshStep"
        />
      </div>
      <div v-if="currentStep === 3">
        <!-- 汇交测绘成果 -->
        <tip
          v-if="developer"
          class="mb-20 font-20 text-center bold"
        >等待测绘单位汇交并确认测绘成果...</tip>
        <!-- 上传数据范围 -->
        <el-form
          v-if="mappingCompany"
          ref="form3"
          :model="form3"
          :rules="rules3"
          label-width="130px"
        >
          <!-- DataCheckState -3 上传并检查中（前端定义） 0 未上传 1 通过 2 不通过 3 需要人工审核 -->
          <tip v-if="ContentInfo.DataCheckState === -3" class="mb-20">
            <i
              class="el-icon-loading mr-5"
            />系统正在对您上传的项目范围坐标进行检查，检查大概需要5~30秒，请耐心等待结果
          </tip>
          <tip
            v-if="ContentInfo.DataCheckState === 0"
            class="mb-20"
          >请根据您的需要勾选申请提取数据并上传项目范围坐标，待系统提取数据成功后方可上传测绘成果数据</tip>
          <tip
            v-if="ContentInfo.DataCheckState === 2"
            type="error"
            class="mb-20"
          >
            您上传的项目范围坐标未能通过检查
            <span
              v-if="ContentInfo.FailedReason"
            >，原因：{{ ContentInfo.FailedReason }}。</span>
          </tip>
          <template
            v-if="
              ContentInfo.DataCheckState === 1 ||
                ContentInfo.DataCheckState === 3
            "
          >
            <tip v-if="!baseDataFiles.length" class="mb-20">
              项目范围坐标上传成功，
              <i
                class="el-icon-loading mr-5"
              />系统正在根据您选择的数据进行提取，根据上传的项目范围坐标大小，提取的时间不同，请耐心等待结果
            </tip>
            <tip
              v-else
              class="mb-20"
              type="success"
            >系统已成功通过您上传的项目范围坐标提取数据，您可在下方进行数据提取</tip>
          </template>
          <el-row
            v-if="
              ContentInfo.DataCheckState === 0 ||
                ContentInfo.DataCheckState === 2 ||
                ContentInfo.DataCheckState === -3
            "
            :gutter="12"
          >
            <el-col :xs="24" :sm="10" :md="100" :lg="10" :xl="10">
              <el-form-item
                v-if="
                  ContentInfo.DataCheckState === 0 ||
                    ContentInfo.DataCheckState === 2
                "
                label="申请提取数据："
                prop="ApplyData"
              >
                <el-checkbox-group v-model="form3.ApplyData">
                  <el-checkbox
                    v-for="(item, index) in ApplyDataList"
                    :key="'apply-data' + index"
                    :label="item"
                  />
                </el-checkbox-group>
              </el-form-item>
              <el-form-item
                v-if="showScopeUpload"
                :label="
                  ContentInfo.DataCheckState === -3 ? '' : '项目范围坐标：'
                "
                prop="ProjectScope"
                class="mb-15"
              >
                <project-scope-upload
                  :id="businessID"
                  :business-class="BaseInfo.BusinessClass"
                  :file-size="10240"
                  :apply-data="form3.ApplyData"
                  :data-check-state="ContentInfo.DataCheckState"
                  @upload-start="projectScopeUploadStart"
                  @upload-success="projectScopeUploadSuccess"
                  @upload-fail="projectScopeUploadFail"
                />
              </el-form-item>
            </el-col>
            <el-col
              v-if="
                showScopeUpload &&
                  (ContentInfo.DataCheckState === 0 ||
                  ContentInfo.DataCheckState === 2)
              "
              class="example-container"
              :xs="24"
              :sm="14"
              :md="14"
              :lg="14"
              :xl="14"
            >
              <p class="example-hint mt-0">温馨提示：</p>
              <ol class="example-list">
                <li class="example-list-item">
                  所上传的项目范围txt坐标文件的每一行内容应按以下格式：点号,北坐标,东坐标,圆弧标识
                </li>
                <li class="example-list-item">
                  坐标系为CGCS2000大地坐标系，中央经线108°，3度带投影，加带号，单位为米；圆弧标识1为圆弧起点，0为普通拐点；起始点坐标要求一致
                </li>
                <li class="example-list-item">
                  坐标必须落在南宁市范围内，（北坐标范围：2456284.585至2660014.159，东坐标范围：36430056.331至36667652.639）
                </li>
                <li class="example-list-item">
                  所上传的测绘成果数据文本参考样例：
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment('项目范围坐标（样例）.txt')
                    "
                  >项目范围坐标（样例）.txt</a>
                </li>
                <li class="example-list-item">
                  根据自然资源部
                  国家保密局关于印发《测绘地理信息管理工作国家秘密范围的规定》的通知（自然资发〔2020〕95号）的要求，“军事禁区以外连续覆盖范围超过
                  25平方千米的大于1:5千的国家基本比例尺地形图（模拟产品）及其全要素数字化成果数据属于涉密成果"。若您的项目范围超过25平方公里，请到南宁市市民中心7楼A738号窗口办理“法人或者其他组织需要利用属于国家秘密的基础测绘成果审批”申请
                </li>
              </ol>
            </el-col>
          </el-row>
          <template
            v-if="
              (ContentInfo.DataCheckState === 1 ||
                ContentInfo.DataCheckState === 3) &&
                baseDataFiles.length
            "
          >
            <!-- 上传测绘成果 -->
            <tip
              v-if="!ContentInfo.AchievementCheckID"
              class="mb-20"
            >请上传测绘成果数据</tip>
            <template v-else>
              <div v-if="ContentInfo.AchievementCheckState === 0" class="mb-20">
                <tip type="default" class="mb-20">
                  测绘成果数据已上传完成，
                  <i
                    class="el-icon-loading mr-5"
                  />系统正在对测绘成果数据进行检查，检查大概需要5分钟，请耐心等待结果
                </tip>
                <timing-progress-bar :is-finished="surveyResultCheckFinished" />
              </div>
              <tip
                v-if="ContentInfo.AchievementCheckState === 1"
                type="success"
                class="mb-20"
              >
                您上传的测绘成果数据符合南宁市不动产测绘成果格式要求，可
                <template
                  v-if="surveyBusinessManager || surveyAdmin"
                >联系本单位注册测绘师</template>登录邕e登App进入“我的授权”模块刷脸确认测绘成果。若想修改，请重新上传测绘成果数据
              </tip>
              <tip
                v-if="ContentInfo.AchievementCheckState === 2"
                type="error"
                class="mb-20"
              >
                您上传的测绘成果数据未能通过检查，请
                <span
                  class="link"
                  @click="downloadSurveyResultErrorReport('拨地定桩')"
                >点击此处</span>下载成果检查报告，待整改后重新上传
              </tip>
            </template>
            <el-row
              v-if="
                !ContentInfo.AchievementCheckID ||
                  (ContentInfo.AchievementCheckID &&
                  ContentInfo.AchievementCheckState > 0)
              "
              :gutter="12"
            >
              <el-col :xs="24" :sm="10" :md="10" :lg="10" :xl="10">
                <el-form-item label="测绘成果数据：" prop="Data">
                  <mdb-upload
                    :id="businessID"
                    :file-list="form3.Data"
                    :file-size="102400"
                    :upload-url="resultUploadUrl"
                    @upload-success="resultUpload"
                  />
                </el-form-item>
              </el-col>
              <el-col
                class="example-container"
                :xs="24"
                :sm="14"
                :md="14"
                :lg="14"
                :xl="14"
              >
                <p class="example-hint">温馨提示：</p>
                <ol class="example-list">
                  <li class="example-list-item">
                    所上传的测绘成果数据内容包括闭合的数据范围面、点号、用地类型
                  </li>
                  <li class="example-list-item">
                    所上传的测绘成果数据文件参考样例：
                    <a
                      class="example-list-item__link"
                      @click="
                        downloadSystemAttachment(
                          '拨地定桩（测绘成果数据样例）.mdb'
                        )
                      "
                    >拨地定桩（测绘成果数据样例）.mdb</a>
                  </li>
                </ol>
              </el-col>
            </el-row>
            <tip
              v-if="!form3.ResultImg.length"
              class="mb-20"
            >请上传测绘成果图</tip>
            <tip
              v-else
              type="success"
              class="mb-20"
            >您的测绘成果图已上传完成</tip>
            <el-form-item label="测绘成果图：" prop="ResultImg">
              <list-upload
                file-format="pdf"
                :file-list="form3.ResultImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '项目成果附件',
                  AttachmentCategories: '测绘成果图',
                }"
                :on-check-format="checkPDF"
                @upload-success="upload($event, 'form3', 'ResultImg')"
                @delete="del($event, 'form3', 'ResultImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
              <div class="example-container">
                <p class="example-hint">温馨提示：</p>
                <ol class="example-list">
                  <li class="example-list-item">
                    所上传的测绘成果图内容包括面积计算表列、项目实际用地面积、用地测量图，在单位名称处加盖公章
                  </li>
                  <li class="example-list-item">
                    所上传的测绘成果数据文件参考样例：
                    <a
                      class="example-list-item__link"
                      @click="
                        downloadSystemAttachment(
                          '拔地定桩（测绘成果图样例）.pdf'
                        )
                      "
                    >拔地定桩（测绘成果图样例）.pdf</a>
                  </li>
                </ol>
              </div>
            </el-form-item>
          </template>
        </el-form>
      </div>
      <div
        v-if="
          currentStep === 4 &&
            mappingCompany &&
            BaseInfo.StateCode !== 2
        "
        class="mb-20"
      >
        <tip
          class="font-20 text-center bold"
        >测绘成果已确认通过，等待业主单位验收成果...</tip>
      </div>
      <!-- 上传项目范围坐标和基础数据下载 -->
      <project-scope
        v-if="showResult('scope')"
        class="mb-20"
        :project-scope="form3.ProjectScope"
        :base-data-files="baseDataFiles"
        :step="currentStep"
        :data-check-state="ContentInfo.DataCheckState"
        :name="FlowInfo.FlowName"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <!-- 测绘成果下载 -->
      <result
        v-if="showResult('result')"
        class="mb-20"
        :survey-result="surveyResult"
        :result-img="form3.ResultImg"
        :show-report="showReport()"
        :name="FlowInfo.FlowName"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep == 3">
        <el-button
          v-if="developer && BaseInfo.StateCode === 0"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="ContentInfo.DataCheckState === -3"
          @click="reSelectSurveyCompany()"
        >
          <i class="iconfont icon-back mr-5" />重新委托测绘单位
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="disabledBack()"
          @click="setReasonDialogVisible('退回业务', true)"
        >
          <i class="iconfont icon-back mr-5" />退回业务
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          type="success"
          :disabled="disabledSubmitAccept()"
          @click="submitAccept(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交验收
        </el-button>
      </template>
      <template
        v-if="currentStep === 4 && developer && BaseInfo.StateCode === 1"
      >
        <el-button
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('验收不通过', true)"
        >
          <i class="iconfont icon-back mr-5" />验收不通过
        </el-button>
        <el-button
          class="ml-10"
          :loading="acceptBtn.loading"
          type="success"
          @click="accept()"
        >
          <i class="el-icon-check mr-5" />验收完成
        </el-button>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2 || (currentStep === 1 && mappingCompany)"
        :base-info="BaseInfo"
        :content-info="ContentInfo"
        :attachment-data="form1"
        :step="currentStep"
        :step-list="stepList"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 额外内容 -->
    <template slot="extra">
      <!-- 提交验收，选择注册测绘师弹窗 -->
      <select-registered-surveyor
        :visible.sync="selectRSurveyorDialog.visible"
        :business-id="BaseInfo.ID"
        :action-id="CurrentAction.ID"
        :flow-name="FlowInfo.FlowName"
        @select="getBusiness"
        @close="setSelectRSurveyorDialogVisible(false)"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import ProjectScopeUpload from "components/business/ProjectScopeUpload/index.vue";
import TimingProgressBar from "components/business/TimingProgressBar/index.vue";
import ViewInfo from "./viewInfo.vue";
import Result from "./result.vue";
import ProjectScope from "./projectScope.vue";
// mixins
import BusinessMixin from "mixins/business/index.js";
import PlanningPointMixin from "mixins/business/planning-point.js";
import SurveyResultMixin from "mixins/business/survey-result.js";
import SelectMappingCompanyMixin from "mixins/business/select-mapping-company.js";
import SelectRegisteredSurveyorMixin from "mixins/business/select-registered-survey.js";
// 校验
import { validateBusinessName, validateAttachment } from "utils/form.js";

export default {
  name: "Staking",
  components: {
    TimingProgressBar,
    ViewInfo,
    Result,
    ProjectScope,
    ProjectScopeUpload,
  },
  mixins: [
    BusinessMixin,
    PlanningPointMixin,
    SurveyResultMixin,
    SelectMappingCompanyMixin,
    SelectRegisteredSurveyorMixin,
  ],
  computed: {
    showScopeUpload() {
      return this.form3.ApplyData.length;
    },
  },
  data() {
    return {
      ApplyDataList: ["地形图", "规划路网"],
      // 步骤1
      form1: {
        BusinessName: null,
        ProjectNumber: null,
        ProjectName: null,
        ProjectAddress: null,
        BlueLineImg: [],
        SurveyNotification: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],
        ProjectNumber: {
          required: true,
          message: "请输入工程建设项目编号",
          trigger: "blur",
        },
        ProjectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        ProjectAddress: [
          { required: true, message: "请输入项目详细地址", trigger: "blur" },
        ],
        BlueLineImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "BlueLineImg",
                "项目用地坐标文件材料"
              ),
            trigger: "change",
          },
        ],
        // TODO:项目测量通知书必填（临时修改，什么时候撤回修改需要确认）
        SurveyNotification: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "SurveyNotification",
                "测量通知书"
              ),
            trigger: "change",
          },
        ],
        // 保存DataCheckState
        defaultDataCheckState: -3,
      },
    };
  },
  destroyed() {
    this.clearTimer(this.resultTimer);
  },
  methods: {
    // 处理表单数据
    handleFormData(currentStep) {
      const {
        BaseInfo,
        ContentInfo,
        Attachments,
        mappingCompany,
        getBaseData,
        getSurveyResultCheckState,
      } = this;
      const { BusinessName } = BaseInfo;
      const {
        ProjectNumber,
        ProjectName,
        ProjectAddress,
        ApplyData,
        DataCheckID,
        DataCheckState,
        AchievementCheckID,
        AchievementCheckState,
      } = ContentInfo;

      // 处理附件
      let BlueLineImg = [];
      let SurveyNotification = [];
      let ProjectScope = [];
      let Others = [];
      let ResultImg = [];

      this.baseDataFiles = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "项目用地坐标文件材料":
                BlueLineImg.push(e);
                break;
              case "测量通知书":
                SurveyNotification.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              case "项目范围坐标":
                ProjectScope.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "基础数据") {
            this.baseDataFiles.push(e);
          }
          if (e.AttachmentType === "项目成果附件") {
            switch (e.AttachmentCategories) {
              case "拨地定桩汇交成果":
                this.surveyResult = e;
                break;
              case "测绘成果图":
                ResultImg.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
        });
      }

      this.form1 = {
        BusinessName,
        ProjectNumber,
        ProjectName,
        ProjectAddress,
        BlueLineImg,
        SurveyNotification,
        Others,
      };

      this.form3 = {
        ApplyData: ApplyData ? ApplyData : [],
        ProjectScope,
        // 判断Data是否有值，AchievementCheckState： 0 检查中  1 检查通过  2 检查不通过
        Data:
          AchievementCheckID && AchievementCheckState > 0
            ? [{ name: "hasResult", status: "success" }]
            : [],
        ResultImg,
      };

      this.defaultDataCheckState = DataCheckState;

      // 审核中定时请求接口
      if (currentStep === 3 && mappingCompany) {
        if (
          (DataCheckState === 1 || DataCheckState === 3) &&
          !this.baseDataFiles.length
        ) {
          getBaseData();
        }
        if (AchievementCheckState === 0 && AchievementCheckID) {
          getSurveyResultCheckState(AchievementCheckID);
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

.link {
  color: $color-primary;
  text-decoration: underline;
  cursor: pointer;
  &:hover {
    color: #09f;
  }
}
</style>
