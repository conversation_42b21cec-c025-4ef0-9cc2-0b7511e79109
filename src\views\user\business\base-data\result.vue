<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="step === 2 || step === 3">
      <template v-if="projectScope.length">
        <div class="result-title mt-0">
          <span>【{{ name }}】项目范围坐标</span>
        </div>
        <div
          v-for="(item, index) in projectScope"
          :key="'projectScope' + index"
          class="result-file flex mb-10"
        >
          <div>{{ item.AttachmentName + item.AttachmentExt }}</div>
          <el-button
            type="primary"
            icon="el-icon-view"
            class="ml-10"
            :loading="scopePreviewDialog.loading"
            @click="previewScope(item)"
          >预览范围</el-button>
        </div>
      </template>
    </template>
    <template v-if="step === 4">
      <template v-if="baseDataFiles.length">
        <div class="result-title mt-0">
          <span>【{{ name }}】数据</span>
        </div>
        <div
          v-for="(item, index) in baseDataFiles"
          :key="'baseDataFile' + index"
          class="result-file flex mb-10"
        >
          <div>
            <span v-if="item.AttachmentLength > 0">{{ item.AttachmentName + item.AttachmentExt }}</span>
            <span v-else>该范围内无可提取的{{ item.AttachmentName }}</span>
          </div>
          <div v-if="item.AttachmentLength > 0">
            <el-button class="ml-10" type="primary" @click="getData(item)">
              <i class="el-icon-download mr-5"></i>提取数据
            </el-button>
          </div>
        </div>
      </template>
      <!-- <template v-if="baseDataFiles.length">
        <el-table ref="table" :data="baseDataFiles" border>
          <el-table-column type="index" label="序号" width="50" align="center" header-align="center"></el-table-column>
          <el-table-column prop="name" label="文件名称" header-align="center">
            <template slot-scope="{ row }">{{ row.AttachmentName + row.AttachmentExt }}</template>
          </el-table-column>
          <el-table-column
            prop="action"
            label="操作"
            align="center"
            header-align="center"
            width="180"
          >
            <template slot-scope="{ row }">
              <el-button size="mini" type="text" icon="el-icon-download" @click="getData(row)">提取数据</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template> -->
    </template>
    <!-- 预览项目范围 -->
    <scope-preview-dialog
      :visible.sync="scopePreviewDialog.visible"
      :coordinates="coordinateList"
      :show-footer="false"
      @cancel="closeScopeDialog"
    />
    <!-- 提取数据弹窗 -->
    <get-data-dialog :visible.sync="getDataDialog.visible" :title="getDataDialog.title" :data="getDataDialog.row" />
  </div>
</template>

<script>
/* eslint-disable */
// mixins
import ScopePreview from "mixins/business/scope-preview.js";
// 组件
import ScopePreviewDialog from "components/business/ScopePreview/index.vue";
import GetDataDialog from "components/business/GetDataDialog/index.vue";

export default {
  name: "PreMappingResult",
  components: {
    ScopePreviewDialog,
    GetDataDialog
  },
  mixins: [ScopePreview],
  props: {
    projectScope: {
      type: Array,
      default: () => []
    },
    baseDataFiles: {
      type: Array,
      default: () => []
    },
    name: {
      type: String,
      default: "测绘数据申请"
    },
    step: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      getDataDialog: {
        visible: false,
        title: "提取测绘数据",
        row: null
      }
    };
  },
  methods: {
    getData(row){
      this.getDataDialog = {
        visible: true,
        title: `提取${row.AttachmentName}`,
        row
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
</style>
