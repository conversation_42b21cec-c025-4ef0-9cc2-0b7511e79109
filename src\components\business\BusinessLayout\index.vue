<template>
  <div class="business-process">
    <div class="business-process__header">
      <!-- 步骤 -->
      <step-nav
        :fixed="fixedStepList"
        :current-step="currentStep"
        :step-list="stepList"
      />
    </div>
    <!-- 提示 -->
    <tips
      class="business-process__tips"
      :state-code="baseInfo.StateCode"
      :current-step="currentStep"
      :current-action="currentAction"
      :extend-info="extendInfo"
    />
    <el-card
      v-loading="downLoading"
      class="business-process__content"
      shadow="never"
      element-loading-text="文件下载中，请稍等..."
    >
      <!-- 步骤内容 -->
      <slot name="stepCotent" />
      <!-- 操作按钮 -->
      <operate-btn
        :current-step="currentStep"
        :step-num="stepList.length"
        :prev-visible="prevBtn.visible"
        :prev-loading="prevBtn.loading"
        :next-visible="nextBtn.visible"
        :next-loading="nextBtn.loading"
        :save-visible="saveBtn.visible"
        :save-loading="saveBtn.loading"
        :close-visible="closeBtn.visible"
        :close-loading="closeBtn.loading"
        :base-info="baseInfo"
        :need-manual-audit="needManualAudit"
        @back-to-list="backToList"
        @reload="reload"
        @prev="prev"
        @next="next"
        @save="save"
        @close="closeBusiness"
      >
        <template slot="before">
          <slot name="operateBtnBefore" />
        </template>
        <template slot="after">
          <slot name="operateBtnAfter" />
        </template>
      </operate-btn>
    </el-card>
    <slot name="viewInfo" />
    <!-- 图片预览 -->
    <img-preview
      :visible.sync="imgDialog.visible"
      :title="imgDialog.title"
      :img-url="imgDialog.imgUrl"
      @close="cancelPreview"
    />
    <!-- PDF预览 -->
    <pdf-preview
      :visible.sync="pdfDialog.visible"
      :title="pdfDialog.title"
      :pdf-url="pdfDialog.pdfUrl"
      @close="cancelPreview"
    />
    <!-- XLS预览 -->
    <!-- <pdf-preview
      :visible.sync="xlsDialog.visible"
      :title="xlsDialog.title"
      :pdf-url="xlsDialog.pdfUrl"
      @close="cancelPreview"
    /> -->
    <!-- 原因弹窗 -->
    <reason-dialog
      :visible.sync="reasonDialog.visible"
      :title="reasonDialog.title"
      :loading="reasonDialog.loading"
      @submit="submitReason"
    />
    <slot name="extra" />
  </div>
</template>

<script>
import StepNav from './StepNav'
import OperateBtn from './OperateBtn'
import ReasonDialog from 'components/business/ReasonDialog/index.vue'
import Tips from './Tips'

export default {
  name: 'BusinessLayout',
  components: {
    StepNav,
    OperateBtn,
    ReasonDialog,
    Tips
  },
  props: {
    // 是否固定步骤列表
    fixedStepList: {
      type: Boolean,
      default: false
    },
    // 当前步骤
    currentStep: {
      type: Number,
      default: 1
    },
    // 步骤列表
    stepList: {
      type: Array,
      default: () => []
    },
    // 基本信息
    baseInfo: {
      type: Object,
      default: () => ({
        BusinessClass: null,
        BusinessName: null,
        BusinessNumber: null,
        BusinessType: null,
        CreatePersonName: null,
        CreatePersonPhone: null,
        DeveloperName: null,
        SurveyCompanyName: null,
        // 0待签收，1办理中，2已完成，3已退回，4已关闭
        StateCode: 0
      })
    },
    // 扩展信息，存放退回和关闭等原因
    extendInfo: {
      type: Object,
      default: () => ({
        CloseReason: null,
        BackReason: null
      })
    },
    // 是否需要人工审核
    needManualAudit: {
      type: Boolean,
      default: false
    },
    // 当前流程
    currentAction: {
      type: Object,
      default: () => {}
    },
    // 下载loding
    downLoading: {
      type: Boolean,
      default: false
    },
    // 上一步按钮
    prevBtn: {
      type: Object,
      default: () => ({
        visible: true,
        loading: false
      })
    },
    // 下一步按钮
    nextBtn: {
      type: Object,
      default: () => ({
        visible: true,
        loading: false
      })
    },
    // 保存按钮
    saveBtn: {
      type: Object,
      default: () => ({
        visible: true,
        loading: false
      })
    },
    // 关闭按钮
    closeBtn: {
      type: Object,
      default: () => ({
        visible: true,
        loading: false
      })
    },
    // 图片预览弹窗
    imgDialog: {
      type: Object,
      default: () => ({
        visible: false,
        title: null,
        imgUrl: null
      })
    },
    // pdf预览弹窗
    pdfDialog: {
      type: Object,
      default: () => ({
        visible: false,
        title: null,
        pdfUrl: null
      })
    },
    // 原因弹窗配置
    reasonDialog: {
      type: Object,
      default: () => ({
        visible: false,
        title: '关闭业务',
        loading: false
      })
    }
  },
  methods: {
    // 返回列表
    backToList(step) {
      this.$emit('back-to-list', step)
    },
    // 刷新页面
    reload(step) {
      this.$emit('reload', step)
    },
    // 上一步
    prev(step) {
      this.$emit('prev', step)
    },
    // 下一步
    next(step) {
      // 提交下一步时判断是否为楼盘信息表变更
      if (
        this.baseInfo.BusinessClass ===
          'RealEstateActualBuildingTableChangeFlow' ||
        this.baseInfo.BusinessClass ===
          'RealEstatePreSurveyBuildingTableChangeFlow'
      ) {
        this.$confirm('您将把变更申请提交到审核部门进行审核', '温馨提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        }).then(async() => {
          this.$emit('next', step)
        })
          .catch((err) => console.log(err))
      } else {
        this.$emit('next', step)
      }
    },
    // 保存
    save(step) {
      this.$emit('save', step)
    },
    // 关闭业务
    closeBusiness() {
      this.$emit('close-business')
    },
    // 取消预览
    cancelPreview(visible) {
      this.$emit('cancel-preview', visible)
    },
    // 原因弹窗提交
    submitReason(reason, title) {
      this.$emit('submit-reason', reason, title)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/business.scss";
</style>
