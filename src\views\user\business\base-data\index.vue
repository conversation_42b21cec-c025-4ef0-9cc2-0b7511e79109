<template>
  <business-layout
    class="base-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="{ visible: false, loading: false }"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="申请单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="业务信息" style="margin-top: -10px">
            <el-form-item label="项目名称：" prop="ProjectName">
              <el-input
                v-model.trim="form1.ProjectName"
                placeholder="请输入项目名称"
                :disabled="BaseInfo.StateCode === 4"
              />
            </el-form-item>
            <el-form-item label="立项依据：" prop="ProjectBasis">
              <el-input
                v-model.trim="form1.ProjectBasis"
                type="textarea"
                :autosize="{ minRows: 4 }"
                placeholder="请输入立项依据"
                :disabled="BaseInfo.StateCode === 4"
              />
            </el-form-item>
            <el-form-item label="申请提取数据：" prop="ApplyData">
              <el-checkbox-group
                v-model="form1.ApplyData"
                :disabled="BaseInfo.StateCode === 4"
              >
                <el-checkbox
                  v-for="(item, index) in ApplyDataList"
                  :key="'apply-data' + index"
                  :label="item"
                />
              </el-checkbox-group>
            </el-form-item>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item label="立项依据文件：" prop="BaseDataList">
              <list-upload
                file-format="pdf"
                :file-list="form1.BaseDataList"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '立项依据文件',
                }"
                :on-check-format="checkPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BaseDataList')"
                @delete="del($event, 'form1', 'BaseDataList')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2">
        <!-- 上传范围 -->
        <el-form ref="form2" :model="form2" :rules="rules2" label-width="120px">
          <!-- DataCheckState -3 上传并检查中（前端定义） 0 未上传 1 通过 2 不通过 -->
          <tip v-if="ContentInfo.DataCheckState === -3" class="mb-20">
            <i class="el-icon-loading mr-5" />系统正在对您上传的项目范围坐标进行检查，检查大概需要5~30秒，请耐心等待结果
          </tip>
          <tip
            v-if="ContentInfo.DataCheckState === 0"
            class="mb-20"
          >请上传项目范围坐标，待系统检查通过后，方可提交</tip>
          <template v-else>
            <tip
              v-if="ContentInfo.DataCheckState === 1"
              type="success"
              class="mb-20"
            >您上传的项目范围坐标已通过系统检查，可提交至科室审核。若想修改，请重新上传项目范围坐标</tip>
            <tip
              v-if="ContentInfo.DataCheckState === 2"
              type="error"
              class="mb-20"
            >
              您上传的项目范围坐标未能通过检查
              <span
                v-if="ContentInfo.FailedReason"
              >，原因：{{ ContentInfo.FailedReason }}</span>，待整改后重新上传
            </tip>
          </template>
          <el-row :gutter="12">
            <el-col
              class="example-container"
              :xs="24"
              :sm="8"
              :md="8"
              :lg="8"
              :xl="8"
            >
              <el-form-item
                :label="
                  ContentInfo.DataCheckState === -3 ? '' : '项目范围坐标：'
                "
                prop="ProjectScope"
              >
                <project-scope-upload
                  :id="businessID"
                  :business-class="BaseInfo.BusinessClass"
                  :file-size="10240"
                  :apply-data="form1.ApplyData"
                  :data-check-state="ContentInfo.DataCheckState"
                  @upload-start="projectScopeUploadStart"
                  @upload-success="projectScopeUploadSuccess"
                  @upload-fail="projectScopeUploadFail"
                />
              </el-form-item>
            </el-col>
            <el-col
              v-if="ContentInfo.DataCheckState !== -3"
              class="example-container"
              :xs="24"
              :sm="16"
              :md="16"
              :lg="16"
              :xl="16"
            >
              <p class="example-hint mt-0">温馨提示：</p>
              <ol class="example-list">
                <li class="example-list-item">
                  所上传的项目范围txt坐标文件的每一行内容应按以下格式：点号,北坐标,东坐标,圆弧标识
                </li>
                <li class="example-list-item">
                  坐标系为CGCS2000大地坐标系，中央经线108°，3度带投影，加带号，单位为米；圆弧标识1为圆弧起点，0为普通拐点；起始点坐标要求一致
                </li>
                <li class="example-list-item">
                  坐标必须落在南宁市范围内，（北坐标范围：2456284.585至2660014.159，东坐标范围：36430056.331至36667652.639）
                </li>
                <li class="example-list-item">
                  所上传的测绘成果数据文本参考样例：<a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment('项目范围坐标（样例）.txt')
                    "
                  >项目范围坐标（样例）.txt</a>
                </li>
                <li class="example-list-item">
                  根据自然资源部
                  国家保密局关于印发《测绘地理信息管理工作国家秘密范围的规定》的通知（自然资发〔2020〕95号）的要求，“军事禁区以外连续覆盖范围超过
                  25平方千米的大于1:5千的国家基本比例尺地形图（模拟产品）及其全要素数字化成果数据属于涉密成果"。若您的项目范围超过25平方公里，请到南宁市市民中心7楼A738号窗口办理“法人或者其他组织需要利用属于国家秘密的基础测绘成果审批”申请
                </li>
              </ol>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div v-if="currentStep === 3">
        <tip
          class="mb-20 font-20 text-center bold"
        >您上传的项目范围已提交至相应科室审核，审核时间为3-5个工作日，请耐心等待结果</tip>
      </div>
      <div v-if="currentStep === 4">
        <tip
          v-if="baseDataUploadTime"
          class="mb-20 font-20 text-center bold"
        >您申请的数据于{{
          baseDataUploadTime
        }}提取完成，请在48小时内点击下方的“提取数据”按钮进行数据提取，过期后不可提取。</tip>
      </div>
      <!-- 测绘数据提取 -->
      <result
        v-if="showResult()"
        class="mb-20"
        :project-scope="form2.ProjectScope"
        :base-data-files="baseDataFiles"
        :step="currentStep"
        :name="FlowInfo.FlowName"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep === 2 && BaseInfo.StateCode === 1">
        <el-button
          class="ml-10"
          :disabled="ContentInfo.DataCheckState === -3"
          :loading="prevBtn.loading"
          type="primary"
          @click="prev(currentStep)"
        >
          <i class="el-icon-caret-left" />上一步
        </el-button>
        <el-button
          class="ml-10"
          :disabled="ContentInfo.DataCheckState !== 1"
          :loading="submitBtn.loading"
          type="success"
          @click="submit(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交
        </el-button>
      </template>
      <!-- TODO:开发和测试库用、正式库隐藏 -->
      <template v-if="currentEnv === 'development' || currentEnv === 'practice'">
        <el-popconfirm
          v-if="currentStep === 3"
          title="确认通过?"
          @onConfirm="audit(currentStep)"
        >
          <el-button
            slot="reference"
            class="ml-10"
            :loading="submitBtn.loading"
            type="success"
          >
            <i class="el-icon-check mr-5" />审核通过(测试用，过程中不可刷新和关闭页面，直接等待)
          </el-button>
        </el-popconfirm>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2"
        :base-info="BaseInfo"
        :content-info="ContentInfo"
        :attachment-data="form1"
        :step="currentStep"
        :step-list="stepList"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import ProjectScopeUpload from "components/business/ProjectScopeUpload/index.vue";
import ViewInfo from "./viewInfo.vue";
import Result from "./result.vue";
// mixins
import BusinessMixin from "mixins/business/index.js";
// Api
import Api from "api/business/base-data.js";
import MyBusinessApi from "api/my-business/index.js";
// 校验
import { validateBusinessName, validateAttachment } from "utils/form.js";
import { scrollTo } from "utils/scroll-to.js";

export default {
  name: "BaseData",
  components: {
    ProjectScopeUpload,
    ViewInfo,
    Result,
  },
  mixins: [BusinessMixin],
  data() {
    return {
      // 业务内容信息，不同业务内容不同
      ContentInfo: {},
      ApplyDataList: ["地形图", "规划路网", "控制性详细规划"],
      // 步骤1
      form1: {
        BusinessName: null,
        ProjectBasis: null,
        ProjectName: null,
        ApplyData: [],
        BaseDataList: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],
        ProjectBasis: [
          { required: true, message: "请输入立项依据", trigger: "blur" },
        ],
        ProjectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        ApplyData: [
          {
            required: true,
            message: "至少选择一项申请数据",
            trigger: "change",
          },
        ],
        BaseDataList: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "BaseDataList",
                "立项依据文件"
              ),
            trigger: "change",
          },
        ],
      },
      // 步骤2
      form2: {
        ProjectScope: [],
      },
      rules2: {
        ProjectScope: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form2",
                "ProjectScope",
                "项目范围"
              ),
            trigger: "change",
          },
        ],
      },
      // 下载状态检查id
      downloadId: null,
      // 定时器
      timer: null,
      // 测绘数据文件
      baseDataFiles: [],
      // 测绘数据提取时间
      baseDataUploadTime: null,
      // 保存DataCheckState
      defaultDataCheckState: -3,
    };
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    handleApiData(Data) {
      Data.ContentInfo.ApplyData = JSON.parse(Data.ContentInfo.ApplyData);
      this.ContentInfo = Data.ContentInfo;
      return true;
    },
    // 处理表单数据
    handleFormData(currentStep) {
      const { BaseInfo, ContentInfo, Attachments, clearTimer } = this;
      const { BusinessName } = BaseInfo;
      const {
        ProjectName,
        ProjectBasis,
        DataCheckID,
        DataCheckState,
        ApplyData,
      } = ContentInfo;

      // 处理附件
      let BaseDataList = [];
      let ProjectScope = [];
      let Others = [];

      this.baseDataFiles = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "立项依据文件":
                BaseDataList.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              case "项目范围坐标":
                ProjectScope.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "基础数据") {
            this.baseDataFiles.push(e);
          }
        });
      }

      this.form1 = {
        BusinessName,
        ProjectBasis,
        ProjectName,
        ApplyData: ApplyData ? ApplyData : [],
        BaseDataList,
        Others,
      };

      this.form2 = { ProjectScope };

      this.defaultDataCheckState = DataCheckState;

      this.baseDataUploadTime = this.baseDataFiles.length
        ? this.baseDataFiles[0].UploadTime
        : null;
    },
    // 处理要提交的业务数据
    handleBusinessData() {
      const { businessID, form1 } = this;
      const { ProjectName, ProjectBasis, BusinessName, ApplyData } = form1;

      this.BaseInfo.BusinessName = BusinessName;

      const data = {
        BaseInfo: this.BaseInfo,
        ContentInfo: {
          ID: businessID,
          ProjectName,
          ProjectBasis,
          ApplyData: JSON.stringify(ApplyData),
        },
      };

      return data;
    },
    // 处理按钮
    handleOperateBtn(currentStep) {
      this.closeBtn.visible = currentStep <= 1 ? true : false;
      this.saveBtn.visible = currentStep <= 1 ? true : false;
      this.nextBtn.visible = currentStep <= 1 ? true : false;
      this.prevBtn.visible = currentStep <= 2 ? true : false;
    },
    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 提交到下一步
    async submit(step) {
      this.GLOBAL.logInfo(`提交第${step}步的内容并校验`);

      const valid = await this.checkStepForm(step);
      if (!valid) return false;

      this.$confirm("确认提交吗？", "温馨提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      })
        .then(async () => {
          this.submitBtn.loading = true;
          const isSaved = await this.saveBusiness(false);
          if (isSaved) {
            Api.SubmitBusiness(this.businessID, this.CurrentAction.ID)
              .then((res) => {
                const { StateCode, Data, Message } = res;

                if (StateCode === 1) {
                  this.$message.success("提交成功");
                  this.refreshStep(Data);
                } else {
                  this.$message.error(Message);
                }
                this.submitBtn.loading = false;
              })
              .catch((err) => {
                console.log(err);
                this.submitBtn.loading = false;
              });
            scrollTo(0);
          }
        })
        .catch((err) => console.log(err));
    },
    // 审核-测试用
    audit(step) {
      MyBusinessApi.AcceptBusiness(this.businessID)
        .then((res) => {
          if (res.StateCode === 1) {
            this.GLOBAL.logInfo(`签收成功`);

            this.submitBtn.loading = true;
            Api.PostEpsDownLoadResult(this.businessID)
              .then((res) => {
                const { Data, StateCode, Message } = res;
                if (StateCode === 1) {
                  this.$message.success(
                    "已提交审核，请耐心等待，勿刷新或关闭页面"
                  );
                  this.downloadId = Data;
                  this.getDownloadCheckState(Data);
                } else this.$message.error(Message);
                this.submitBtn.loading = false;
              })
              .catch((err) => {
                console.log(err);
                this.submitBtn.loading = false;
              });
          }
        })
        .catch((err) => {
          console.log(err);
          this.submitBtn.loading = false;
        });
    },
    // 获取下载状态-测试用
    getDownloadCheckState(checkId) {
      if (this.timer) {
        this.clearTimer();
      }

      const { BusinessClass } = this.BaseInfo;

      this.timer = setInterval(() => {
        Api.GetEPSDownLoadState(checkId, BusinessClass)
          .then((res) => {
            const { Data, StateCode } = res;
            if (StateCode === 1) {
              if (Data > 0) {
                this.clearTimer();
              }
              if (Data === 1) {
                this.$message.success("EPS数据切图成功");
                this.reload();
              }
              if (Data === 2) {
                this.$message.error("EPS数据切图失败");
              }
            } else {
              console.log(res);
            }
          })
          .catch((err) => console.log(err));
      }, 10000);
    },
    // 显示项目范围数据
    showResult() {
      const { currentStep, companyRole, ContentInfo } = this;
      // 系统检查通过
      if (currentStep === 2 && ContentInfo.DataCheckState === 1) {
        return true;
      }
      if (currentStep === 3 || currentStep === 4) {
        return true;
      }
    },
    // 返回列表
    backToList() {
      const { currentStep } = this;

      if (currentStep <= 2) {
        this.$router.push({ name: "BusinessInProgress" });
        return;
      }
      // 审核
      else {
        this.$router.push({ name: "BusinessDone" });
        return;
      }

      this.$router.push({ name: "BusinessInProgress" });
    },
    // 提取数据
    getData(currentStep) {
      this.getDataDialog.visible = true;
    },
    // 开始上传项目范围
    projectScopeUploadStart() {
      this.ContentInfo.DataCheckState = -3;
    },
    // 项目范围上传成功
    projectScopeUploadSuccess(Data, fileList) {
      this.ContentInfo.DataCheckState = Data.ContentInfo.DataCheckState;
      this.refreshStep(Data, this.currentStep);
    },
    // 项目范围上传失败
    projectScopeUploadFail() {
      this.ContentInfo.DataCheckState = this.defaultDataCheckState;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";
.base-data-container {
  /deep/ .step-nav-list__item {
    min-width: 175px;
  }
}
</style>
