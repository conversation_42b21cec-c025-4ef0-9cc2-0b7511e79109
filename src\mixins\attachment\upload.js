/*
 * 模块 : 文件上传相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-24
 * 版本 : version 1.0
 */
/* eslint-disable */
// Api
import Api from "api/public/index.js";
import { getToken } from "utils/auth";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
    data() {
        return {
            defaultImg: require("@/assets/images/no-found-pic.jpg"),
            // 缩略图预览
            thumbnailUrl: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/ShowThumbnail`,
            // 图片预览
            imgUrl: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/ShowImage`,
            // 上传地址
            action: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/AttachmentUploadRequest`,
            // 请求头
            headers: {
                Authorization: 'bearer ' + getToken()
            },
            // 附件id
            attachmentId: null,
            // 超过指定大小的文件需要分片上传（单位KB）
            splitSize: 5,
            // 是否要创建上传记录
            createUpload: true,
        }
    },
    methods: {
        // 设置默认图片
        setDefaultImg(e) {
            e.currentTarget.src = this.defaultImg;
            e.currentTarget.onerror = null;
        },
        // 单位从kb开始计算
        getSize(fileSize) {
            if (fileSize < 1) return `${Math.floor(fileSize * 1024 * 100) / 100}B`;
            return fileSize >= 1024 ? fileSize / 1024 >= 1024 ? `${Math.floor(fileSize / 1024 / 1024 * 100) / 100}G` : `${Math.floor(fileSize / 1024 * 100) / 100}M` : `${Math.floor(fileSize * 100) / 100}KB`;
        },
        // 进度处理
        parsePercentage(val) {
            return parseInt(val, 10);
        },
        // 上传前
        beforeUpload(file) {
            const { $message, fileSize, createUpload, createUploadRecord } = this;

            // 中文和标点替换成3个英文字符（数据库中文代表3个字节）判断文件名长度是否超过300
            const fileName = file.name.replace(
                /[\u4e00-\u9fa5|\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/g,
                "aaa"
            );
            if (fileName.length > 300) {
                $message.warning(`文件名称过长，请修改后重新上传`);
                return false;
            }

            const format = this.onCheckFormat(file);
            if (!format) return false;

            // 控制文件大小
            if (file.size / 1024 > fileSize) {
                $message.warning(`文件大小不能超过${this.getSize(fileSize)}`);
                return false;
            }

            if (createUpload) {
                return new Promise((resolve, reject) => {
                    createUploadRecord().then(res => {
                        resolve(res);
                        file.ID = res.Data;
                        // console.log(file);
                    }).catch(err => reject(err));
                });
            } else {
                return true;
            }
        },
        // 创建上传记录
        createUploadRecord() {
            return new Promise((resolve, reject) => {
                Api.CreateAttachment(this.data)
                    .then(res => {
                        if (res.StateCode === 1) {
                            this.attachmentId = res.Data;
                            this.action = `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/AttachmentUploadRequest?id=${this.attachmentId}`;
                            resolve(res);
                        } else {
                            this.$message.warning("无法上传，请稍后重试");
                            reject(res);
                        }
                    })
                    .catch(err => {
                        reject(err);
                        this.$message.warning("无法上传，请稍后重试");
                    });
            })
        },
        // 上传成功
        uploadSuccess(res, file, fileList) {
            const { StateCode, Data, Message } = res;
            // console.log(res, file, fileList);
            if (StateCode === 1) {
                // 修复因为自定义file-list导致element upload 报错Cannot set property 'status' of null的问题
                let list = fileList.map((e, index) => {
                    if (index === fileList.indexOf(file)) {
                        e = {...Data };
                    }
                    return e;
                });

                this.$message.success("文件上传成功");
                this.$emit("upload-success", list);
            } else {
                console.log(res);
                this.$message.error(Message);
                return false;
            }
        },
        // 上传失败
        uploadFail(res, file, fileList) {
            console.log(res);
            this.$message.error("服务器繁忙，请稍后重试");

            this.del(file);
        },
        // 取消上传
        cancelUpload(file) {
            // console.log(file);
            this.$refs.fileUpload.abort(file);
            this.$refs.fileUpload.uploadFiles.splice(this.$refs.fileUpload.uploadFiles.indexOf(file), 1);
            Api.DeleteAttachment(file.raw.ID).then(res => {
                this.GLOBAL.logInfo("因取消上传需要删除附件");
            }).catch(err => {
                console.log(err);
            });
        }
    }
}