<template>
  <!-- eslint-disable -->
  <div class="user-info-container">
    <tip v-if="!isRealName" type="default" class="mb-20"
      >温馨提示，您还未进行实名认证，请扫描下方
      <span class="link">邕e登APP</span> 二维码安装App进行实名认证。</tip
    >
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="95px"
      class="demo-ruleForm"
    >
      <el-form-item label="姓名：">
        <el-input :value="personName" disabled></el-input>
      </el-form-item>
      <el-form-item label="身份证号：">
        <el-input :value="personNo" disabled></el-input>
      </el-form-item>
      <el-form-item label="手机号码：">
        <el-input :value="userName" disabled></el-input>
      </el-form-item>
      <el-form-item label="旧密码：" prop="OldPassword">
        <el-input
          type="password"
          v-model.trim="form.OldPassword"
          placeholder="请输入旧密码"
          autocomplete="off"
          @keyup.enter.native="submitForm('form')"
        ></el-input>
      </el-form-item>
      <el-form-item label="新密码：" prop="NewPassword">
        <el-input
          type="password"
          v-model.trim="form.NewPassword"
          :placeholder="passwordPla"
          autocomplete="off"
          @keyup.enter.native="submitForm('form')"
        ></el-input>
      </el-form-item>
      <el-form-item label="确认密码：" prop="ConfirmPassword">
        <el-input
          type="password"
          v-model.trim="form.ConfirmPassword"
          placeholder="请输入确认密码"
          autocomplete="off"
          @keyup.enter.native="submitForm('form')"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="submitForm('form')" :loading="loading"
          >提交修改</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
/* eslint-disable */
import { validPassword, isEqual } from "utils/validate";
// Api方法
import Api from "api/user";
import PublicApi from "api/public/index.js";
// vuex
import { mapGetters } from "vuex";

export default {
  name: "UserInfo",
  computed: {
    ...mapGetters(["isRealName", "personName", "userName", "personNo"]),
  },
  data() {
    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入新密码"));
      } else {
        /**
         *  新密码验证规则统一由后端制定
         *  else if (!validPassword(value)) {
         *    callback(new Error("密码格式为6-16位字母或数组的组合"));
         *  }
         */
        callback();
      }
    };
    const validateComfirPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入确认密码"));
      } else if (!isEqual(value, this.form.NewPassword)) {
        callback(new Error("两次输入密码不一致"));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      form: {
        OldPassword: "",
        NewPassword: "",
        ConfirmPassword: "",
      },
      rules: {
        OldPassword: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
        ],
        NewPassword: [
          {
            required: true,
            validator: validateNewPassword,
            trigger: "blur",
          },
        ],
        ConfirmPassword: [
          {
            required: true,
            validator: validateComfirPassword,
            trigger: "blur",
          },
        ],
      },
      passwordPla: "请输入新密码",
    };
  },
  mounted() {
    this.getUsedPasswordRegex();
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.OldPassword == this.form.NewPassword) {
            this.$Message.error("旧密码和新密码不能相同！");
            return false;
          }

          Api.ChangePwd(this.form)
            .then((res) => {
              if (res.StateCode == 1) {
                // 登出 action方法
                this.$store
                  .dispatch("user/logout")
                  .then((res) => {
                    this.$message.success("密码修改成功!请重新登录");
                    this.$router.push({ name: "Home" });
                  })
                  .catch((err) => console.log(err));
              } else this.$message.error(res.Message);
            })
            .catch((err) => {
              console.log(err);
            });
        }
      });
    },
    //获取密码校验规则
    getUsedPasswordRegex() {
      PublicApi.GetUsedPasswordRegex()
        .then((res) => {
          if (res.StateCode == 1) {
            if (
              res.Data !== "" &&
              res.Data !== undefined &&
              res.Data !== null &&
              res.Data.length !== 0
            ) {
              this.passwordPla = `请输入新密码（${res.Data[0].Business}）`;
            }
          } else this.$message.error(res.Message);
        })
        .catch((err) => {
          console.log(err);
          // this.$message.error("服务器繁忙，请稍后重试");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.user-info-container {
  padding: 40px 0;
  width: 500px;
  margin: auto;
}

.link {
  color: $color-primary;
}
</style>
