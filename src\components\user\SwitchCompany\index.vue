<template>
  <!-- eslint-disable -->
  <el-dialog
    title="切换单位"
    :visible="visible"
    :close-on-click-modal="false"
    :append-to-body="true"
    class="switch-company-container"
    width="700px"
    @close="close"
  >
    <div>
      <tip type="default">
        您当前的单位是：<span class="company-name">{{ companyName }}</span
        >，您在该单位的角色为：{{ companyRole }}
      </tip>
      <el-form class="mt-20" label-width="167px" @submit.native.prevent>
        <el-form-item label="请选择您要切换的单位：">
          <el-select
            v-model="selectCompanyID"
            placeholder="请选择单位"
            class="width-100"
          >
            <el-option
              v-for="(item, index) in list"
              :key="index"
              :label="item.CName"
              :value="item.CID"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="confirmSwitch()"
        :loading="switchLoading"
        >确认切换</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
/* eslint-disable */
import { mapGetters } from "vuex";

export default {
  name: "SwitchCompany",
  computed: {
    ...mapGetters([
      "companyList",
      "companyID",
      "companyName",
      "companyRole",
      "isRealName",
    ]),
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      selectCompanyID: null,
      list: [
        [
          {
            State: 0,
            CName: null,
            CID: null,
            CRole: null,
          },
        ],
      ],
      switchLoading: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    init() {
      const { companyList, companyID } = this;
      this.list = [...companyList];

      this.selectCompanyID = companyID;
    },
    async confirmSwitch() {
      this.switchLoading = true;
      try {
        const {
          companyRole,
          companyList,
          selectCompanyID,
          isRealName,
          $route,
        } = this;

        let currentCompany = companyList.find((e) => e.CID === selectCompanyID);

        const { CID, CName, CType, CRole, CNo, State } = currentCompany;

        const company = {
          stateCode: State,
          companyType: CType,
          companyName: CName,
          companyID: CID,
          companyRole: CRole,
          companyNo: CNo,
        };

        await this.$store.dispatch("user/setCompany", company);

        // 修改角色和路由权限
        if (companyRole !== CRole) {
          let newRoles = isRealName ? ["c-d"] : ["0"];

          State === 3 ? newRoles.push("2") : newRoles.push("1");

          CRole === "单位管理员" ? newRoles.push("2-a") : "";

          // 非单位管理员且在“业务转移”页面
          if (
            newRoles.indexOf("2-a") < 0 &&
            $route.name === "TransferBusiness"
          ) {
            // 替换路由
            this.$router.replace({ name: "UserCenter" });
          }
          setTimeout(async () => {
            // 修改路由权限
            await this.$store.dispatch("permission/generateRoutes", {
              roles: newRoles,
            });
            // 修改角色
            await this.$store.dispatch("user/setRoles", newRoles);
          }, 500);
        }

        setTimeout(() => {
          this.$message.success(
            `当前单位已成功切换至【${company.companyName}】`
          );
          this.switchLoading = false;
          this.close();

          // 切换单位后需要回到用户中心的业务路由，否则业务关联的单位名称有误
          if ($route.meta.switchCompanyToUserCenter) {
            this.$router.push({ name: "UserCenter" });
          }
        }, 500);
      } catch (err) {
        console.log(err);
        this.$message.error("切换单位操作失败，请稍后重试");
        this.switchLoading = false;
      }
    },
    close() {
      this.$emit("close", false);
      this.$emit("update:visible", false);
    },
  },
};
</script>
<style lang="scss" scoped>
.switch-company-container {
  /deep/ .el-dialog__body {
    padding: 10px 20px 15px 20px;
  }
}
.company-name {
  color: $color-primary;
  font-weight: bold;
}
</style>

