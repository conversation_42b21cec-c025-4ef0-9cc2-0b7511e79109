<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="`${row ? '编辑' : '添加'}${title}`"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    width="1200px"
    @close="close"
    class="project-licence-store-container"
  >
    <el-form
      v-if="form"
      ref="form"
      :rules="rules"
      class="licence-form"
      :model="form"
      label-width="0"
    >
      <table class="table" cellpadding="0" cellspacing="0">
        <tr>
          <th colspan="3">
            <span class="required">工程规划许可证号</span>
          </th>
          <td colspan="13">
            <el-form-item prop="Code" class="mb-15">
              <el-input
                v-model.trim="form.Code"
                placeholder="请输入工程规划许可证号(示例：450101202050579、4501052024GG0218437)"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <th colspan="3">
            <span class="required">建设单位</span>
          </th>
          <td colspan="7">
            <el-form-item prop="ConstructCompany" class="mb-15">
              <el-input
                v-model="form.ConstructCompany"
                placeholder="请输入建设单位"
              ></el-input>
            </el-form-item>
          </td>
          <th colspan="2">
            <span class="required">
              申请号
              <br />&nbsp;&nbsp;&nbsp;（项目编号）
            </span>
          </th>
          <td colspan="4">
            <el-form-item prop="AppendixImgNumber" class="mb-15">
              <el-input
                v-model.trim="form.AppendixImgNumber"
                placeholder="请输入编号(示例：SGH0101201800000)"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <th colspan="3">
            <span class="required">建设地址</span>
          </th>
          <td colspan="7">
            <el-form-item prop="Address" class="mb-15">
              <el-input
                v-model="form.Address"
                placeholder="请输入建设地址"
              ></el-input>
            </el-form-item>
          </td>
          <th colspan="2">
            <span class="required">项目号</span>
          </th>
          <td colspan="4">
            <el-form-item prop="CaseCode" class="mb-15">
              <el-input
                v-model="form.CaseCode"
                placeholder="请输入项目号"
              ></el-input>
            </el-form-item>
          </td>          
        </tr>
        <tr>
          <th colspan="3">
            <span class="required">审批内容</span>
          </th>
          <td colspan="13">
            <el-form-item prop="SPNR" class="mb-15">
              <el-input
                type="textarea"
                v-model="form.SPNR"
                placeholder="请输入审批内容"
                autosize
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <th colspan="3" class="required">项目名称</th>
          <td colspan="13">
            <el-form-item prop="ProjectName" class="mb-15">
              <el-input
                v-model="form.ProjectName"
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit('form')"
        >确认{{ row ? "编辑" : "添加" }}</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// 验证
import { validProjectLicencee, validAppendixImgNumber,validAgrLicencee} from "utils/validate";
// Api
import Api from "api/business/index.js";

export default {
  name: "ProjectLicenceStoreDialog",

  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前项
    row: {
      type: Object,
      default: "",
    },
    // 索引
    index: {
      type: Number,
      default: -1,
    },
    // 记录列表
    list: {
      type: Array,
      default: () => [],
    },
    // 标题
    title: {
      type: String,
      default: "工程规划许可证",
    },
    // 工规证类型
    licenceType: {
      type: String,
      default: "放线",
    },
    // 业务逻辑类
    businessClass: {
      type: String,
      default: null,
    },
  },
  data() {
    const validateCode = (rule, value, callback) => {
      // AgrLicenceNo
      const { businessClass } = this;
      //不动产预核业务即时办理流程
      if (businessClass === "RealEstatePreCheckSurveyAutoFlow") {
          if (!value) {
          callback(new Error("请输入工程规划许可证号"));
        } 
        else if (!validAgrLicencee(value)) {
          callback(new Error("格式错误，示例：450021202100001"));
        } 
        else {
          callback();
        }
      } else {
        if (!value) {
          callback(new Error("请输入工程规划许可证号"));
        } else if (!validProjectLicencee(value)) {
          callback(new Error("格式错误，示例：450101201001244"));
        } else {
          callback();
        }
      }
    };

    // const validAppendixNumber = (rule, value, callback) => {
    //   if (!value) {
    //     callback(new Error("请输入附图编号（项目编号）"));
    //   } else if (!validAppendixImgNumber(value)) {
    //     callback(new Error("格式错误，示例：GC0101201001700"));
    //   } else {
    //     callback();
    //   }
    // };

    return {
      defaultLicenceForm: {
        //是否自行添加
        IsAddNew:true,
        // 工程规划许可证号
        Code: "",
        // 建设单位
        ConstructCompany: "",
        // 建设地址
        Address: "",
        // 项目名称
        ProjectName: "",
        // 附图编号
        AppendixImgNumber: "",
        //报建编号（项目号）
        CaseCode: "",
        //审批内容
        SPNR: "",
        Add: true,
      },
      form: "",
      // 没修改前的建筑类别
      defaultBuildings: [],
      rules: {
        Code: [
          {
            required: true,
            validator: validateCode,
            trigger: "blur",
          },
        ],
        ConstructCompany: [
          {
            required: true,
            message: "请输入建设单位",
            trigger: "blur",
          },
        ],
        Address: [
          {
            required: true,
            message: "请输入建设地址",
            trigger: "blur",
          },
        ],
        ProjectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        AppendixImgNumber: [
          {
            required: true,
            // validator: validAppendixNumber,
            message: "请输入申请号（项目编号）",
            trigger: "blur",
          },
        ],
        CaseCode: [
          {
            required: true,
            message: "请输入项目号",
            trigger: "blur",
          },
        ],
        SPNR: [
          {
            required: true,
            message: "请输入审批内容",
            trigger: "blur",
          },
        ],
      },
      NumberReg: /^[0-9]+.?[0-9]*$/,
      edit: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init("工程规划许可证");
      }
    },
  },
  methods: {
    // 初始化
    init(name) {
      const { row, defaultLicenceForm } = this;
      this.form = JSON.parse(JSON.stringify(row ? row : defaultLicenceForm));
      this.defaultBuildings = this.row ? [...this.row.Buildings] : [];
      console.log(this);
    },
    // 重置表单
    reset() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // 关闭存储弹窗
    close() {
      const { form, edit, index, defaultBuildings } = this;

      this.reset();
      this.edit = false;
      this.$emit("cancel");
      this.$emit("update:visible", false);
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { form, index, row, list } = this;
          const {
            Buildings
          } = form;

          let error = false;

          let params = { ...form };

          const isExist = list.find((e) => e.Code === form.Code);
          if (isExist && !row) {
            this.$message.warning("该工程规划许可证已存在");
            return false;
          }

          this.edit = true;
          this.$emit("submit", params, index);
          this.$emit("update:visible", false);
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
    limitMinValue(value, attr, attr2 = "") {
      if (value < 0 || !this.NumberReg.test(value)) {
        if (attr2) {
          this.form[attr][attr2] = 0;
          return;
        }

        this.form[attr] = 0;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-bottom: 10px;
  padding-top: 10px;
}

.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  th {
    background: #f8f8f8;
    color: #909399;
  }
  th,
  td {
    text-align: center;
    padding: 5px 0;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
  }

  td {
    .table-div:last-child {
      div {
        border-bottom: none;
      }
    }
  }

  .th-fixed-width {
    min-width: 65px;
    width: 65px;
    max-width: 65px;
  }

  &-add-btn {
    // border: 1px dashed #dfe6ec;
    width: 100%;
    // border-radius: 4px;
    text-align: center;
    margin-top: -1px;
    height: 45px;
    line-height: 45px;
    cursor: pointer;
    position: relative;
    z-index: 10;
    color: $color-primary;
    margin-top: -1px;

    // &:hover {
    //   border-color: $color-primary;
    // }

    & > i {
      margin-right: 5px;
    }
  }

  &-del-btn {
    min-width: 65px;
    padding: 5px;
    line-height: inherit;
    color: $color-primary;
    cursor: pointer;
  }
}

.table-div {
  width: 100%;
  display: table;
  line-height: 1;
  min-height: 35px;

  div {
    display: table-cell;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    padding: 5px 0;

    &:last-child {
      border-right: none;
    }
  }
}

.licence-form {
  /deep/ .el-input__inner {
    border: none;
    padding: 5px 10px;
  }

  /deep/ .el-input {
    border: none;
    padding: 0;
  }

  /deep/ .el-textarea {
    border: none;
  }

  /deep/ .el-textarea__inner {
    border: none;
    padding: 5px;
    min-height: 40px !important;
    // height: auto !important;
  }

  /deep/ .el-form-item {
    margin-bottom: 0;
    padding: 0;
    border: none;
  }
  /deep/ .el-form-item__error {
    padding-left: 10px;
  }
}

.td-input {
  /deep/ .el-form-item__error {
    padding-left: 0;
  }
}

.required {
  &::before {
    content: "*";
    color: #ff4949;
    margin-right: 4px;
  }
}
</style>