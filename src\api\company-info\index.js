
/* eslint-disable */
import request from 'utils/request';

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
    // 获取正在申请的单位注册信息
	GetCompanyRegisterInfo: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetRequesting`,
            method: 'get'
		})
	},
	// 逻辑删除单位申请信息，仅测试使用
	DeleteCompanyRequest: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/DeleteCompanyRequest?id=${id}`,
            method: 'post'
		})
	},
	// 修改单位申请信息状态，仅测试使用
	UpdateCompanyStateCode: (id, satateCode) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/UpdateCompanyRequestStateCode?id=${id}&stateCode=${satateCode}`,
            method: 'post'
		})
	},
	// 保存单位申请信息
	SaveCompanyInfo: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/SaveCompanyRequest`,
			method: 'post',
			data
		})
	},
	// 单位注册-基本信息提交(创建单位注册申请)
	CompanyRegister: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/CreateNewCompanyRequest`,
			method: 'post',
			data
		})
	},
	// 单位注册-提交单位信息至审核
	SubmitCompany: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/SubmitCompanyAuditRequest?id=${id}`,
			method: 'post'
		})
	},
	// 获取注册成功后的单位信息
	GetCompanyDetailsInfo: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetCompanyDetailsInfo?id=${id}`,
			method: 'post'
		})
	},
	// 撤销单位申请
	CloseCompanyAudit: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/CloseCompanyAuditRequest`,
            method: 'post',
            data
		})
	},
	// 获取建设单位人员信息
	GetMemberList: (id, PageIndex, PageSize) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetDeveloperUserList?id=${id}&PageIndex=${PageIndex}&PageSize=${PageSize}`,
            method: 'get'
		})
	},
	// 建设单位人员信息保存
	StoreMember: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/SaveDeveloperUserInfo`,
            method: 'post',
            data
		})
	},
	// 建设单位人员信息删除
	DeleteMember: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/DeleteDeveloperUserInfo?id=${id}`,
            method: 'post'
		})
	},
	// 从邕e登读取建设单位已备案信息和人员信息
	GetRealEstateDeveloperCompanyInfo: (companyNo) => {
		return request({
			url: `${VUE_APP_SERVER_API}/suapi/GetRealEstateDeveloperCompanyInfo?companyNo=${companyNo}`,
            method: 'get'
		})
	},
	// 离职
	LeaveTheCompany: (companyId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/LeaveTheCompany?companyId=${companyId}`,
            method: 'post'
		})
	},
	// 获取多测合一业务类型，新接口
	GetSurveyFlows: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetSurveyFlows`,
            method: 'get'
		})
	},

	// 检查测绘单位注销是否满足注销条件
	CheckCompanyCanWithDraw: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyWithDraw/CheckCanWithDraw`,
			method: 'post'
		})
	},
	// 提交测绘单位注销名录库申请
	CreateCompanyWithDrawInfo: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyWithDraw/CreateSurveyCompanyWithDraw`,
			method: 'post',
			data
		})
	},
}
