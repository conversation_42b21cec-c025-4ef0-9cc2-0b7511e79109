<template>
  <business-layout
    class="pre-mapping-merge-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="prevBtn"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @prev="prev"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1 && developer">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业主单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="业务信息" style="margin-top: -10px">
            <el-form-item
              label-width="205px"
              label="工程规划许可证（放线）："
              prop="PutLineProjectPlanPermission"
            >
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="form1.PutLineProjectPlanPermission"
                :default-props="tableProps"
                :show-pagination="false"
              >
                <el-table-column
                  prop="action"
                  label="操作"
                  width="180"
                  fixed="right"
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index, '放线')"
                    >查看</el-button>
                    <el-button
                      v-if="row.Add && BaseInfo.StateCode !== 4"
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="storeLicence(row, $index, '放线')"
                    >编辑</el-button>
                    <el-popconfirm
                      v-if="BaseInfo.StateCode !== 4"
                      title="确认删除?"
                      @onConfirm="delLicence(row, $index, '放线')"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        class="ml-10"
                      >删除</el-button>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </dynamic-table>
              <div
                v-if="BaseInfo.StateCode !== 4"
                class="table-add-btn"
                @click="setLicenceGetDialogVisible(true, '放线')"
              >
                <i class="el-icon-plus" />添加工程规划许可证（放线）
              </div>
            </el-form-item>
            <el-form-item
              label-width="205px"
              label="工程规划许可证（预测绘）："
              prop="PreProjectPlanPermission"
            >
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="form1.PreProjectPlanPermission"
                :default-props="tableProps"
                :show-pagination="false"
              >
                <el-table-column
                  prop="action"
                  label="操作"
                  width="180"
                  fixed="right"
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index, '预测绘')"
                    >查看</el-button>
                    <el-button
                      v-if="row.Add && BaseInfo.StateCode !== 4"
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="storeLicence(row, $index, '预测绘')"
                    >编辑</el-button>
                    <el-popconfirm
                      v-if="BaseInfo.StateCode !== 4"
                      title="确认删除?"
                      @onConfirm="delLicence(row, $index, '预测绘')"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        class="ml-10"
                      >删除</el-button>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </dynamic-table>
              <div
                v-if="BaseInfo.StateCode !== 4"
                class="table-add-btn"
                @click="setLicenceGetDialogVisible(true, '预测绘')"
              >
                <i class="el-icon-plus" />添加工程规划许可证（预测绘）
              </div>
            </el-form-item>
            <el-form-item
              label-width="205px"
              label="不动产单元号（宗地号）："
              prop="GroundCode"
            >
              <el-input
                v-model.trim="form1.GroundCode"
                placeholder="宗地号为19位宗地统一代码，如：450103001001GB00001"
                :disabled="BaseInfo.StateCode === 4"
              />
            </el-form-item>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item
              label-width="170px"
              label="工程规划许可证件："
              prop="ProjectLicenceImg"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.ProjectLicenceImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '工程规划许可证件',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'ProjectLicenceImg')"
                @delete="del($event, 'form1', 'ProjectLicenceImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="建筑定位图："
              prop="BuildingLocationMap"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.BuildingLocationMap"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '建筑定位图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BuildingLocationMap')"
                @delete="del($event, 'form1', 'BuildingLocationMap')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="总平面图："
              prop="SitePlan"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.SitePlan"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '总平面图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'SitePlan')"
                @delete="del($event, 'form1', 'SitePlan')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="经备案的建筑设计图："
              prop="BuildingDesgin"
            >
              <list-upload
                file-format="dwg"
                :file-list="form1.BuildingDesgin"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '经备案的建筑设计图',
                }"
                :on-check-format="checkDWG"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'BuildingDesgin')"
                @delete="del($event, 'form1', 'BuildingDesgin')"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item
              label-width="170px"
              label="门牌证明："
              prop="DoorPlate"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.DoorPlate"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '门牌证明',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'DoorPlate')"
                @delete="del($event, 'form1', 'DoorPlate')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label-width="170px" label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar / dwg"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2 && developer">
        <!-- 委托测绘单位 -->
        <mapping-company-list
          class="mb-20"
          :business-class="BaseInfo.BusinessClass"
          :business-id="businessID"
          :current-action-id="CurrentAction.ID"
          :flow-name="FlowInfo.FlowName"
          @select="refreshStep"
        />
      </div>
      <div v-if="currentStep === 3">
        <!-- 上传测绘成果 -->
        <tip
          v-if="developer"
          class="mb-20 font-20 text-center bold"
        >等待测绘单位汇交并确认测绘成果...</tip>
        <el-form
          v-if="mappingCompany"
          ref="form3"
          class="mb-40"
          :model="form3"
          :rules="rules3"
          label-width="100px"
        >
          <!-- 提示语 -->
          <tip
            v-if="!PreSurveyContentInfo.DataCheckID"
            class="mb-20"
          >请上传测绘成果</tip>
          <template v-else>
            <div v-if="PreSurveyContentInfo.DataCheckState === 0" class="mb-20">
              <tip type="default" class="mb-20">
                测绘成果已上传完成，
                <i
                  class="el-icon-loading mr-5"
                />系统正在对成果进行检查，检查大概需要5分钟，请耐心等待结果
              </tip>
              <timing-progress-bar :is-finished="surveyResultCheckFinished" />
            </div>
            <tip
              v-if="PreSurveyContentInfo.DataCheckState === 1"
              type="success"
              class="mb-20"
            >
              您上传的测绘成果符合南宁市不动产测绘成果格式要求，可
              <template
                v-if="surveyBusinessManager || surveyAdmin"
              >联系本单位注册测绘师</template>登录邕e登App进入“我的授权”模块刷脸确认测绘成果。若想修改，请重新上传测绘成果
            </tip>
            <tip
              v-if="PreSurveyContentInfo.DataCheckState === 2"
              type="error"
              class="mb-20"
            >
              您上传的测绘成果未能通过检查，请
              <span
                class="link"
                @click="downloadSurveyResultErrorReport('不动产预测绘')"
              >点击此处</span>下载成果检查报告，待整改后重新上传
            </tip>
          </template>
          <!-- 附件信息 -->
          <el-collapse value="1" class="mb-20">
            <el-collapse-item title="申请信息附件" name="1">
              <attachment-info
                v-if="mappingCompany"
                class="mt-10"
                :base-info="BaseInfo"
                :step="currentStep"
                :step-list="stepList"
                :attachment-data="form1"
                :actions-infos="ActionsInfos"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-collapse-item>
          </el-collapse>
          <!-- 上传按钮 -->
          <el-row
            v-if="
              !PreSurveyContentInfo.DataCheckID ||
                (PreSurveyContentInfo.DataCheckID &&
                PreSurveyContentInfo.DataCheckState > 0)
            "
            :gutter="12"
          >
            <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label-width="100px" label="测绘成果：" prop="Data">
                <mdb-upload
                  :id="businessID"
                  :file-list="form3.Data"
                  :file-size="102400"
                  :upload-url="resultUploadUrl"
                  @upload-success="resultUpload"
                />
              </el-form-item>
            </el-col>
            <el-col
              class="example-container"
              :xs="24"
              :sm="12"
              :md="12"
              :lg="12"
              :xl="12"
            >
              <p class="example-hint">温馨提示：</p>
              <ol class="example-list">
                <li class="example-list-item">
                  所上传的测绘成果须符合
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment('不动产预测绘数据汇交标准.docx')
                    "
                  >《南宁市不动产预测绘和放线测量数据汇交标准》</a>
                </li>
                <li class="example-list-item">
                  所上传的测绘成果文件参考样例：
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment(
                        '不动产预测绘（测绘成果样例）.mdb'
                      )
                    "
                  >不动产预测绘（测绘成果样例）.mdb</a>
                </li>
                <li class="example-list-item">
                  放线测量报告模板：
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment(
                        '南宁市建设工程规划放线测量报告.doc'
                      )
                    "
                  >《南宁市建设工程规划放线测量报告》</a>
                </li>
                <li class="example-list-item">
                  如有疑问，请联系南宁市不动产登记中心测绘管理部，联系电话：
                  <a class="example-list-item__phone">4306662</a>
                </li>
              </ol>
            </el-col>
          </el-row>
          <!-- 上传放线报告 -->
          <tip v-if="!form3.Report.length" class="mb-20">请上传放线报告</tip>
          <tip v-else type="success" class="mb-20">您的放线报告已上传完成</tip>
          <el-form-item label="放线报告：" prop="Report">
            <list-upload
              file-format="pdf"
              :file-list="form3.Report"
              :file-size="102400"
              :data="{
                BusinessType: BaseInfo.BusinessType,
                BusinessID: businessID,
                AttachmentType: '项目成果附件',
                AttachmentCategories: '放线报告',
              }"
              :on-check-format="checkPDF"
              @upload-success="upload($event, 'form3', 'Report')"
              @delete="del($event, 'form3', 'Report')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-form-item>
          <!-- 不动产预核报告 -->
          <tip
            v-if="!form3.PreReport.length"
            class="mb-20"
          >请上传不动产预核报告</tip>
          <tip
            v-else
            type="success"
            class="mb-20"
          >您的不动产预核报告已上传完成</tip>
          <el-form-item
            label-width="145px"
            label="不动产预核报告："
            prop="PreReport"
          >
            <list-upload
              file-format="pdf"
              :file-list="form3.PreReport"
              :file-size="102400"
              :data="{
                BusinessType: BaseInfo.BusinessType,
                BusinessID: businessID,
                AttachmentType: '项目成果附件',
                AttachmentCategories: '不动产预核报告',
              }"
              :on-check-format="checkPDF"
              @upload-success="upload($event, 'form3', 'PreReport')"
              @delete="del($event, 'form3', 'PreReport')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="currentStep === 4 && mappingCompany" class="mb-20">
        <tip
          class="font-20 text-center bold"
        >测绘成果已确认通过，等待业主单位验收成果...</tip>
      </div>
      <div v-if="currentStep === 5 && BaseInfo.StateCode !== 2" class="mb-20">
        <tip class="font-20 text-center bold">
          成果验收完成，
          <i
            class="el-icon-loading mr-5"
          />已提交南宁市不动产登记中心检查（3个工作日）
        </tip>
      </div>
      <!-- 测绘成果下载 -->
      <result
        v-if="showResult()"
        class="mb-20"
        :survey-result="surveyResult"
        :property-info="propertyInfo"
        :report="form3.Report"
        :pre-report="form3.PreReport"
        :audit-feedback="auditFeedback"
        :building-table-info="PreSurveyContentInfo.BuildingTableInfo"
        :project-result-info="PreSurveyContentInfo.ProjectResultInfo"
        :name="FlowInfo.FlowName"
        :step="currentStep"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep == 3">
        <el-button
          v-if="developer && BaseInfo.StateCode === 0"
          :loading="prevBtn.loading"
          type="primary"
          @click="reSelectSurveyCompany()"
        >
          <i class="iconfont icon-back mr-5" />重新委托测绘单位
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="disabledBack()"
          @click="setReasonDialogVisible('退回业务', true)"
        >
          <i class="iconfont icon-back mr-5" />退回业务
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          type="success"
          :disabled="disabledAccept()"
          @click="submitAccept(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交验收
        </el-button>
      </template>
      <template
        v-if="currentStep === 4 && developer && BaseInfo.StateCode === 1"
      >
        <el-button
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('验收不通过', true)"
        >
          <i class="iconfont icon-back mr-5" />验收不通过
        </el-button>
        <el-button
          class="ml-10"
          :loading="acceptBtn.loading"
          type="success"
          @click="accept()"
        >
          <i class="el-icon-check mr-5" />验收完成
        </el-button>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2 || (currentStep === 1 && mappingCompany)"
        :base-info="BaseInfo"
        :pre-project-plan-permission="form1.PreProjectPlanPermission"
        :put-line-project-plan-permission="form1.PutLineProjectPlanPermission"
        :content-info="PreSurveyContentInfo"
        :attachment-data="form1"
        :step="currentStep"
        :step-list="stepList"
        :actions-infos="ActionsInfos"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
        @view-licence="viewLicence"
      />
    </template>
    <!-- 额外内容 -->
    <template slot="extra">
      <!-- 工程规划许可证预览 -->
      <el-dialog
        :title="licenceViewDialog.title"
        :visible="licenceViewDialog.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
        width="1100px"
        @close="licenceViewDialog.visible = false"
      >
        <project-licence-view
          :row="licenceViewDialog.row"
          :index="licenceViewDialog.index"
        />
      </el-dialog>
      <!-- 获取工程规划许可证 -->
      <project-licence-get-dialog
        :title="licenceGetDialog.title"
        :visible.sync="licenceGetDialog.visible"
        :licence-type="licenceGetDialog.licenceType"
        :list="form1.ProjectPlanPermission"
        :business-id="businessID"
        :business-class="BaseInfo.BusinessClass"
        @add-lience="needAddLicence"
        @submit="licenceGetSuccess"
        @close="setLicenceGetDialogVisible(false)"
      />
      <!-- 存储工程规划许可证 -->
      <project-licence-store-dialog
        :title="licenceStoreDialog.title"
        :visible.sync="licenceStoreDialog.visible"
        :licence-type="licenceGetDialog.licenceType"
        :row="licenceStoreDialog.row"
        :index="licenceStoreDialog.index"
        :list="form1.ProjectPlanPermission"
        @submit="licenceStoreSuccess"
        @close="setLicenceStoreDialogVisible(false)"
      />
      <!-- 提交验收，选择注册测绘师弹窗 -->
      <select-registered-surveyor
        :visible.sync="selectRSurveyorDialog.visible"
        :business-id="BaseInfo.ID"
        :action-id="CurrentAction.ID"
        :flow-name="FlowInfo.FlowName"
        @select="getBusiness"
        @close="setSelectRSurveyorDialogVisible(false)"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import TimingProgressBar from "components/business/TimingProgressBar/index.vue";
import ViewInfo from "./view-info/index.vue";
import AttachmentInfo from "./view-info/attachmentInfo.vue";
import Result from "./result.vue";

import ProjectLicenceView from "components/business/ProjectLicence/View.vue";
import ProjectLicenceGetDialog from "components/business/ProjectLicence/Get.vue";
import ProjectLicenceStoreDialog from "components/business/ProjectLicence/Store.vue";
import DynamicTable from "components/common/Table/DynamicTable";
// mixins
import BusinessMixin from "mixins/business/index.js";
// import CheckLineMixin from "mixins/business/check-line.js";
import SurveyResultMixin from "mixins/business/survey-result.js";
import SelectMappingCompanyMixin from "mixins/business/select-mapping-company.js";
import SelectRegisteredSurveyorMixin from "mixins/business/select-registered-survey.js";
// 校验
import {
  validateBusinessName,
  validateAttachment,
  validateGroundCode,
} from "utils/form.js";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  name: "PreMappingMerge",
  components: {
    TimingProgressBar,
    ViewInfo,
    AttachmentInfo,
    Result,

    ProjectLicenceView,
    ProjectLicenceGetDialog,
    ProjectLicenceStoreDialog,
    DynamicTable,
  },
  mixins: [
    BusinessMixin,
    // CheckLineMixin,
    SurveyResultMixin,
    SelectMappingCompanyMixin,
    SelectRegisteredSurveyorMixin,
  ],
  data() {
    return {
      // 预测绘内容
      PreSurveyContentInfo: {
        ProjectPlanPermission: [],
      },
      // 放线测量内容
      PutLineContentInfo: {
        ProjectPlanPermission: [],
      },
      // 步骤1
      form1: {
        BusinessName: null,
        GroundCode: null,
        PutLineProjectPlanPermission: [],
        PreProjectPlanPermission: [],
        ProjectLicenceImg: [],
        BuildingLocationMap: [],
        SitePlan: [],
        BuildingDesgin: [],
        DoorPlate: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],
        GroundCode: [
          { required: true, validator: validateGroundCode, trigger: "blur" },
        ],
        PutLineProjectPlanPermission: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !this.form1.PutLineProjectPlanPermission ||
                !this.form1.PutLineProjectPlanPermission.length
              ) {
                callback(new Error("请添加工程规划许可证（放线）"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        PreProjectPlanPermission: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !this.form1.PreProjectPlanPermission ||
                !this.form1.PreProjectPlanPermission.length
              ) {
                callback(new Error("请添加工程规划许可证（预测绘）"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        ProjectLicenceImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "ProjectLicenceImg",
                "工商许可证件"
              ),
            trigger: "change",
          },
        ],
        BuildingLocationMap: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "BuildingLocationMap",
                "建筑定位图"
              ),
            trigger: "change",
          },
        ],
        SitePlan: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "SitePlan",
                "总平面图"
              ),
            trigger: "change",
          },
        ],
        BuildingDesgin: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "BuildingDesgin",
                "经备案的建筑设计图"
              ),
            trigger: "change",
          },
        ],
        DoorPlate: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "DoorPlate",
                "门牌证明"
              ),
            trigger: "change",
          },
        ],
      },
      // 步骤3
      form3: {
        Data: [],
        Report: [],
        PreReport: [],
      },
      rules3: {
        Data: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Data",
                "测绘成果"
              ),
            trigger: "change",
          },
        ],
        Report: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Report",
                "放线报告"
              ),
            trigger: "change",
          },
        ],
        PreReport: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "PreReport",
                "不动产预核报告"
              ),
            trigger: "change",
          },
        ],
      },
      // 工程规划许可证获取弹窗配置
      licenceGetDialog: {
        visible: false,
        licenceType: "放线",
        title: "工程规划许可证",
      },
      // 工程规划许可证自行添加弹窗配置
      licenceStoreDialog: {
        visible: false,
        licenceType: "放线",
        row: null,
        index: -1,
      },
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title",
      },
      licenceTableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50",
        },
        {
          title: "工程规划许可证号",
          key: "Code",
          align: "center",
        },
        {
          title: "建设单位",
          key: "ConstructCompany",
          align: "center",
        },
        {
          title: "项目名称",
          key: "ProjectName",
          align: "center",
        },
      ],
      // 工程规划许可证查看弹窗配置
      licenceViewDialog: {
        visible: false,
        row: null,
        index: -1,
      },
      // 定时器
      timer: null,
      // 楼盘信息表pdf
      propertyInfo: null,
      // 成果审核反馈
      auditFeedback: [],
      // 测绘成果上传地址
      resultUploadUrl: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostSurveyProjectResult`,
    };
  },
  destroyed() {
    this.clearTimer(this.resultTimer);
  },
  methods: {
    // 额外处理请求数据
    handleApiData(Data) {
      this.PreSurveyContentInfo = {
        ...Data.PreSurveyContentInfo,
        ProjectPlanPermission: Data.PreSurveyContentInfo.ProjectPlanPermission
          ? JSON.parse(Data.PreSurveyContentInfo.ProjectPlanPermission)
          : [],
        BuildingTableInfo: Data.PreSurveyContentInfo.BuildingTableInfo
          ? JSON.parse(Data.PreSurveyContentInfo.BuildingTableInfo)
          : [],
        ProjectResultInfo: Data.PreSurveyContentInfo.ProjectResultInfo
          ? JSON.parse(Data.PreSurveyContentInfo.ProjectResultInfo)
          : [],
      };

      this.PutLineContentInfo = {
        ...Data.PutLineContentInfo,
        ProjectPlanPermission: Data.PutLineContentInfo.ProjectPlanPermission
          ? JSON.parse(Data.PutLineContentInfo.ProjectPlanPermission)
          : [],
      };
    },
    // 更新步骤
    refreshStep(Data, step = null) {
      const {
        BaseInfo,
        PreSurveyContentInfo,
        PutLineContentInfo,
        FlowInfo,
        ActionsInfos,
        Attachments,
        CurrentAction,
      } = Data;

      this.BaseInfo = BaseInfo;
      this.Attachments = Attachments;
      this.CurrentAction = CurrentAction;
      this.FlowInfo = FlowInfo;
      this.ActionsInfos = ActionsInfos;
      const ExtendInfo = JSON.parse(BaseInfo.ExtendInfo);
      this.ExtendInfo = { ...this.ExtendInfo, ...ExtendInfo };

      // 额外处理请求数据
      this.handleApiData(Data);

      const Actions = [...FlowInfo.FlowActionInfo.Actions];
      const index = Actions.findIndex((e) => e.ID === CurrentAction.ActionId);

      const currentStep = step ? step : index > 0 ? index + 1 : 1;
      this.handleStepList(currentStep, FlowInfo.FlowActionInfo.Actions);

      if (PreSurveyContentInfo && PutLineContentInfo) {
        this.handleFormData(currentStep);
      }
    },
    // 处理表单数据
    handleFormData(currentStep) {
      const {
        BaseInfo,
        PreSurveyContentInfo,
        PutLineContentInfo,
        Attachments,
        mappingCompany,
        getSurveyResultCheckState,
      } = this;
      const { BusinessName } = BaseInfo;
      const { DataCheckID, DataCheckState, GroundCode } = PreSurveyContentInfo;

      // 处理附件
      let ProjectLicenceImg = [];
      let BuildingLocationMap = [];
      let SitePlan = [];
      let WorkingDraw = [];
      let BuildingDesgin = [];
      let DoorPlate = [];
      let Others = [];
      let Report = [];
      let PreReport = [];
      this.auditFeedback = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "工程规划许可证件":
                ProjectLicenceImg.push(e);
                break;
              case "建筑定位图":
                BuildingLocationMap.push(e);
                break;
              case "总平面图":
                SitePlan.push(e);
                break;
              case "全套建筑施工图":
                WorkingDraw.push(e);
                break;
              case "经备案的建筑设计图":
                BuildingDesgin.push(e);
                break;
              case "门牌证明":
                DoorPlate.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "项目成果附件") {
            switch (e.AttachmentCategories) {
              case "放线报告":
                Report.push(e);
                break;
              case "不动产预核报告":
                PreReport.push(e);
                break;
              case "不动产预核测绘成果":
              case "不动产预核业务成果":
              case "不动产测绘成果":
                this.surveyResult = e;
                break;
              case "楼盘信息表":
                this.propertyInfo = e;
                break;
              case "不动产预核测绘成果审核反馈":
                this.auditFeedback.push(e);
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
        });
      }

      this.form1 = {
        BusinessName,
        GroundCode,
        PutLineProjectPlanPermission: PutLineContentInfo.ProjectPlanPermission,
        PreProjectPlanPermission: PreSurveyContentInfo.ProjectPlanPermission,
        ProjectLicenceImg,
        BuildingLocationMap,
        SitePlan,
        WorkingDraw,
        BuildingDesgin,
        DoorPlate,
        Others,
      };

      this.form3 = {
        // 判断Data是否有值，DataCheckState： 0 检查中  1 检查通过  2 检查不通过
        Data:
          DataCheckID && DataCheckState > 0
            ? [{ name: "hasResult", status: "success" }]
            : [],
        Report,
        PreReport,
      };

      // 审核中定时请求接口
      if (currentStep === 3 && mappingCompany) {
        if (DataCheckID && DataCheckState === 0) {
          getSurveyResultCheckState(DataCheckID);
        }
      }
    },
    // 处理要提交的业务数据
    handleBusinessData() {
      const { businessID, PreSurveyContentInfo, form1 } = this;
      const {
        BusinessName,
        GroundCode,
        PutLineProjectPlanPermission,
        PreProjectPlanPermission,
      } = form1;
      const { BuildingTableInfo, ProjectResultInfo } = PreSurveyContentInfo;

      this.BaseInfo.BusinessName = BusinessName;

      const data = {
        BaseInfo: this.BaseInfo,
        PreSurveyContentInfo: {
          ID: businessID,
          GroundCode,
          ProjectPlanPermission: JSON.stringify(PreProjectPlanPermission),
          BuildingTableInfo: JSON.stringify(BuildingTableInfo),
          ProjectResultInfo: JSON.stringify(ProjectResultInfo),
        },
        PutLineContentInfo: {
          ID: businessID,
          GroundCode,
          ProjectPlanPermission: JSON.stringify(PutLineProjectPlanPermission),
        },
      };

      return data;
    },
    // 显示成果数据
    showResult() {
      const { currentStep, mappingCompany, PreSurveyContentInfo } = this;

      if (
        currentStep === 3 &&
        mappingCompany &&
        PreSurveyContentInfo.DataCheckID &&
        PreSurveyContentInfo.DataCheckState === 1
      ) {
        return true;
      }
      if (currentStep === 4 || currentStep === 5) {
        return true;
      }

      return false;
    },
    // 禁用退回、提交验收和确认通过（测试按钮）
    disabledBack() {
      const { DataCheckID, DataCheckState } = this.PreSurveyContentInfo;

      // 正在在检查成果
      if (DataCheckID && DataCheckState === 0) {
        return true;
      }
      return false;
    },
    //单独禁用验收按钮
     disabledAccept() {
      const { DataCheckID, DataCheckState } = this.PreSurveyContentInfo;
      // 正在在检查成果
      if (DataCheckID && DataCheckState === 0) {
        return true;
      }
      if (DataCheckID && DataCheckState === 2) {
        return true;
      }
      return false;
    },
    // 查看工程规划许可证
    viewLicence(row, index, name) {
      this.licenceViewDialog = {
        visible: true,
        row,
        index,
        title: `查看工程规划许可证（${name}）`,
        licenceType: name,
      };
    },
    // 工程规划许可证获取成功
    licenceGetSuccess(params) {
      if (this.licenceGetDialog.licenceType === "放线") {
        this.form1.PutLineProjectPlanPermission.push(params);
        this.$refs.form1.clearValidate("PutLineProjectPlanPermission");
      } else {
        this.form1.PreProjectPlanPermission.push(params);
        this.$refs.form1.clearValidate("PreProjectPlanPermission");
      }
    },
    // 工程规划许可证获取弹窗可见性
    setLicenceGetDialogVisible(val, name) {
      this.licenceGetDialog.visible = true;
      this.licenceGetDialog.title = `工程规划许可证（${name}）`;
      this.licenceGetDialog.licenceType = name;
    },
    // 删除工程规划许可证
    delLicence(row, index, name) {
      if (name === "放线") {
        this.form1.PutLineProjectPlanPermission.splice(index, 1);
      } else {
        this.form1.PreProjectPlanPermission.splice(index, 1);
      }
    },
    // 显示工程规划许可证存储弹窗
    storeLicence(row, index, name) {
      this.licenceStoreDialog = {
        visible: true,
        row,
        index,
        title: `工程规划许可证（${name}）`,
        licenceType: name,
      };
    },
    // 需要手动添加工规证号
    needAddLicence(licenceType) {
      this.licenceStoreDialog = {
        visible: true,
        row: null,
        index: -1,
        title: `工程规划许可证（${licenceType}）`,
        licenceType,
      };
    },
    // 工程规划许可证存储成功
    licenceStoreSuccess(data, index) {
      if (index >= 0) {
        if (this.licenceStoreDialog.licenceType === "放线") {
          this.$set(this.form1.PutLineProjectPlanPermission, index, data);
        } else {
          this.$set(this.form1.PreProjectPlanPermission, index, data);
        }
      } else {
        if (this.licenceStoreDialog.licenceType === "放线") {
          this.form1.PutLineProjectPlanPermission.push(data);
        } else {
          this.form1.PreProjectPlanPermission.push(data);
        }
      }
    },
    // 工程规划许可证存储弹窗可见性
    setLicenceStoreDialogVisible(val, name) {
      this.licenceStoreDialog.visible = val;
      this.licenceStoreDialog.title = `工程规划许可证（${name}）`;
      this.licenceStoreDialog.licenceType = name;
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
