<template>
  <!-- eslint-disable -->
  <div class="view-info-container">
    <div class="business-info-title">
      <span>申请信息</span>
    </div>
    <el-card shadow="never">
      <el-form ref="form" :model="form" label-width="200px">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务编号：">{{
                  baseInfo.BusinessNumber | isNull
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务类型：">{{
                  baseInfo.BusinessType | isNull
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务名称：">{{
                  baseInfo.BusinessName | isNull
                }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人：">{{
                  baseInfo.CreatePersonName | isNull
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机号：">{{
                  baseInfo.CreatePersonPhone | isNull
                }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位：">{{
                  baseInfo.DeveloperName | isNull
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测绘单位：">{{
                  baseInfo.SurveyCompanyName | isNull
                }}</el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="业务信息" name="2">
            <el-form-item label="工程规划许可证：">
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="preProjectPlanPermission"
                :default-props="tableProps"
                :showPagination="false"
              >
                <el-table-column
                  prop="action"
                  label="操作"
                  width="170"
                  fixed="right"
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-button
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index, '预测绘')"
                      >查看详情</el-button
                    >
                  </template>
                </el-table-column>
              </dynamic-table>
            </el-form-item>
            <el-form-item label="不动产单元号（宗地号）：">{{
              contentInfo.GroundCode | isNull
            }}</el-form-item>
          </el-collapse-item>
          <el-collapse-item title="附件信息" name="3" v-if="showAttachmentInfo">
            <attachment-info
              :base-info="baseInfo"
              :step="step"
              :step-list="stepList"
              :attachment-data="attachmentData"
              :actions-infos="actionsInfos"
              :needManualAudit="checkProjectPlanPermissionNeedManualAudit"
              :audit-status="auditStatus"
              @upload-success="upload($event, 'form', 'Others')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
              ref="childFiles"
            />
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import AttachmentInfo from "./attachmentInfo.vue";
// mixins
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "PreMappingMergeViewInfo",
  mixins: [ViewInfo],
  props: {    
    // 工程规划许可证（预测绘）
    putLineProjectPlanPermission: {
      type: Array,
      default: () => [],
    },
    // 工程规划许可证（放线）
    preProjectPlanPermission: {
      type: Array,
      default: () => [],
    },
    //MDB人工审核状态
    auditStatus: {
      type: String,
      default: null,
    },
  },
  components: { AttachmentInfo },
  computed: {
    showAttachmentInfo() {
      const { step, mappingCompany } = this;
      if (step === 3 && mappingCompany) {
        return false;
      }

      return true;
    }
  },
  data() {
    return {
      licenceTableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50",
        },
        {
          title: "工程规划许可证号",
          key: "Code",
          align: "center",
        },
        {
          title: "建设单位",
          key: "ConstructCompany",
          align: "center",
        },
        {
          title: "项目名称",
          key: "ProjectName",
          align: "center",
        },
      ],
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title",
      }
    };
  },
  methods: {
    // 查看工程规划许可证
    viewLicence(row, index, name) {
      this.$emit("view-licence", row, index, name);
    },

    //检查工规证是否有2023-08-30至2024-12-31发证并且未缴纳城建费
    checkProjectPlanPermissionNeedManualAudit() {
      const { preProjectPlanPermission } = this;
      //是否需要人工审核
      var needManualAudit = false;
      if (preProjectPlanPermission != null && preProjectPlanPermission != []){
        preProjectPlanPermission.forEach((e) => {          
          if (e.NeedManualAudit == true) {
            needManualAudit = true;
            return;
          }
        });
      }
      return needManualAudit;
    },
    getFiles() {
      let files = this.$refs.childFiles.getFiles();
      return files;
    }
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
