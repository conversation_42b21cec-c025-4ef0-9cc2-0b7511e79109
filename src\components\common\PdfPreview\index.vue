<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title ? title: '查看PDF'"
    :visible="visible"
    :close-on-click-modal="false"
    :append-to-body="true"
    class="pdf-preview-container"
    width="1100px"
    @close="close"
  >
    <iframe v-if="url" :src="url" frameborder="0" :style="{width: '100%', height }">
      您的浏览器暂不支持在此系统中预览pdf文件，可<a :href="url" target="_blank" class="link">点击此处</a>直接打开
    </iframe>
    <empty v-else />
  </el-dialog>
</template>
<script>
/* eslint-disable */
// mixins
import Resize from "mixins/resize.js";

export default {
  name: "PdfPreview",
  mixins: [Resize],
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: true
    },
    // pdf地址
    pdfUrl: {
      type: String,
      default: null
    },
    // 标题
    title: {
      type: String,
      default: "查看PDF"
    }
  },
  watch: {
    pdfUrl(val) {
      this.url = val;
    }
  },
  data() {
    return {
      height: "100%",
      url: null
    };
  },
  methods: {
    $_resizeHandler() {
      const docHeight = document.body.clientHeight;
      this.height = `${docHeight - 150}px`;
    },
    close() {
      this.$emit("close", false);
      this.$emit("update:visible", false);
    }
  }
};
</script>
<style lang="scss" scoped>
.pdf-preview-container {
  /deep/ .el-dialog__body {
    padding: 10px 20px 15px 20px;
  }
}

.link {
  color: $color-primary;
  cursor: pointer;
  &:hover {
    color: #09f;
  }
}
</style>

