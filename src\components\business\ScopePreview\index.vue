<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="1000px"
    @close="close"
    class="scope-dialog-container"
  >
    <div :id="mapId" :style="mapStyle">
      <empty v-if="!coordinates.length" text="无法生成预览图，请稍后重试" />
    </div>
    <div v-if="showFooter" slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">{{ confirmText }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
import proj4 from "proj4";
import "ol/ol.css";
import { Projection, transform, fromLonLat } from "ol/proj";
import { register } from "ol/proj/proj4";
import { Map, View, Feature } from "ol/index";
import { Style, Stroke, Fill } from "ol/style";
import { Vector as VectorSource } from "ol/source";
import { Vector as VectorLayer } from "ol/layer";
import { LineString } from "ol/geom";

// 参考 https://www.jianshu.com/p/9eca1bc8a12d 和 http://epsg.io/4524
// 定义EPSG:4524坐标系转到EPSG:4326坐标系下
proj4.defs(
  "EPSG:4524",
  "+proj=tmerc +lat_0=0 +lon_0=108 +k=1 +x_0=36500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"
);
register(proj4);

const projection_4524 = new Projection({
  code: "EPSG:4524",
  extent: [36341298.83, 2012660.02, 36623358.71, 4704933.89]
});

const projection_4326 = new Projection({
  code: "EPSG:4326"
});

export default {
  name: "ScopePreviewDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    coordinates: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: "预览范围"
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    confirmText: {
      type: String,
      default: "确认"
    },
    mapId: {
      type: String,
      default: "scopeMap"
    },
    mapStyle: {
      type: Object,
      default: () => ({
        height: "500px",
        textAlign: "center"
      })
    }
  },
  data() {
    return {
      layer: null,
      map: null,
      // 转换坐标点
      transPoints: [],
      // 中心点
      centerPoint: [0, 0],
      layerStyle: new Style({
        fill: new Fill({
          color: "#FFFFFF"
        }),
        stroke: new Stroke({
          color: "#F07057",
          width: 1
        })
      }),
      // 视图范围
      extent: [0, 0, 0, 0]
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      this.$nextTick(() => {
        this.drawScope(this.coordinates);
      });
    },
    // 关闭存储弹窗
    close() {
      this.clearMap();
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    confirm() {
      this.$emit("confirm");
    },
    // 转换坐标点
    transPoint(coordinate) {
      return transform(coordinate, projection_4524, projection_4326);
    },
    // 绘制范围
    drawScope(coordinates) {
      this.transPoints = [];

      let minX = 180;
      let minY = 90;
      let maxX = 0;
      let maxY = 0;

      for (let i = 0; i < coordinates.length; i++) {
        let oldCoor = [coordinates[i][0], coordinates[i][1]];
        // console.log(i + 1 + " old:" + oldCoor);

        // 转换坐标点
        let newCoor = this.transPoint(oldCoor);
        // console.log(i + 1 + " new:" + newCoor);
        let pointTransform = fromLonLat(newCoor, "EPSG:4326");
        this.transPoints.push(pointTransform);

        //  取图形边界线
        if (newCoor[0] > maxX) {
          maxX = newCoor[0];
        }
        if (newCoor[1] > maxY) {
          maxY = newCoor[1];
        }

        if (newCoor[0] < minX) {
          minX = newCoor[0];
        }
        if (newCoor[1] < minY) {
          minY = newCoor[1];
        }
      }

      this.extent = [minX, minY, maxX, maxY];
      this.centerPoint = [(maxX - minX) / 2 + minX, (maxY - minY) / 2 + minY];
      this.initMap();
      this.drawLine(this.transPoints);
    },
    // 绘制线条
    drawLine(points) {
      const source = new VectorSource();

      const lineFeature = new Feature({
        geometry: new LineString(points, "XY")
      });
      source.addFeature(lineFeature);

      // 矢量图层
      this.layer = new VectorLayer({
        source: source,
        style: this.layerStyle
      });

      this.map.addLayer(this.layer);

    },
    // 初始化地图
    initMap() {
      let view = new View({
        center: this.centerPoint,
        zoom: 10,
        projection: "EPSG:4326"
      });

      this.map = new Map({
        view,
        target: this.mapId
      });

      this.map.getView().fit(this.extent, this.map.getSize(), {
        constrainResolution: false,
        earest: false,
        padding: [50, 50, 50, 50]
      });
    },
    // 清楚地图
    clearMap() {
      const { map, layer, mapId } = this;
      let scopeContainer = document.getElementById(mapId);
      // console.log(scopeContainer);
      if (scopeContainer) {
        scopeContainer.innerHTML = "";
      }

      if (map) {
        this.map.removeLayer(layer);
      }
      this.map = null;
      this.layer = null;
      this.transPoints = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.scope-dialog-container {
  /deep/ .el-dialog__body {
    padding: 10px 20px 20px 20px;
  }
}
</style>