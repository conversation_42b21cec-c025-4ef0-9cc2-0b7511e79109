<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      :width="disableEdit ? '800px' : '900px'"
      @close="close"
      class="member-store-container"
    >
      <div>
        <!-- 预览 -->
        <el-form v-if="disableEdit" :model="form" :label-width="labelWidth">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="姓名：">{{ form.PersonName | isNull }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色：">
                <el-tag type="warning">{{ form.PersonRole }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="注册测绘师编号：">
                <div class="certificate-no flex">
                  <span>{{ form.RegisteredSurveyorNo | isNull }}</span>
                  <i class="iconfont icon-question ml-10" @click="certExampleDialog.visible = true"></i>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册测绘师证书有效期至：">{{ form.ValidityTime | isNull }}</el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="注册测绘师执业章：">
                <img-list
                  v-if="form.SurveyorCertificateChapter && form.SurveyorCertificateChapter.length"
                  :img-list="form.SurveyorCertificateChapter"
                  @preview="preview"
                />
                <span v-else>未上传文件</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册测绘师证书：">
                <img-list
                  v-if="form.SurveyorCertificateImg && form.SurveyorCertificateImg.length"
                  :img-list="form.SurveyorCertificateImg"
                  @preview="preview"
                />
                <span v-else>未上传文件</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="身份证号码：">{{ form.PersonNumber | isNull }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码：">{{ form.PersonPhone | isNull }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 存储 -->
        <el-form v-else ref="form" :model="form" :rules="rules" :label-width="labelWidth">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="姓名：" prop="PersonName">
                <el-input
                  v-model="form.PersonName"
                  placeholder="请输入姓名"
                  :disabled="form.PersonRole === '注册测绘师' && row ? true :false"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色：" prop="PersonRole">
                <el-tag type="warning">{{ form.PersonRole }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="注册测绘师证书编号：" prop="RegisteredSurveyorNo">
                <div class="certificate-no flex">
                  <el-input v-model="form.RegisteredSurveyorNo" placeholder="请输入证书编号"></el-input>
                  <i class="iconfont icon-question ml-10" @click="certExampleDialog.visible = true"></i>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册测绘师证书有效期至：" prop="ValidityTime" label-width="200px">
                <el-date-picker
                  type="date"
                  placeholder="请选择证书有效期"
                  v-model="form.ValidityTime"
                  value-format="yyyy-MM-dd"
                  class="width-100"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册测绘师执业章：" prop="SurveyorCertificateChapter">
                <img-upload
                  :img-list="form.SurveyorCertificateChapter"
                  :show-size-hint="false"
                  :file-size="10240"
                  :limit="1"
                  :data="{ BusinessType: 'CompanyRegisterRequest', BusinessID: companyID, AttachmentType: '申请材料附件', AttachmentCategories: '注册测绘师执业章' }"
                  @upload-success="upload($event, 'form', 'SurveyorCertificateChapter')"
                  @preview="preview"
                  @delete="del($event, 'form', 'SurveyorCertificateChapter')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册测绘师证书：" prop="SurveyorCertificateImg">
                <img-upload
                  :img-list="form.SurveyorCertificateImg"
                  :show-size-hint="false"
                  :file-size="10240"
                  :limit="1"
                  :data="{ BusinessType: 'CompanyRegisterRequest', BusinessID: companyID, AttachmentType: '申请材料附件', AttachmentCategories: '人员注册测绘师证书' }"
                  @upload-success="upload($event, 'form', 'SurveyorCertificateImg')"
                  @preview="preview"
                  @delete="del($event, 'form', 'SurveyorCertificateImg')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="身份证号码：" prop="PersonNumber">
                <el-input
                  v-model="form.PersonNumber"
                  placeholder="请输入身份证号码"
                  :disabled="form.PersonRole === '注册测绘师' && row ? true :false"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码：" prop="PersonPhone">
                <el-input v-model="form.PersonPhone" placeholder="请输入手机号码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div v-if="!disableEdit" slot="footer">
        <el-button @click="close">取消</el-button>
        <template>
          <el-button type="primary" @click="submitForm('form')">确认{{ row ? '编辑' : '添加' }}</el-button>
        </template>
      </div>
    </el-dialog>
    <!-- 图片预览 -->
    <img-preview
      :visible.sync="imgDialog.visible"
      :title="imgDialog.title"
      :img-url="imgDialog.imgUrl"
      @close="cancelPreview"
    />
    <el-dialog
      title="注册测绘师编号样例"
      :visible.sync="certExampleDialog.visible"
      @close="certExampleDialog.visible = false"
    >
      <img style="width:100%" :src="certExampleDialog.imgUrl" />
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// mixins
import CompanyManageStore from "mixins/company-info/store.js";
import MemberMixin from "mixins/company-info/member.js";

export default {
  name: "MemberStore",
  mixins: [CompanyManageStore, MemberMixin],
  data() {
    return {
      labelWidth: "180px"
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init("注册测绘师");
      }
    }
  },
  methods: {
    // 设置表单
    setForm() {
      if (this.row) {
        let data = JSON.parse(JSON.stringify(this.row));
        // 添加附件字段（解决后端返回的附件字段缺少问题）
        data = { ...data, ...this.attachmentInfo };

        // 应后端接口需求特殊处理AttachmentInfo字段
        if (this.row.AttachmentInfo) {
          delete data.AttachmentInfo;

          const field = JSON.parse(this.row.AttachmentInfo);
          this.form = { ...data, ...field };
        } else {
          this.form = data;
        }
      } else {
        this.form = { ...this.defaultForm, ...this.attachmentInfo };
      }
      
      this.form.PersonRole = "注册测绘师";
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const params = { ...this.form };

          const isExist = this.list.find(
            e => e.PersonNumber === params.PersonNumber
          );
          if (isExist && !this.row) {
            this.$message.warning("该注册测绘师已存在，不可添加");
            return false;
          }

          // 成功回传父组件
          this.$emit("submit", this.row ? 1 : 0, params, this.index);
          this.submit = true;
          this.$emit("update:visible", false);
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.certificate-no {
  align-items: center;

  .icon-question {
    cursor: pointer;
    &:hover {
      color: $color-primary;
    }
  }
}
</style>