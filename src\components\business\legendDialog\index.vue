<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :licenceType="licenceType"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="900px"
    @close="close"
    class="legend-dialog-container"
  >
    <img class="preview-img" :src="url" />
  </el-dialog>
</template>

<script>
/* eslint-disable */
export default {
  name: "LegendDialog",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 名称
    title: {
      type: String,
      default: "",
    },
    //图例证件类型
    licenceType:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      // 表单
      url: "",
    };
  },
  watch: {
    visible(val) {
      if(val){
        this.init()
      }
    },
  },
  methods: {
    init() {
      if(this.licenceType==="不动产权证"){
        this.url = require("@/assets/certificate-images/realEstate.png");
      }else if (this.licenceType==="工规证"){
        this.url = require("@/assets/certificate-images/licence.png");
      }
    },
    // 关闭存储弹窗
    close() {
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.legend-dialog-container {
  /deep/ .el-dialog__body {
    padding: 10px 20px 20px 20px;
  }
  .preview-img {
    display: block;
    width: 100%;
    margin: 0 auto;
  }
}
</style>