
/* eslint-disable */
import request from 'utils/request';

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
	// 提交业务信息（下一步）
	SubmitBusiness: (id, actionId) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/SubmitBusiness?id=${id}&actionId=${actionId}`,
			method: 'post'
		})
	},
	// 审核通过（启动EPS进行数据切图）
	PostEpsDownLoadResult:(id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostEpsDownLoadResult?id=${id}`,
			method: 'post'
		})
	},
	// 获取EPS下载状态
	GetEPSDownLoadState:(id, businessClass) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetEPSDownLoadState?downloadid=${id}&businessClass=${businessClass}`,
			method: 'get'
		})
	}
}