<template>
  <!-- eslint-disable -->
  <div v-if="!loading" class="base-info-container">
    <!-- 预览 -->
    <el-form v-if="disableEdit" :model="form" :label-width="labelWidth">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="统一社会信用代码：">{{
            form.CreditCode | isNull
          }}</el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="单位名称：">{{
            form.CompanyName | isNull
          }}</el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="单位地址：">{{
            form.CompanyAddress | isNull
          }}</el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="单位类型：">
            <el-tag v-if="form.CompanyType" :class="form.CompanyType == '测绘单位' ? '' : 'tag-blue'">{{
              form.CompanyType
            }}</el-tag>
            <span v-else>-</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="useType === 'register' || companyType === '测绘单位'">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="法定代表人姓名：">{{
            form.LegalPersonName | isNull
          }}</el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="法定代表人身份证号：">{{
            form.LegalPersonNumber | isNull
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="联系人：">{{
            form.Contacter | isNull
          }}</el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="联系电话：">{{
            form.ContacterPhone | isNull
          }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 存储 -->
    <el-form
      v-else
      ref="baseInfoForm"
      :model="form"
      :rules="rules"
      :label-width="labelWidth"
    >
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="统一社会信用代码：" prop="CreditCode">
            <el-input
              v-model.trim="form.CreditCode"
              placeholder="请输入统一社会信用代码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="单位名称：" prop="CompanyName">
            <el-input
              v-model.trim="form.CompanyName"
              placeholder="请输入单位名称"
              @blur="companyNameChange"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="单位地址：" prop="CompanyAddress">
            <el-input
              v-model.trim="form.CompanyAddress"
              placeholder="请输入地址"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="单位类型：" prop="CompanyType">
            <el-tag v-if="form.CompanyType" :class="form.CompanyType == '测绘单位' ? '' : 'tag-blue'">{{
              form.CompanyType
            }}</el-tag>
            <span v-else>-</span>
            <!-- <el-select
              v-model.trim="form.CompanyType"
              placeholder="请选择单位类型"
              disabled
              style="width: 100%"
            >
              <el-option
                v-for="item in CompanyTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              ></el-option>
            </el-select> -->
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="法定代表人姓名：" prop="LegalPersonName">
            <el-input
              v-model.trim="form.LegalPersonName"
              placeholder="请输入法定代表人姓名"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="法定代表人身份证号：" prop="LegalPersonNumber">
            <el-input
              v-model.trim="form.LegalPersonNumber"
              placeholder="请输入法定代表人身份证号"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="联系人：" prop="Contacter">
            <el-input
              v-model.trim="form.Contacter"
              placeholder="请输入联系人姓名"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="联系电话：" prop="ContacterPhone">
            <el-input
              v-model.trim="form.ContacterPhone"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
/* eslint-disable */
// 校验
import {
  validRealName,
  validBusinessLicence,
  validIDcard,
  validPhone,
} from "utils/validate";
// 组件
import ImgUpload from "components/common/AttachmentUpload/ImgUpload";
// mixins
import Attachment from "mixins/company-info/attachment.js";
// vuex
import { mapGetters } from "vuex";

const urlPrefix = process.env.VUE_APP_URL_PREFIX;

export default {
  name: "CompanyBaseInfo",
  components: {
    ImgUpload,
  },
  mixins: [Attachment],
  computed: {
    ...mapGetters(["companyType"]),
  },
  props: {
    // 当前激活类型
    activeTabName: {
      type: String,
      default: "base",
    },
    // 数据
    data: {
      type: Object,
      default: () => ({}),
    },
    // 加载
    loading: {
      type: Boolean,
      default: true,
    },
    // 禁止编辑
    disableEdit: {
      type: Boolean,
      default: false,
    },
    // 组件使用类型，register 单位注册 info 单位信息
    useType: {
      type: String,
      default: "info",
    },
    // 仅单位信息变更使用，new 新单位信息 old 原单位信息
    infoType: {
      type: String,
      default: "new",
    },
  },
  data() {
    const validateRealName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入真实姓名"));
      } else if (!validRealName(value)) {
        callback(new Error("真实姓名格式错误"));
      } else {
        callback();
      }
    };

    const validateBusinessLicence = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入统一社会信用代码"));
      } else if (!validBusinessLicence(value)) {
        callback(new Error("统一社会信用代码格式错误"));
      } else {
        callback();
      }
    };

    const validateIDcard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号"));
      } else if (!validIDcard(value)) {
        callback(new Error("身份证号格式错误"));
      } else {
        callback();
      }
    };

    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入联系电话"));
      } else if (!validPhone(value)) {
        callback(new Error("联系电话格式有误"));
      } else {
        callback();
      }
    };

    return {
      defaultForm: {
        CompanyName: null,
        CompanyType: null,
        CreditCode: null,
        legalpersonName: null,
        LegalPersonNumber: null,
        Contacter: null,
        ContacterPhone: null,
        CompanyAddress: null,
      },
      form: {},
      // 单位类型选项
      CompanyTypeOptions: [
        {
          // value: 1,
          label: "建设单位",
        },
        {
          // value: 2,
          label: "测绘单位",
        },
      ],
      labelWidth: "165px",
      // 基本信息规则
      rules: {
        CompanyName: [
          { required: true, message: "请输入单位名称", trigger: "blur" },
        ],
        CompanyType: [
          { required: true, message: "请选择单位类型", trigger: "change" },
        ],
        CreditCode: [
          {
            required: true,
            validator: validateBusinessLicence,
            trigger: "blur",
          },
        ],
        LegalPersonName: [
          { required: true, validator: validateRealName, trigger: "blur" },
        ],
        LegalPersonNumber: [
          {
            required: true,
            validator: validateIDcard,
            trigger: "blur",
          },
        ],
        Contacter: [
          { required: true, validator: validateRealName, trigger: "blur" },
        ],
        ContacterPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        CompanyAddress: [
          { required: true, message: "请输入单位地址", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    loading(val) {
      if (!val) {
        this.init();
      }
    },
    infoType(val) {
      // 修复因有时差导致信息未变更的BUG
      setTimeout(() => this.init(), 0);
    },
  },
  methods: {
    // 初始化
    init() {
      // 解决后端接口出错时表单校验问题
      if (this.$refs.baseInfoForm) {
        this.$refs.baseInfoForm.clearValidate();
      }

      if (!this.disableEdit) {
        this.$nextTick(() => {
          this.$refs.baseInfoForm.clearValidate();
        });
      }

      this.form =
        this.data && Object.keys(this.data).length
          ? { ...this.data }
          : { ...this.defaultForm };
    },
    // 单位名称修改
    companyNameChange(e) {
      const value = e.target.value;
      this.$emit("change", "base", value, "单位名称变更");
    },
  },
};
</script>
<style lang="scss" scoped></style>
