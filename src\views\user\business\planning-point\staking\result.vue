<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="surveyResult">
      <div class="result-title mt-0">
        <span>【{{ name }}】测绘成果</span>
      </div>
      <div class="result-file flex">
        <div>{{ surveyResult.AttachmentName + surveyResult.AttachmentExt }}</div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(surveyResult)"
        >下载</el-button>
      </div>
    </template>
    <template v-if="resultImg.length && showReport">
      <div class="result-title mt-20">
        <span>【{{ name }}】测绘成果图</span>
      </div>
      <file-list
        :file-list="resultImg"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
  </div>
</template>

<script>
/* eslint-disable */
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";
import BusinessResult from "mixins/business/result.js";

export default {
  name: "BlueLineResult",
  mixins: [AttachmentDownload, BusinessResult],
  props: {
    resultImg: {
      type: Array,
      default: () => []
    },
    showReport: {
      type: Boolean,
      default: false
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
</style>

