/* eslint-disable */
import request from "utils/request";
import qs from "qs";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  // 获取关于我们信息
  GetIntroduction: (data) => {
    return request({
      url: `${VUE_APP_SERVER_API}/api/bdcappdata/GetIntroduction`,
      method: "get",
    });
  },
  // 通过手机号获取验证码
  GetVertificateCodeByMobile: (data) => {
    return request({
      url: `${VUE_APP_SERVER_API}/api/Account/ForgotPassword`,
      method: "post",
      // json转formData格式
      data: qs.stringify(data),
      header: {
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    });
  },
  // 重置密码
  ResetPwd: (data) => {
    return request({
      url: `${VUE_APP_SERVER_API}/api/Account/ResetPassword`,
      method: "post",
      data: qs.stringify(data),
      header: {
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    });
  },
  //获取密码规则
  GetUsedPasswordRegex:(data)=>{
    return request({
      url: `${VUE_APP_SERVER_API}/api/account/GetUsedPasswordRegex`,
      method: "get",
      // data: qs.stringify(data),
      header: {
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    });
  },
  // 生成附件记录
  CreateAttachment: (data) => {
    return request({
      url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/CreateAttachmentRequest`,
      method: "post",
      data: qs.stringify(data),
      header: {
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    });
  },
  // 附件上传
  // UploadAttachment: (data) => {
  //   return request({
  //     url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/AttachmentUploadRequest`,
  //     method: "post",
  //     data: qs.stringify(data),
  //     header: {
  //       "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
  //     },
  //   });
  // },
  // 获取附件列表
  GetAttachmentList: (id) => {
    return request({
      url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/GetAttachmentListRequest?id=${id}`,
      method: "post",
    });
  },
  // 附件下载
  DownloadAttachment: (id) => {
    return request({
      url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/AttachmentDownloadRequest?id=${id}`,
      method: "get",
      responseType: "blob",
      headers: {
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    });
  },
  // 获取附件下载key
  GetAttachmentDownloadKey: (id) => {
    return request({
      url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/GetAttachmentDownloadKey?id=${id}`,
      method: "get",
    });
  },
  // 通过key值获取文件路径进行下载
  DownloadAttachmentByKey: (key, filename) => {
    return `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/DownloadAttachmentWithKey/${key}/${encodeURIComponent(filename)}`;
  },
  // 附件删除
  DeleteAttachment: (id) => {
    return request({
      url: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/AttachmentDeleteRequest?id=${id}`,
      method: "post",
    });
  },
  // 获取数据字典
  GetDictionaryList: (name, type) => {
    return request({
      url: encodeURI(
        `${VUE_APP_SERVER_API}/sdcapi/DataDirectory/GetDirectoryListOrTree?name=${name}&type=${type}`
      ),
      method: "get",
    });
  },
  // 系统附件下载
  DownloadSystemAttachment: (fileName) => {
    return request({
      url: encodeURI(
        `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/SystemAttachmentDownload?fileName=${fileName}`
      ),
      method: "get",
      responseType: "blob",
      headers: {
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    });
  },
  // 预览图片
  ShowImg: (id) => {
    return `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/ShowImage?id=${id}`;
  },
  // 预览pdf
  ShowPDF: (id) => {
    return `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/ShowPDF?id=${id}`;
  },
};
