<template>
  <div class="mt-20">
    <tip class="agreement-tips" type="default">
      <p class="mt-0">
        请打印以下协议，盖章后送往<span class="company">不动产测绘数据管理单位</span>进行备案后即可完成注册。若已提交备案，请耐心等待。（备案地址：南宁市良庆区玉洞大道33号南宁市民中心C座601南宁市不动产登记中心档案部，联系电话：5609520）
      </p>
      <a
        class="agreement"
        @click="setDialogVisible(true, '《南宁市不动产登记综合服务平台使用协议》')"
      >《南宁市不动产登记综合服务平台使用协议》</a>
    </tip>
    <el-dialog
      class="agreement-dialog"
      :visible="dialog.visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="900px"
      @close="setDialogVisible(false)"
    >
      <template slot="title">
        <el-button
          class="print-btn"
          type="primary"
          icon="el-icon-printer"
          :loading="printLoading"
          @click="print"
        >打印</el-button>
        <el-button
          class="print-btn"
          type="warning"
          icon="el-icon-download"
          :loading="downLoading"
          @click="downloadSystemAttachment('南宁市不动产登记综合服务平台使用协议.docx')"
        >下载协议</el-button>
      </template>
      <agreement />
      <iframe id="printf" src width="0" height="0" frameborder="0" />
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";
// 组件
import Agreement from "views/agreement/company-register/index.vue";
// Api
import PublicApi from "api/public/index.js";
// 工具
import { downloadFileByStream } from "utils/index.js";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

const printStyles = `
body{
  font-family: "宋体";
}

h1{
  font-size: 30px;
}

p{
  line-height: 1.5;
}

.text-center {
  text-align: center;
}

.text-indent {
  text-indent: 2em;
}

.chapter {
  display: flex;
  justify-content: space-between;
}

.space {
  display: inline-block;
  width: 50px;
}
`;

export default {
  name: "CompanyRegisterAgreement",
  components: {
    Agreement
  },
  // props: {
  //   companyStateCode: {
  //     type: Number,
  //     default: 0
  //   }
  // },
  computed: {
    ...mapGetters(["personName"])
  },
  data() {
    return {
      printLoading: false,
      downLoading: false,
      dialog: {
        title: "",
        visible: false
      },
      systemAttachmentUrl: `${VUE_APP_SERVER_API}/sdcapi/AttachmentManage/SystemAttachmentDownload`,
    };
  },
  methods: {
    setDialogVisible(val, title = "") {
      this.dialog = {
        visible: val,
        title
      };
    },
    print() {
      this.printLoading = true;

      let $printDiv = document.getElementById("printContent");
      const printContent = $printDiv.innerHTML;

      let style1 = document.createElement("style");
      style1.setAttribute("type", "text/css");
      style1.innerHTML = printStyles;

      // 页眉页脚设置
      let style2 = document.createElement("style");
      style2.setAttribute("media", "print");
      style2.innerHTML = `
        @page {
            size: auto;
            margin: 20mm;
        }
      `;

      let $iframe = document.getElementById("printf");
      let doc = document.all
        ? $iframe.contentWindow.document
        : $iframe.contentDocument;
      doc.write(printContent);
      if ($iframe.contentWindow.document) {
        $iframe.contentWindow.document
          .getElementsByTagName("head")[0]
          .appendChild(style1)
          .appendChild(style2);

        const { $route, dialog } = this;
        const defaultTitle = $route.meta.title;

        // 设置打印名称
        document.title = dialog.title;

        $iframe.contentWindow.print();
        doc.close();
        $iframe.innerHTML = "";
        document.title = defaultTitle;
      } else {
        this.$message.warning("您的浏览器不支持打印功能，推荐使用谷歌浏览器");
      }

      this.printLoading = false;
    },
    downloadSystemAttachment(fileName) {
      this.downLoading = true;
      PublicApi.DownloadSystemAttachment(fileName).then(res => {
        downloadFileByStream(res, fileName);
        this.downLoading = false;
      }).catch(err => {
        this.downLoading = false;
        this.$message.error("服务器繁忙，请稍后重试");
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.agreement-container {
  margin: 0;
}
.agreement {
  color: #f07057;
  text-decoration: underline;
  &:hover {
    color: #09f;
  }
}
.company {
  color: #f07057;
}

.agreement-dialog {
  /deep/.el-dialog__body {
    padding: 0 20px 20px 20px;
  }
}

.agreement-tips {
  font-size: 16px;
}
</style>