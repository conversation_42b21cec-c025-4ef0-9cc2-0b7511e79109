<template>
  <!-- eslint-disable -->
  <el-form ref="form" :model="form" label-width="140px">
    <el-form-item label-width="160px" label="工程规划许可证件：">
      <file-list
        :file-list="attachmentData.ProjectLicenceImg"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="建筑定位图：">
      <file-list
        :file-list="attachmentData.BuildingLocationMap"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="总平面图：">
      <file-list
        :file-list="attachmentData.SitePlan"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="经备案的建筑设计图：">
      <file-list
        :file-list="attachmentData.BuildingDesgin"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="门牌证明：">
      <file-list
        :file-list="attachmentData.DoorPlate"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="其他：">
      <list-upload
        v-if="isFinished && isCurrentApplicant"
        file-format="png / jpg / gif / bmp / pdf / zip / rar"
        :file-list="form.Others"
        :file-size="102400"
        :data="{ BusinessType: baseInfo.BusinessType, BusinessID: baseInfo.ID, AttachmentType: '申请材料附件', AttachmentCategories: '其他附件' }"
        :on-check-format="checkOthers"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'Others')"
        @delete="del($event, 'form', 'Others')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.Others"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
  </el-form>
</template>

<script>
/* eslint-disable */
// 组件
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "PreMappingAttachmentInfo",
  mixins: [ViewInfo],
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e;
        if (e.length) {
          this.$refs[formName].clearValidate(attr);
        }
      }

      this.$emit("upload-success", e);
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
