<template>
  <!-- eslint-disable -->
  <div ref="operateBtn" class="operate-btn-container" :class="{ 'fixed': fixed}">
    <el-button class="back-btn" type="success" @click="backToList">
      <i class="el-icon-arrow-left"></i>返回列表
    </el-button>
    <el-button type="warning" icon="el-icon-refresh" @click="reload">刷新</el-button>

    <slot name="before" />

    <el-button
      v-if="prevVisible && currentStep > 1"
      type="primary"
      :loading="prevLoading"
      @click="prev()"
    >
      <i class="el-icon-caret-left"></i>上一步
    </el-button>
    <el-button
      v-if="saveVisible"
      class="save-btn"
      type="success"
      :loading="saveLoading"
      @click="save()"
    >
      <i class="iconfont icon-baocun mr-5 mt-5"></i>保存
    </el-button>
    <!-- 提交审核保留，暂时不用了 -->
    <!-- <el-button v-if="submitVisible" type="success" :loading="submitLoading" @click="submit()">
      <i class="el-icon-check mr-5"></i>提交审核
    </el-button>-->
    <el-button
      v-if="nextVisible && currentStep < stepNum"
      type="success"
      :loading="nextLoading"
      @click="next()"
    >
      下一步
      <i class="el-icon-caret-right"></i>
    </el-button>
    <!-- 仅第一步可关闭 -->
    <el-button v-if="closeVisible" :loading="closeLoading" type="primary" @click="close()">
      <i class="el-icon-close mr-5"></i>关闭业务
    </el-button>

    <slot name="after" />
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "OperateBtn",
  props: {
    // 当前步骤
    currentStep: {
      type: Number,
      default: 1
    },
    // 步骤总数
    stepNum: {
      type: Number,
      default: 0
    },
    // 显示保存按钮
    saveVisible: {
      type: Boolean,
      default: true
    },
    // 保存加载
    saveLoading: {
      type: Boolean,
      default: false
    },
    // 显示上一步按钮
    prevVisible: {
      type: Boolean,
      default: true
    },
    // 提交加载
    prevLoading: {
      type: Boolean,
      default: false
    },
    // 显示下一步按钮
    nextVisible: {
      type: Boolean,
      default: true
    },
    // 下一步加载
    nextLoading: {
      type: Boolean,
      default: false
    },
    // 显示关闭按钮
    closeVisible: {
      type: Boolean,
      default: true
    },
    // 关闭加载
    closeLoading: {
      type: Boolean,
      default: false
    },
    // // 显示提交按钮
    // submitVisible: {
    //   type: Boolean,
    //   default: false
    // },
    // // 提交加载
    // submitLoading: {
    //   type: Boolean,
    //   default: false
    // },
    // 固定
    fixed: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    backToList() {
      this.$emit("back-to-list", this.currentStep);
    },
    reload() {
      this.$emit("reload", this.currentStep);
    },
    prev() {
      this.$emit("prev", this.currentStep);
    },
    next() {
      this.$emit("next", this.currentStep);
    },
    save() {
      this.$emit("save", this.currentStep);
    },
    close() {
      this.$emit("close", this.currentStep);
    }
    // submit() {
    //   this.$emit("submit", this.currentStep);
    // }
  }
};
</script>

<style lang="scss" scoped>
.operate-btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: -57px;

  .back-btn,
  .save-btn {
    background: #5bc0de;
    border-color: #5bc0de;
    align-items: center;

    &:hover {
      background: #75cbe4;
      border-color: #75cbe4;
    }
  }

  &.fixed {
    position: fixed;
    background: #fff;
    padding: 10px;
    width: 100%;
    left: 0;
    bottom: 0;
    z-index: 80;
    border-top: #eee 1px solid;
    box-shadow: rgba(0,0,0, 0.05) 0 0 8px;
    transition: all 0.2s;
  }
}
</style>
