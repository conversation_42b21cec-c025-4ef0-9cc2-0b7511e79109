<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="report.length && showReport">
      <div class="result-title mt-0">
        <span>【{{ name }}】放线报告</span>
      </div>
      <file-list
        :file-list="report"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
  </div>
</template>

<script>
/* eslint-disable */
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";
import BusinessResult from "mixins/business/result.js";

export default {
  name: "SetoutSurveyResult",
  mixins: [AttachmentDownload, BusinessResult],
  props: {
    report: {
      type: Array,
      default: () => []
    },
    showReport: {
      type: Boolean,
      default: false
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
</style>

