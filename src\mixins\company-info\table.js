/*
 * 模块 : 单位信息-表格功能相关配置(仅用于前端交互)
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-18
 * 版本 : version 1.0
 */
/* eslint-disable */

export default {
  props: {
    // 当前激活类型
    activeTabName: {
      type: String,
      default: "equipment"
    },
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 加载
    loading: {
      type: Boolean,
      default: true
    },
    // 禁止编辑
    disableEdit: {
      type: Boolean,
      default: false
    },
    // 单位类型
    companyInfoType: {
      type: String,
      default: "测绘单位"
    },
    // 单位名称
    companyInfoName: {
      type: String,
      default: ""
    },
    // 组件使用类型，register 单位注册 info 单位信息
    useType: {
      type: String,
      default: "info"
    },
    // 仅单位信息变更使用，new 新单位信息 old 原单位信息
    infoType: {
      type: String,
      default: "new"
    }
  },
  data() {
    return {
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title",
      },
      // 列表数据
      listData: [],
      // 全选选项列表
      selectList: [],
      // 存储弹窗配置
      storeDialog: {
        visible: false,
        row: null,
        index: -1
      }
    };
  },
  watch: {
    loading(val) {
      if (!val) {
        this.init();
      }
    },
    // activeTabName(val) {
    //   this.init();
    // },
    infoType(val){
      // 修复因有时差导致信息未变更的BUG
      setTimeout(() => this.init(), 0);
    }
  },
  methods: {
    // 初始化
    init() {
      this.listData = this.data && this.data.length ? [...this.data] : [];
    },
    // 获取选项
    getSelectList(selection) {
      // console.log(selection);
      // 清空选项列表
      this.clearSelect();
      if (!selection.length) return false;

      selection.forEach((e) => {
        this.selectList.push(e);
      });
    },
    // 清空选项
    clearSelect() {
      this.selectList = [];
    },
    // 删除一行
    delRow(row) {
      this.selectList = [row];
      this.del();
    },
    // 删除
    del() {
      if (!this.selectList.length) {
        this.$message.warning("请选择要删除的选项");
        return false;
      }

      this.selectList.forEach((selection) => {
        const index = this.listData.findIndex((e) => e === selection);
        this.listData.splice(index, 1);
      });

      // 删除成功要清除selectList
      this.clearSelect();
    },
    // 弹出添加/编辑窗
    showStore(row, index) {
      this.storeDialog = {
        visible: true,
        row,
        index
      };
    },
    // 取消存储
    // type: 1 编辑 0 添加
    cancelStore(type, params, index) {
      if (type === 1) {
        this.$set(this.listData, index, this.storeDialog.row);
      }
    },
    // 存储操作成功
    // type: 1 编辑 0 添加
    storeSuccess(type, params, index) {
      if (type === 1) {
        this.$set(this.listData, index, params);
      } else {
        this.listData.push(params);
      }

      this.storeDialog.visible = false;
    }
  },
};
