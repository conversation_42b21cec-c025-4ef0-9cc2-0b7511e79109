import Cookies from 'js-cookie'

const state = {
  // 设备类型
  device: 'desktop',
  // 组件大小
  size: Cookies.get('size') || 'medium',
  // 页面初始化loading
  pageLoading: false,
  // 系统初始化loading
  sysLoading: true,
  // 当前环境
  currentEnv: process.env.VUE_APP_ENV
}

const mutations = {
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  SET_PAGE_LOADING: (state, pageLoading) => {
    state.pageLoading = pageLoading
  },
  SET_SYS_LOADING: (state, sysLoading) => {
    state.sysLoading = sysLoading
  }
}

const actions = {
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setPageLoading({ commit }, pageLoading) {
    commit('SET_PAGE_LOADING', pageLoading)
  },
  setSysLoading({ commit }, sysLoading) {
    commit('SET_SYS_LOADING', sysLoading)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
