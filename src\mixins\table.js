/*
 * 模块 : 表格功能相关配置(仅用于后端交互)
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-10
 * 版本 : version 1.0
 */
export default {
  data() {
    return {
      // 筛选参数
      filterParams: {},
      // 默认参数
      defaultParams: {},
      // 表格数据
      listData: [],
      // 表格高度
      tableHeight: '500',
      // 全选选项列表
      selectList: [],
      // 表格配置属性
      tableProps: {
        prop: 'key',
        label: 'title'
      }
    }
  },
  methods: {
    /**
			* 设置表格固定高度
			*
			* @param {*} tableReduceH 缩减高度
			*/
    setTableHeight(tableReduceH) {
      const windowH = window.innerHeight
      if (this.tableHeight) {
        this.tableHeight = windowH - tableReduceH
      }
    },
    /**
			 * 获取选项id
			 *
			 * @param {*} selection 选项
			 */
    getSelectList(selection) {
      // 清空选项列表
      this.clearSelect()
      if (!selection.length) return false

      for (let i = 0; i < selection.length; i++) {
        this.selectList.push(selection[i].ID)
      }
    },
    /**
			 * 清空选项
			 */
    clearSelect() {
      this.selectList = []
    },
    /**
			 * 搜索
			 */
    search() {
      this.getList(1, this.page.pageSize)
    },
    /**
		   * 重置
		   */
    reset() {
      this.filterParams = { ...this.defaultParams }
      this.clearSelect()
      this.getList(1, this.page.pageSize)
    },
    /**
			 * 添加
			 *
			 * @param {*} params 参数
			 */
    add(params) {
      this.apiAdd(params).then(res => {
        if (res.StateCode === 1) {
          this.$message.success('添加成功!')
          this.getList(1, this.page.pageSize)
        } else this.$message.error(res.Message)
      }).catch(err => {
        console.log(err)
        this.$message.warning('服务器繁忙，请稍后重试')
      })
    },
    /**
			 * 编辑
			 *
			 * @param {*} params 参数
			 * @param {*} id 当前对象id
			 */
    edit(params, id) {
      this.apiEdit(params, id).then(res => {
        if (res.StateCode === 1) {
          this.$message.success('编辑成功!')
          this.getList(1, this.page.pageSize)
        } else this.$message.error(res.Message)
      }).catch(err => {
        console.log(err)
        this.$message.warning('服务器繁忙，请稍后重试')
      })
    },
    /**
			 * 删除
			 */
    del() {
      if (!this.selectList.length) {
        this.$message.warning('请选择要删除的选项')
        return false
      }

      const ids = this.selectList.join(',')

      this.apiDelete(ids).then(res => {
        if (res.StateCode === 1) {
          this.$message.success(res.Message)
          this.getList(1, this.page.pageSize)
        } else this.$message.error(res.Message)
      }).catch(err => {
        console.log(err)
        this.$message.warning('服务器繁忙，请稍后重试')
      })

      // 删除成功要清除selectList
      this.clearSelect()
    }
  }
}
