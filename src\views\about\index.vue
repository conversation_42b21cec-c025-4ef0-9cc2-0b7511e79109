<template>
  <!-- eslint-disable -->
  <div
    class="about-container"
    v-loading="loading"
    element-loading-text="加载中，请稍等..."
  >
    <template v-if="data.length">
      <el-card shadow="never" v-for="(info, i) in data" :key="'info' + i">
        <div class="introduction">
          <h2 class="introduction-title">{{ info.Name }}</h2>
          <div class="introduction-content">
            <template v-if="info.Infos.length">
              <div v-for="(item, j) in info.Infos" :key="'content' + j">
                <div class="introduction-content__wrapper">
                  <h3 v-if="item.Title" class="introduction-content__name">{{ item.Title }}</h3>
                  <div class="introduction-content__cont">
                    <img v-if="item.Img" :src="item.Img" class="introduction-content__pic" />
                    <div v-html="item.Content"></div>
                  </div>
                </div>
              </div>
            </template>
            <empty v-else></empty>
          </div>
        </div>
      </el-card>
    </template>
    <empty v-else></empty>
  </div>
</template>

<script>
/* eslint-disable */
import Api from "api/public";

export default {
  name: "About",
  data() {
    return {
      loading: false,
      data: []
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      Api.GetIntroduction()
        .then(res => {
          let data = res.Introduction;
          // 移除内联样式
          data.forEach(e => {
            e.Infos.forEach(o => {
              o.Content = o.Content.replace(/style=\"(.*?)\"/g, "");
            });
          });
          this.data = data;

          this.loading = false;
        })
        .catch(err => (this.loading = false));
    }
  }
};
</script>
<style lang="scss" scoped>
.about-container {
  padding: 10px 0 10px 0;

  /deep/ .el-card{
    margin-left: 0;
    margin-right: 0;
  }
}
.introduction {
  &-title {
    color: $color-primary;
    padding: 10px;
    // background: $color-primary;
    border-radius: 4px;
    text-align: center;
    margin-top: 0;
  }

  &-content {
    &__name {
      color: $color-primary;
      padding-left: 10px;
      position: relative;
      &::before{
        content: "";
        position: absolute;
        width: 4px;
        height: 80%;
        top:3px;
        left: 0;
        background: $color-primary;
      }
    }

    &__cont {
      img {
        max-width: 100%;
      }
    }
  }
}
</style>
