<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="completionLandArea || completionBuildingArea">
      <div class="result-title mt-0">
        <span>【{{ name }}】竣工面积信息</span>
      </div>
        <el-form>
         <el-row>
            <el-col :span="12">
             <el-form-item label="竣工用地面积（该地块用地面积）：" class="completion-area-item">{{ completionLandArea | isNull }}㎡</el-form-item>
            </el-col>
            <el-col :span="12">
             <el-form-item label="竣工总建筑面积（单体总建筑面积）：" class="completion-area-item">{{ completionBuildingArea | isNull }}㎡</el-form-item>
            </el-col>
         </el-row>
        </el-form>
    </template>
    <template v-if="report.length && showReport">
      <div class="result-title mt-0">
        <span>【{{ name }}】放线报告</span>
      </div>
      <file-list
        :file-list="report"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <template v-if="auditFeedback && auditFeedback.length">
      <div class="result-title mt-10">
        <span>【{{ name }}】成果备案反馈</span>
      </div>
      <div
        v-for="(item, index) in auditFeedback"
        :key="'auditFeedback' + index"
        class="result-file flex mb-15"
      >
        <div>{{ item.AttachmentName + item.AttachmentExt }}</div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(item)"
          >下载</el-button
        >
      </div>
    </template>
  </div>
</template>

<script>
/* eslint-disable */
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";
import BusinessResult from "mixins/business/result.js";

export default {
  name: "SetoutSurveyResult",
  mixins: [AttachmentDownload, BusinessResult],
  props: {
    report: {
      type: Array,
      default: () => [],
    },
    showReport: {
      type: Boolean,
      default: false,
    },
    // 成果反馈附件
    auditFeedback: {
      type: Array,
      default: null,
    },
    // 竣工土地面积
    completionLandArea: {
      type: String,
      default: null,
    },
    // 竣工建筑面积
    completionBuildingArea: {
      type: String,
      default: null,
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
.completion-area-item {
  padding-left: 85px;
}
</style>

