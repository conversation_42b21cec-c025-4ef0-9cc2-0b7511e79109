<template>
  <el-form ref="form" :model="form" label-width="140px">
    <el-form-item label-width="160px" label="工程规划许可证件：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.ProjectLicenceImg"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '工程规划许可证件',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ProjectLicenceImg')"
        @delete="del($event, 'form', 'ProjectLicenceImg')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ProjectLicenceImg"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="不动产权属证书：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.Cert"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '不动产权属证书',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'Cert')"
        @delete="del($event, 'form', 'Cert')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.Cert"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item
      label-width="160px"
      label="不动产权证书："
    >
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.PropertyCertificate"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '不动产权证书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PropertyCertificate')"
        @delete="del($event, 'form', 'PropertyCertificate')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PropertyCertificate"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item
      label-width="160px"
      label="土地出让合同/土地划拨决定书："
    >
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.LandContract"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '土地出让合同/土地划拨决定书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'LandContract')"
        @delete="del($event, 'form', 'LandContract')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.LandContract"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="规划审批版方案文本：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.SchemeText"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '规划审批版方案文本',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'SchemeText')"
        @delete="del($event, 'form', 'SchemeText')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.SchemeText"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="经规划审批的总平图：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="dwg"
        :file-list="form.SitePlan"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '经规划审批的总平图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'SitePlan')"
        @delete="del($event, 'form', 'SitePlan')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.SitePlan"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="经规划审批的总平图扫描件：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="dwg"
        :file-list="form.SitePlanScan"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '经规划审批的总平图扫描件',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'SitePlanScan')"
        @delete="del($event, 'form', 'SitePlanScan')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.SitePlanScan"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item
      label-width="160px"
      label="各单体初步核实证明："
    >
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.VerificationCert"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '各单体初步核实证明',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'VerificationCert')"
        @delete="del($event, 'form', 'VerificationCert')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.VerificationCert"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="城市基础设施配套费结清证明材料：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.CityConstruction"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '城市基础设施配套费结清证明材料',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'CityConstruction')"
        @delete="del($event, 'form', 'CityConstruction')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.CityConstruction"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="土地用途及占比相关佐证材料（国有建设用地出让合同、划拨决定书、不动产权证、土地使用证等）：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.LandUse"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '土地用途',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'LandUse')"
        @delete="del($event, 'form', 'LandUse')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.LandUse"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="园林绿化主管部门出具的工程质量监督报告：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.QualityControl"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '园林绿化主管部门出具的工程质量监督报告',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'QualityControl')"
        @delete="del($event, 'form', 'QualityControl')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.QualityControl"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="管网核实成果图：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.ResultMap"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '管网核实成果图',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ResultMap')"
        @delete="del($event, 'form', 'ResultMap')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ResultMap"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="项目跟踪卡：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.ProjectTrackCard"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '项目跟踪卡',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ProjectTrackCard')"
        @delete="del($event, 'form', 'ProjectTrackCard')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ProjectTrackCard"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="规划设计条件通知书：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.PlanNotice"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '规划设计条件通知书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PlanNotice')"
        @delete="del($event, 'form', 'PlanNotice')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PlanNotice"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="建设工程质量竣工预验收结论意见：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.AcceptComment"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '建设工程质量竣工预验收结论意见',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'AcceptComment')"
        @delete="del($event, 'form', 'AcceptComment')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.AcceptComment"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="有违法建筑经处理后的，提交处罚决定书及发票：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.PunishmentDecision"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '处罚决定书及发票',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PunishmentDecision')"
        @delete="del($event, 'form', 'PunishmentDecision')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PunishmentDecision"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="其他：">
      <list-upload
        v-if="(isRecordBack(5) && !isReplaceMdb) || isRecordAction(5)"
        file-format="png / jpg / gif / bmp / pdf / zip / rar"
        :file-list="form.Others"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '其他附件',
        }"
        :on-check-format="checkOthers"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'Others')"
        @delete="del($event, 'form', 'Others')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.Others"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import ViewInfo from 'mixins/business/view-info.js'

export default {
  name: 'OverallActualMappingAttachmentInfo',
  mixins: [ViewInfo],
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e
        if (e.length) {
          this.$refs[formName].clearValidate(attr)
        }
      }
      this.$emit('upload-success', e)
    }
  }
}
</script>
<style lang="scss" scoped></style>
