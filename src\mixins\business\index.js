/*
 * 模块 : 办理业务相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2021-02-20
 * 版本 : version 3.0
 */

// 组件
// Api
import Api from 'api/business/index.js'
import PublicApi from 'api/public/index.js'
import ListUpload from 'components/common/AttachmentUpload/ListUpload.vue'
import CheckAttachmentMixin from 'mixins/attachment/check.js'
import AttachmentMixin from 'mixins/attachment/index.js'
// mixins
import ScrollMixin from 'mixins/scroll.js'
import { getPageTitle } from 'utils'
// 工具
import { validForm } from 'utils/form.js'
import { downloadFileByStream } from 'utils/index.js'
import { scrollTo } from 'utils/scroll-to.js'
// vuex
import { mapGetters } from 'vuex'

export default {
  components: {
    ListUpload
  },
  mixins: [ScrollMixin, AttachmentMixin, CheckAttachmentMixin],
  computed: {
    ...mapGetters([
      'pageLoading',
      'userId',
      'companyType',
      'roles',
      'companyRole',
      'currentEnv'
    ]),
    // 测绘单位测绘人员
    surveyBusinessManager() {
      const { roles, companyRole } = this
      return roles.indexOf('c-m') >= 0 && companyRole === '测绘人员'
    },
    // 测绘单位注册测绘师
    registeredSurveyor() {
      const { roles, companyRole } = this
      return roles.indexOf('c-m') >= 0 && companyRole === '注册测绘师'
    },
    // 测绘单位管理员
    surveyAdmin() {
      const { roles, companyRole } = this
      return roles.indexOf('c-m') >= 0 && companyRole === '单位管理员'
    },
    // 测绘单位
    mappingCompany() {
      const { roles } = this
      return roles.indexOf('c-m') >= 0
    },
    // 建设单位
    developer() {
      const { roles } = this
      return roles.indexOf('c-d') >= 0
    }
  },
  data() {
    return {
      // 基本信息
      BaseInfo: {
        ID: null,
        BusinessClass: null,
        BusinessName: null,
        BusinessNumber: null,
        BusinessType: null,
        CreatePersonName: null,
        CreatePersonPhone: null,
        DeveloperName: null,
        SurveyCompanyName: null,
        // 0待签收，1办理中，2已完成，3已退回，4已关闭
        StateCode: 0,
        // 1是五象，其它否
        IsWuXiang: null,
        // 1是已办理规划核实业务，其它否
        IsCompletedGHHS: null
      },
      // 扩展信息，存放退回和关闭等原因
      ExtendInfo: {
        CloseReason: null,
        BackReason: null
      },
      // 业务流程信息
      FlowInfo: {},
      // 附件信息
      Attachments: [],
      // 当前流程
      CurrentAction: {},
      // 步骤流程
      ActionsInfos: [],
      // 上一步按钮
      prevBtn: {
        visible: true,
        loading: false
      },
      // 下一步按钮
      nextBtn: {
        visible: true,
        loading: false
      },
      // 保存按钮
      saveBtn: {
        visible: true,
        loading: false
      },
      // 关闭按钮
      closeBtn: {
        visible: true,
        loading: false
      },
      // 提交按钮
      submitBtn: {
        visible: true,
        loading: false
      },
      // 业务ID
      businessID: null,
      // 当前第几步
      currentStep: 1,
      // 步骤列表
      stepList: [],
      // 图片格式
      imgReg: /\.(bmp|jpg|jpeg|png|gif|webp|JPG|PNG|GIF)$/,
      // 原因弹窗配置
      reasonDialog: {
        visible: false,
        title: '关闭业务',
        loading: false
      },
      // 固定步骤栏
      fixedStepList: false,
      // 操作按钮配置
      operateBtn: {
        formBtnFixed: false,
        bottomBtnVisible: false
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    /**
     * 初始化
     */
    init() {
      // 判断业务id是否存在
      this.businessID = this.$route.query.id

      if (!this.businessID) {
        this.$message.warning('该业务不存在，即将返回业务列表')
        this.$router.push({ name: 'BusinessList' })
        return false
      }
      this.getBusiness()
    },
    /**
     * 滚动事件
     */
    $_scrollHandler() {
      const { documentElement, body } = document
      const scrollTop = documentElement.scrollTop || body.scrollTop

      if (scrollTop > 125) {
        this.fixedStepList = true
      } else {
        this.fixedStepList = false
      }
    },
    /**
     * 刷新页面
     */
    reload() {
      scrollTo(0)
      this.getBusiness()
    },
    /**
     * 页面加载
     * @param {Boolean} val 加载判断
     */
    setPageLoading(val) {
      this.$store.dispatch('app/setPageLoading', val)
    },
    /**
     * 获取业务信息
     * @param {Number} step 当前步骤
     */
    getBusiness(step = null) {
      this.setPageLoading(true)
      Api.GetBusiness(this.businessID)
        .then((res) => {
          const { StateCode, Data, Message } = res
          if (StateCode === 1) {
            this.refreshStep(Data, step)
          } else this.$message.error(Message)

          const { Catalog, FlowName } = Data.FlowInfo
          this.$route.meta.title = Catalog
            ? `${Catalog}-${FlowName}`
            : FlowName
          document.title = getPageTitle(
            Catalog ? `${Catalog}-${FlowName}` : FlowName
          )
          this.setPageLoading(false)
        })
        .catch((err) => {
          console.log(err)
          this.setPageLoading(false)
          // this.$message.error('服务器繁忙，请稍后重试')
        })
    },
    /**
     * 处理请求数据，在各模块替换该方法
     * @param {Object} Data 接口返回的数据
     */
    handleApiData(Data) {
      this.ContentInfo = Data.ContentInfo
      return true
    },
    /**
     * 处理步骤列表
     * @param {Number} step 当前步骤
     * @param {Array} list 流程列表
     */
    handleStepList(step, list) {
      if (!list.length || step <= 0) return false

      this.stepList = list.map((e, index) => {
        const { ID, Name } = e
        return {
          id: ID,
          name: Name,
          finished:
            step === list.length && this.BaseInfo.StateCode === 2
              ? true
              : index < step - 1
        }
      })

      this.currentStep = step
      this.GLOBAL.logInfo(`当前为第${step}步`)
      this.handleStep(step)

      if (this.BaseInfo.StateCode === 4) {
        this.hideOperateBtn()
      } else {
        if (step > 1) {
          this.closeBtn.visible = false
        } else {
          this.closeBtn.visible = true
        }
        this.handleOperateBtn(step)
      }

      // 清除表单校验
      const form = this.$refs[`form${step}`]
      if (form) {
        this.$nextTick(() => {
          form.clearValidate()
        })
      }
    },
    /**
     * 处理操作按钮，在各模块替换该方法
     * @param {Number} step 当前步骤
     */
    // 处理按钮
    handleOperateBtn(currentStep) {
      const { developer, mappingCompany } = this
      if (mappingCompany) {
        this.closeBtn.visible = false
        this.saveBtn.visible = false
        this.nextBtn.visible = false
        this.prevBtn.visible = false
      } else if (developer) {
        this.closeBtn.visible = currentStep <= 1
        this.saveBtn.visible = currentStep <= 1
        this.nextBtn.visible = currentStep <= 1
        this.prevBtn.visible = currentStep <= 2
      } else {
        this.hideOperateBtn()
      }
    },
    /**
     * 隐藏操作按钮
     */
    hideOperateBtn() {
      this.prevBtn.visible = false
      this.nextBtn.visible = false
      this.saveBtn.visible = false
      this.closeBtn.visible = false
      this.submitBtn.visible = false
      if (this.passBtn) {
        this.passBtn.visible = false
      }
      if (this.acceptBtn) {
        this.acceptBtn.visible = false
      }
    },
    /**
     * 处理步骤，在各模块替换该方法
     * @param {Number} step 当前步骤
     */
    handleStep(step) {
      return true
    },
    /**
     * 处理表单数据，在各模块替换该方法
     * @param {Number} step 当前步骤
     */
    handleFormData(step) {
      return true
    },
    /**
     * 点击按钮返回一步(即退回业务)
     */
    prev() {
      this.prevBtn.loading = true
      Api.BackBusiness({ id: this.businessID, actionId: this.CurrentAction.ID, backReason: '' })
        .then((res) => {
          const { StateCode, Message } = res

          if (StateCode === 1) {
            this.getBusiness()
          } else {
            this.$message.error(Message)
          }
          this.prevBtn.loading = false
        })
        .catch((err) => {
          console.log(err)
          this.prevBtn.loading = false
          // this.$message.error("服务器繁忙，请稍后重试");
        })
    },
    /**
     * 更新步骤
     * @param {Object} Data 接口返回的数据
     * @param {Number} step 当前步骤
     */
    refreshStep(Data, step = null) {
      const {
        BaseInfo,
        ContentInfo,
        FlowInfo,
        ActionsInfos,
        Attachments,
        CurrentAction
      } = Data

      this.BaseInfo = BaseInfo
      this.Attachments = Attachments
      this.CurrentAction = CurrentAction
      this.FlowInfo = FlowInfo
      this.ActionsInfos = ActionsInfos
      const ExtendInfo = JSON.parse(BaseInfo.ExtendInfo)
      this.ExtendInfo = { ...this.ExtendInfo, ...ExtendInfo }

      // 额外处理请求数据
      this.handleApiData(Data)

      const Actions = [...FlowInfo.FlowActionInfo.Actions]
      const index = Actions.findIndex((e) => e.ID === CurrentAction.ActionId)

      const currentStep = step || (index > 0 ? index + 1 : 1)
      this.handleStepList(currentStep, FlowInfo.FlowActionInfo.Actions)

      if (ContentInfo) {
        this.handleFormData(currentStep)
      }
    },
    /**
     * 点击按钮进行下一步
     * @param {Number} step 当前步骤
     */
    async next(step) {
      this.GLOBAL.logInfo(`提交第${step}步的内容并校验`)

      const valid = await this.checkStepForm(step)
      if (!valid) return false

      this.nextBtn.loading = true

      const isSaved = await this.saveBusiness(false)
      if (isSaved) {
        Api.SubmitBusiness(this.businessID, this.CurrentAction.ID)
          .then((res) => {
            const { StateCode, Data, Message } = res

            if (StateCode === 1) {
              this.refreshStep(Data)
            } else {
              this.$message.error(Message)
            }
            this.nextBtn.loading = false
          })
          .catch((err) => {
            console.log(err)
            this.nextBtn.loading = false
            // this.$message.error("服务器繁忙，请稍后重试");
          })
        scrollTo(0)
      }
    },
    /**
     * 检查步骤表单
     * @param {Number} step 当前步骤
     */
    async checkStepForm(step) {
      const formName = `form${step}`
      const form = this.$refs[formName]

      if (form) {
        const valid = await validForm(form, '请检查提交内容，必填项不可为空')
        if (valid) return true
        else return false
      }
      return true
    },
    /**
     * 点击按钮保存
     * @param {Number} step 当前步骤
     */
    save(step) {
      this.GLOBAL.logInfo(`保存第${step}步的内容`)

      this.saveBtn.loading = true
      this.saveBusiness()
    },
    /**
     * 保存业务
     * @param {Boolean} showSaveMsg 是否显示保存提示语
     */
    saveBusiness(showSaveMsg = true) {
      const data = this.handleBusinessData()

      return new Promise((resolve, reject) => {
        Api.SaveBusiness(this.businessID, { model: JSON.stringify(data) })
          .then((res) => {
            const { StateCode, Message } = res

            if (StateCode === 1) {
              if (showSaveMsg) {
                this.$message.success('保存成功')
              }
              resolve(true)
            } else {
              this.$message.error(Message)
              this.nextBtn.loading = false
              resolve(false)
            }
            this.saveBtn.loading = false
          })
          .catch((err) => {
            console.log(err)
            this.saveBtn.loading = false
            this.nextBtn.loading = false
            resolve(false)
            // this.$message.error("服务器繁忙，请稍后重试");
          })
      })
    },
    /**
     * 预览
     * @param {Object} file 文件
     */
    preview(file) {
      if (this.imgReg.test(file.AttachmentExt)) {
        // 图片
        this.previewImg(file)
      } else if (file.AttachmentExt.indexOf('.pdf') >= 0) {
        // pdf
        this.previewPdf(file)
      }
      return false
    },
    // 返回列表
    backToList() {
      const { BaseInfo, currentStep, mappingCompany } = this

      if (currentStep <= 2 || currentStep === 4) {
        this.$router.push({
          name: mappingCompany ? 'BusinessDone' : 'BusinessInProgress'
        })
        return
      }
      if (currentStep === 3) {
        this.$router.push({
          name: mappingCompany ? 'BusinessInProgress' : 'BusinessDone'
        })
        return
      }
      // 已完成
      if (BaseInfo.StateCode === 2) {
        this.$router.push({ name: 'BusinessDone' })
        return
      }

      this.$router.push({ name: 'BusinessInProgress' })
    },
    /**
     * 原因弹窗可见性
     * @param {String} title 名称
     * @param {Boolean} visible 可见性
     */
    setReasonDialogVisible(title, visible) {
      this.reasonDialog = {
        visible,
        title,
        loading: false
      }
    },
    /**
     * 提交原因弹窗
     * @param {BooleStringan} reason 原因
     * @param {String} title 名称
     */
    handleReasonDialogSubmit(reason, title) {
      switch (title) {
        case '关闭业务':
          this.close(reason)
          break
        case '退回业务':
          this.back(reason)
          break
        case '验收不通过':
          this.back(reason)
          break
        default:
          console.log("reasonDialog's title is error")
      }
    },
    /**
     * 关闭业务
     * @param {String} closeReason 关闭原因
     */
    close(closeReason) {
      this.$confirm('确认关闭该业务吗？该操作不可逆，请谨慎处理', '温馨提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      })
        .then(async() => {
          this.reasonDialog.loading = true
          Api.CloseBusiness(this.businessID, closeReason)
            .then((res) => {
              const { StateCode, Message } = res

              if (StateCode === 1) {
                this.$message.success('业务关闭成功')
                this.reasonDialog.visible = false
                this.getBusiness()
              } else {
                this.$message.error(Message)
              }
              this.reasonDialog.loading = false
            })
            .catch((err) => {
              console.log(err)
              this.reasonDialog.loading = false
            })
          scrollTo(0)
        })
        .catch((err) => console.log(err))
    },
    /**
     * 退回业务 修改至2021-03-16，请求参数传Body
     * @param {String} backReason 退回原因
     */
    back(backReason) {
      this.reasonDialog.loading = true
      const { businessID, CurrentAction, $message, backToList } = this
      Api.BackBusiness({
        id: businessID,
        actionId: CurrentAction.ID,
        backReason
      })
        .then((res) => {
          const { StateCode, Message } = res

          if (StateCode === 1) {
            $message.success('业务退回成功')
            this.BaseInfo.StateCode = 3
            backToList()
          } else {
            $message.error(Message)
          }
          this.reasonDialog.loading = false
        })
        .catch((err) => {
          console.log(err)
          this.reasonDialog.loading = false
        })
    },
    /**
     * 下载系统附件
     */
    downloadSystemAttachment(fileName) {
      this.downLoading = true
      PublicApi.DownloadSystemAttachment(fileName)
        .then((res) => {
          downloadFileByStream(res, fileName)
          this.downLoading = false
        })
        .catch(() => {
          this.downLoading = false
          this.$message.error('服务器繁忙，请稍后重试')
        })
    },
    /**
     * 清除定时器
     * @param {String} timer 定时器
     */
    clearTimer(timer) {
      if (timer) {
        clearInterval(timer)
        timer = null
      }
    }
  }
}
