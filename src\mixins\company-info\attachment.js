/*
 * 模块 : 单位信息附件相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-28
 * 版本 : version 1.0
 */
/* eslint-disable */

// Api
import Api from "api/public/index.js";
// vuex
import { mapGetters } from "vuex";

export default {
  computed: {
    ...mapGetters(["companyID"])
  },
  data() {
    return {
      downLoading: false,
      // 图片预览弹窗
      imgDialog: {
        visible: false,
        title: null,
        imgUrl: null
      }
    };
  },
  methods: {
    // 设置表单
    setForm(){
      if(this.data && Object.keys(this.data).length){
        let data = { ...this.data };

        // 添加附件字段（解决后端返回的附件字段缺少问题）
        data = {...data, ...this.attachmentInfo};

        // 应后端接口需求特殊处理AttachmentInfo字段
        if(this.data.AttachmentInfo){
          delete data.AttachmentInfo;

          const field = JSON.parse(this.data.AttachmentInfo);
          this.form = {...data, ...field};
        }
        else{
          this.form = data;
        }  
      }
      else{
        this.form = { ...this.defaultForm, ...this.attachmentInfo };
      }
    },
    // 附件上传
    upload(e, formName, attr) {
      this.form[attr] = e;
      if (e.length && formName) {
        this.$refs[formName].clearValidate(attr);
      }
    },
    // 预览图片
    preview(file) {
      this.imgDialog = {
        visible: true,
        title: file.AttachmentCategories,
        imgUrl: Api.ShowImg(file.ID)
      };
    },
    // 取消预览
    cancelPreview() {
      this.imgDialog = {
        visible: false,
        title: null,
        imgUrl: null
      };
    },
    // 删除
    del(e, formName, attr) {
      if (!e.file.ID) return false;

      Api.DeleteAttachment(e.file.ID).then(res => {
        if (formName) {
          this.$refs[formName].model[attr] = e.list;
          if (e.list.length) {
            this.$refs[formName].clearValidate(attr);
          }
        }
        this.GLOBAL.logInfo(`附件删除成功`);
      }).catch(err => {
        console.log(err);
      });
    }
  },
};
