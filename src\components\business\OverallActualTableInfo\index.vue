<template>
  <!-- eslint-disable -->
  <div v-if="buildingTableInfo.length">

      <div class="operate-btn flex">
        <el-button
          v-if="conditionVerificateInfo"
          type="primary"
          icon="el-icon-download"
          @click="download(conditionVerificateInfo)"
          >下载竣工规划条件全面核实及土地核验信息表</el-button
        >
      </div>

  </div>
</template>

<script>
/* eslint-disable */
// 组件
import PropertyInfoTableDialog from "components/business/PropertyInfoTableDialog/index.vue";
// Api
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";

export default {
  name: "OverallActualTableInfo",
  mixins: [AttachmentDownload],
  // components: { PropertyInfoTableDialog },
  props: {
    // 竣工规划条件核实信息表pdf
    conditionVerificateInfo: {
      type: Object,
      default: null,
    },
    // 楼盘信息
    buildingTableInfo: {
      type: Array,
      default: null,
    },
  },
  methods: {
    downloadStart(file) {
      this.$emit("download-start", file);
    },
    downloadEnd(file) {
      this.$emit("download-end", file);
    },
    downloadFail(file) {
      this.$emit("download-fail", file);
    },
    view(info) {
      this.propertyDialog = {
        visible: true,
        info,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  .inside {
    border-top: none;
    border-left: none;
    border-right: none;

    tr:last-child {
      th,
      td {
        // border-bottom: none;
      }
    }
  }

  th {
    background: #f8f8f8;
    color: #909399;
    // min-width: 130px;
    // width: 110px;
    // max-width: 130px;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    // min-width: 130px;
  }
}

.operate-btn {
  justify-content: center;
  // border-bottom: 1px solid #dfe6ec;
  padding: 20px 0;
}
.th-width {
  width: 10%;
}
.td-width {
  width: 40%;
}
.br-none {
  border-right: none !important;
}
.bb-none {
  border-bottom: none !important;
}
.table.inside1 tr:last-child .inside2 td,
.table.inside1 tr:last-child > th {
  border-bottom: none;
}
</style>