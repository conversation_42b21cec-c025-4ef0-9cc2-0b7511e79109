<template>
  <!-- eslint-disable -->
  <div>
    <tips :response-msg="responseMsg" :company-state-code="companyStateCode" />
    <!-- 需要协议备案 -->
    <agreement v-if="companyStateCode === 6" />
    <div
      class="company-info-container tabs"
      :class="{
        'has-tips': companyStateCode > 0,
        'has-agreement': companyStateCode === 6,
      }"
    >
      <el-tabs v-model="activeName" type="card" class="tabs-title">
        <el-tab-pane
          v-for="item in tabs"
          :key="item.label"
          :label="item.label"
          :name="item.name"
        >
          <component
            :ref="item.name"
            :is="item.component"
            :active-tab-name="activeName"
            :data="data[item.data]"
            :loading="pageLoading"
            :disable-edit="disableEdit"
            :company-info-type="companyType"
            :company-info-name="companyName"
            use-type="register"
            @change="change"
          />
        </el-tab-pane>
      </el-tabs>
      <div v-if="!disableEdit" class="operate-btn-container">
        <el-button type="default" @click="remove">撤销申请</el-button>
        <el-button type="primary" :loading="saveLoading" @click="save"
          >保存</el-button
        >
        <el-button type="primary" :loading="submitLoading" @click="check"
          >提交注册</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
/// 组件
import Tips from "./tips.vue";
import Agreement from "./agreement.vue";
import Member from "views/user/company-info/tab/member";
// mixins
import CompanyInfoTabs from "mixins/company-info/tabs.js";
import CompanySubmit from "mixins/company-info/submit.js";
// Api
import Api from "api/company-info/index.js";
import UserApi from "api/user/index.js";

export default {
  name: "DeveloperRegister",
  components: {
    Member,
    Tips,
    Agreement,
  },
  mixins: [CompanyInfoTabs, CompanySubmit],
  data() {
    return {
      timer: null,
      // 默认单位信息
      defaultCompany: {
        stateCode: 0,
        companyType: null,
        companyName: null,
        companyID: null,
      },
    };
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    init() {
      this.initTabs();
      this.$route.meta.title = `${
        this.roles.indexOf("c-m") >= 0 ? "测绘单位" : "建设单位"
      }注册信息`;
      this.getRegisterInfo();
      this.getCompanyAudit();
    },
    // 获取正在申请的单位注册信息
    async getRegisterInfo() {
      const {
        setPageLoading,
        unRegistered,
        defaultCompany,
        roles,
        initTabs,
        $store,
        $router,
        $message,
      } = this;

      setPageLoading(true);

      try {
        const companyInfo = await UserApi.GetCompanyInfoByUserInfo();

        // 已绑定单位
        if (companyInfo.StateCode === 1 && companyInfo.Data) {
          const { CompanyList, CompanyType } = companyInfo.Data;

          let company = { ...defaultCompany };

          if (CompanyList.length) {
            let currentCompany = CompanyList[0];

            const { CID, CName, CType, CRole, State } = currentCompany;

            company = {
              stateCode: State,
              companyType: CType, // 因为只有测绘单位注册，所以类型都是测绘单位
              companyName: CName,
              companyID: CID,
              companyRole: CRole,
            };

            // 审核通过进入“单位信息”页面
            if (State === 3) {
              // 修改单位信息
              await $store.dispatch("user/setCompany", company);
              await $store.dispatch("user/setCompanyList", CompanyList);

              let newRoles = [...roles];
              newRoles.splice(newRoles.findIndex((e) => e === "1"), 1);

              // 已完成注册
              let role =
                CompanyType === "测绘单位" ? ["c-m", "2"] : ["c-d", "2"];
              newRoles = newRoles.concat(role);

              // 修改路由权限
              await $store.dispatch("permission/generateRoutes", {
                roles: newRoles,
              });
              // 修改角色
              await $store.dispatch("user/setRoles", newRoles);

              setPageLoading(false);

              $message.success("您申请注册的单位已审核通过");
              $router.push({ name: "CompanyInfo" });

              return true;
            } else {
              // 获取注册信息
              const registerInfo = await Api.GetCompanyRegisterInfo();

              if (registerInfo.StateCode === 1 && registerInfo.Data) {
                const {
                  DetailInfo,
                  ResponseMessage,
                  StateCode,
                } = registerInfo.Data;

                // 未提交、退回修改等状态可修改
                if (StateCode === 0 || StateCode === 4) {
                  this.disableEdit = false;
                } else {
                  this.disableEdit = true;
                }

                this.data = JSON.parse(DetailInfo);
                this.companyName = this.data.CompanyBaseInfo.CompanyName;

                this.responseMsg = ResponseMessage;
                await $store.dispatch("user/setCompanyStateCode", StateCode);

                if (!this.data) {
                  initTabs();
                }

                setPageLoading(false);

                return true;
              }
            }
          } else {
            // 未进行单位注册
            await unRegistered();
            this.disableEdit = true;
            return false;
          }
        }
        // 未进行单位注册
        await unRegistered();
        this.disableEdit = true;
        return false;
      } catch (err) {
        console.log(err);
        this.disableEdit = true;
        setPageLoading(false);
      }
    },
    // 未进行单位注册
    unRegistered() {
      const {
        setPageLoading,
        defaultCompany,
        $message,
        $store,
        $router,
      } = this;
      return new Promise(async (resolve, reject) => {
        try {
          setPageLoading(false);
          $message.warning("您还未进行单位注册申请！请注册");
          await $store.dispatch("user/setCompany", defaultCompany);
          $router.push({ name: "CompanyRegister" });
          resolve(true);
        } catch (err) {
          console.log(err);
          reject(false);
        }
      });
    },
    // 获取单位审核状态，根据状态修改权限和菜单
    getCompanyAudit() {
      if (this.timer) {
        this.clearTimer();
      }

      const {
        companyStateCode,
        defaultCompany,
        roles,
        clearTimer,
        $message,
        $router,
        $store,
      } = this;
      if (companyStateCode === 3 || companyStateCode === 5) return;
      else if (companyStateCode > 0) {
        this.timer = setInterval(async () => {
          console.log(111);
          try {
            const companyInfo = await UserApi.GetCompanyInfoByUserInfo();

            if (companyInfo.StateCode === 1 && companyInfo.Data) {
              const { CompanyList, CompanyType } = companyInfo.Data;

              let company = { ...defaultCompany };

              if (CompanyList.length) {
                let currentCompany = CompanyList[0];

                const { CID, CName, CType, CRole, State } = currentCompany;

                company = {
                  stateCode: State,
                  companyType: CType, // 因为只有测绘单位注册，所以类型都是测绘单位
                  companyName: CName,
                  companyID: CID,
                  companyRole: CRole,
                };

                // 审核通过
                if (State === 3) {
                  clearTimer();

                  // 修改单位信息
                  await $store.dispatch("user/setCompany", company);
                  await $store.dispatch("user/setCompanyList", CompanyList);

                  let newRoles = [...roles];
                  newRoles.splice(newRoles.findIndex((e) => e === "1"), 1);

                  // 已完成注册
                  let role =
                    CompanyType === "测绘单位" ? ["c-m", "2"] : ["c-d", "2"];
                  newRoles = newRoles.concat(role);

                  // 修改路由权限
                  await $store.dispatch("permission/generateRoutes", {
                    roles: newRoles,
                  });
                  // 修改角色
                  await $store.dispatch("user/setRoles", newRoles);

                  $message.success("您申请注册的单位已审核通过");
                  $router.push({ name: "CompanyInfo" });
                }

                // 退回
                if (State === 4) {
                  clearTimer();
                  // 开放修改
                  this.disableEdit = false;
                }
              }
            } else {
              clearTimer();
              this.disableEdit = true;
            }
          } catch (err) {
            console.log(err);
            clearTimer();
          }
        }, 30000);
      }
    },
    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/compnay-info-tabs.scss";
</style>
