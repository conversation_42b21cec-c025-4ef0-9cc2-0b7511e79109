<template>
  <!-- eslint-disable -->
    <el-card shadow="never" class="overspread-card">
    <div class="operate-btn-container" style="right: 35px;">
      <el-button type="default" @click="$router.push({ name: 'CompanyInfo' })"
        >返回单位列表</el-button
      >
    </div>

    <div>
      <tip type="default" class="mb-20" style="margin-top: 50px;" id="tip">温馨提示：您即将注销“多测合一”信息系统，在注销完成前，确保已完成相关测绘业务、履行测绘合同规定条款及其他未了结事务，清算工作全面完结。注销登记完成后，自动删除此单位和单位下的账户信息，自动确认退出南宁市“多测合一”测绘服务机构名录库，不再受项目业主委托开展测绘活动。</tip>
      
      <!-- eslint-disable -->
      <div class="with-draw-container" style="margin-top: 20px;">
        <!-- 存储 -->
        <el-form
          ref="withDrawForm"
          :model="form"
          :rules="rules"
          :label-width="labelWidth"
        >

        <el-form-item label="注销理由：" prop="Reason" v-if="this.canWithDraw===true">
          <el-select            
            v-model.trim="defaultForm.WithDrawType"
            placeholder="请选择注销理由"
            style="width: 100%"
            :disabled="this.disableEdit"
          >
            <el-option
              v-for="item in WithDrawTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label prop="OtherReason">
        <el-input
          v-if="defaultForm.WithDrawType===3"
          type="textarea"
          v-model="defaultForm.OtherReason"
          placeholder="请填写其他理由"
          :autosize="{ minRows: 6 }"
          maxlength="500"   
        ></el-input>

        <el-button type="primary" :loading="submitLoading" @click="checkCanWithDraw" 
        v-if="this.canWithDraw===false"
        style="margin-top: 20px;margin-left:300px;"
          >检查是否满足注销条件</el-button
        >

        <el-button type="primary" :loading="submitLoading" @click="check" 
        v-if="this.canWithDraw===true"
        style="margin-top: 20px;"
          >开始注销</el-button
        >
        </el-form-item>

        </el-form>
      </div>
    </div>

  </el-card>
</template>

<script>
/* eslint-disable */

// Api
import CompanyInfoApi from "api/company-info/index.js";

export default {
  name: "CompanyWithDraw",

  props: {

  },

  data() {
    return {      
      submitLoading: false,
      disableEdit: false,
      canWithDraw: false,

      defaultForm: {
        WithDrawType: null, //注销理由
        OtherReason: null, //其他原因        
      },
      form: {},
      //注销理由
      WithDrawTypeOptions: [
        {
          value: 1,
          label: "已注销测绘资质",
        },
        {
          value: 2,
          label: "暂不参与多测合一测绘市场",
        },
        {
          value: 3,
          label: "其他",
        },
      ],
      labelWidth: "165px",
      // 基本信息规则
      rules: {
        WithDrawType: [
          { required: true, message: "请选择注销理由", trigger: "change" },
        ],
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {

    },

    /**
     * 页面加载
     * @param {Boolean} val 加载判断
     */
    setPageLoading(val) {
      this.$store.dispatch('app/setPageLoading', val)
    },

    //检查是否满足注销条件
    checkCanWithDraw() {
      this.submitLoading = true;

      CompanyInfoApi.CheckCompanyCanWithDraw()
        .then(async (res) => {
          if (res.StateCode === 1) {
            this.$message.success("检查已满足注销条件");
            this.canWithDraw = true;
          } else {
            this.$message.error(res.Message);
            this.canWithDraw = false;
          }
          this.submitLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.submitLoading = false;
          this.$message.error("服务器繁忙，请稍后重试");
          this.submitLoading = false;
        });
    },

    // 校验
    check() {
      if (!this.defaultForm.WithDrawType) {
          this.$message.error("请选择注销理由！");
          return;
      }
      if (this.defaultForm.WithDrawType == 3) {
        if ($.trim(this.defaultForm.OtherReason) == '') {
          this.$message.error("请填写其他理由！");
          return;
        }
      }
      const data = {
        reason: this.defaultForm.WithDrawType,
        otherReason: this.defaultForm.OtherReason
      };

      this.$confirm(
        `<p>您即将注销“多测合一”信息系统，在注销完成前，确保已完成相关测绘业务、履行测绘合同规定条款及其他未了结事务，清算工作全面完结。注销登记完成后，自动删除此单位和单位下的账户信息，自动确认退出南宁市“多测合一”测绘服务机构名录库，不再受项目业主委托开展测绘活动。</p>`,
        "温馨提示",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "下一步",
          cancelButtonText: "取消",
        }
      )
        .then((res) => {
            this.$confirm(
          `<p>确认注销测绘单位？</p>`,
          "温馨提示",
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
          }
        )
          .then((res) => {
            this.submit(data);
          })
          .catch((err) => console.log(err));
        })
        .catch((err) => console.log(err));

    },

    //提交表单
    submit(data) {
      this.submitLoading = true;

      CompanyInfoApi.CreateCompanyWithDrawInfo(data)
        .then(async (res) => {
          if (res.StateCode === 1) {
            this.$message.success("注销成功，将自动退出登录");

            //注销登录
            this.logout();

          } else {
            this.$message.error(res.Message);
            // 重新获取信息

          }
          this.submitLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.submitLoading = false;
          this.$message.error("服务器繁忙，请稍后重试");
        });
    },

    //注销登录
    async logout() {
      try {
        await this.$store.dispatch("user/logout");
        await this.$store.dispatch("permission/generateRoutes", null);
        this.$message.success("您已退出该系统");
        this.$router.push({ name: "Home" });
      } catch (err) {
        console.log(err);
        this.$router.push({ name: "Home" });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/compnay-info-tabs.scss";
</style>