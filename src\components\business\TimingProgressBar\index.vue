<template>
  <!-- eslint-disable -->
  <el-progress
    :stroke-width="strokeWidth"
    :show-text="showText"
    :text-inside="textInside"
    :color="color"
    :percentage="percentage"
  ></el-progress>
</template>

<script>
/* eslint-disable */
export default {
  name: "TimingProgressBar",
  props: {
    strokeWidth: {
      type: Number,
      default: 16,
    },
    showText: {
      type: Boolean,
      default: true,
    },
    textInside: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      default: "rgb(60, 184, 241)",
    },
    increment: {
      type: Number,
      default: 1,
    },
    duration: {
      type: Number,
      default: 3000,
    },
    isFinished: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      percentage: 0,
      timer: null,
    };
  },
  watch: {
    isFinished(val){
      if(val){
        this.clearTimer();
        this.percentage = 100;
      }
    }
  },
  created() {
    this.init();
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    init() {
      this.percentage = 0;

      const { duration, increment, clearTimer } = this;

      this.timer = setInterval(() => {
        let percentage = this.percentage + increment;
        this.percentage = Math.round(percentage);

        if (this.percentage >= 100) {
          this.percentage = 100;
          clearTimer();
        }
      }, duration);
    },
    clearTimer() {
      clearInterval(this.timer);
      this.timer = null;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-progress__text{
  font-size: 16px !important;
}
</style>
