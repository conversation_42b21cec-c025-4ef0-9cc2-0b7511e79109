<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      @close="close"
      class="setting-dialog-container"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="姓名：">{{ row.PersonName }}</el-form-item>
        <el-form-item
          label="业务类型："
          prop="BusinessClasses"
          class="business-class"
        >
          <div style="margin: 8px 0;"></div>
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="checkAllBusinessClasses"
            >全选</el-checkbox
          >
          <div style="margin: 10px 0;"></div>
          <el-checkbox-group
            v-model="form.BusinessClasses"
            @change="checkBusinessClasses"
          >
            <el-checkbox
              v-for="business in businessOptions"
              :label="business.BusinessClass"
              :key="business.BusinessClass"
              >{{ business.BusinessName }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="form.BusinessClasses.length"
          label="授权截止日期："
          prop="AuthEndDate"
        >
          <el-date-picker
            type="date"
            placeholder="请选择授权截止日期"
            v-model="form.AuthEndDate"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            class="width-100"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submitForm('form')" :loading="loading"
          >提交申请</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/registered-surveyor-auth/index.js";

export default {
  name: "RegisteredSurveyorAuthSetting",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 记录数据
    row: {
      type: Object,
      default: () => ({
        ID: null,
        CompanyType: "测绘单位",
      }),
    },
    // 业务选项
    businessOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      title: "注册测绘师授权申请",
      form: {
        BusinessClasses: [],
        AuthEndDate: "",
      },
      rules: {
        BusinessClasses: [
          { required: true, message: "请选择授权业务类型", trigger: "change" },
        ],
        AuthEndDate: [
          { required: true, message: "请选择授权截止日期", trigger: "change" },
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          const nowDate = new Date();
          const nextYearDate = new Date();

          return (
            time.getTime() < nowDate.getTime() ||
            time.getTime() >=
              nextYearDate.setFullYear(nowDate.getFullYear() + 1)
          );
        },
      },
      checkAll: false,
      isIndeterminate: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      console.log(this.businessOptions);
      const { row, getAuthInfo, formatBusinessClasses } = this;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
      if (row.AuthRequest) {
        const { BusinessClasses, AuthEndDate } = row;
        this.form = {
          BusinessClasses: formatBusinessClasses(BusinessClasses),
          AuthEndDate,
        };
      }
    },
    // 关闭存储弹窗
    close() {
      this.$refs.form.resetFields();
      this.isIndeterminate = false;
      this.checkAll = false;
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 全选业务类型
    checkAllBusinessClasses(val) {
      if (val) {
        this.form.BusinessClasses = this.businessOptions.map((e) => e.BusinessClass);
      } else {
        this.form.BusinessClasses = [];
        this.form.AuthEndDate = null;
      }
      this.isIndeterminate = false;
    },
    // 勾选业务类型
    checkBusinessClasses(val) {
      let checkedCount = val.length;

      const { businessOptions } = this;

      this.checkAll = checkedCount === businessOptions.length;

      this.isIndeterminate =
        checkedCount > 0 && checkedCount < businessOptions.length;
    },
    // 业务类型格式化
    formatBusinessClasses(BusinessClasses) {
      let classes = [];
      BusinessClasses.forEach((e) => {
        const businessClass = this.businessOptions.find(
          (c) => c.value === e.BusinessClass
        );
        classes.push(businessClass.BusinessClass);
      });

      return classes;
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true;
          const { form, row } = this;
          const { BusinessClasses, AuthEndDate } = form;
          const { AuthRequest, RelationRequestId, ID, PersonNumber } = row;

          // 重组提交到后端的数据
          const formatBusinessClasses = BusinessClasses.map((e) => ({
            BusinessClass: e,
          }));

          const SurveyMasterAuthRequest = {
            // ID为null将新增，ID不为null为修改，若ID不为null但是找不到记录，将返回失败
            // ID: AuthRequest ? AuthRequest.ID : null,
            ID: null, // 只能新增授权或重新授权，不可修改
            CompanyID: RelationRequestId,
            SurveyMasterID: ID, // 注册测绘师的ID
            SurveyMasterPersonNo: PersonNumber,
            AuthEndDate: `${AuthEndDate} 00:00:00`,
            CreateDate: null,
            Creator: null,
            AcceptDate: null,
            // 0 待确认，1 已确认，2 已过期，-1 已撤销
            StateCode: 0,
            BusinessClasses: formatBusinessClasses,
          };

          Api.SaveSurveyMasterAuth(SurveyMasterAuthRequest)
            .then((res) => {
              const { StateCode, Data, Message } = res;

              this.loading = false;

              if (StateCode === 1) {
                this.$message.success("提交成功");
                this.$emit("submit", SurveyMasterAuthRequest);
                this.close();
              } else {
                this.$message.error(Message);
              }
            })
            .catch((err) => {
              console.log(err);
              this.loading = false;
            });
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.business-class {
  /deep/ .el-form-item__content {
    line-height: 24px;
  }
}
</style>
