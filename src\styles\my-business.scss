.business-list-container {
  .top {
    justify-content: space-between;
  }

  .tag {
    &-yellow {
      &.el-tag--dark {
        background: #f0ad4e;
        border-color: #f0ad4e;
      }
    }

    &-blue {
      &.el-tag--dark {
        background: #409eff;
        border-color: #409eff;
      }
    }
  }

  .classify {
    justify-content: space-between;
  }

  .tabs {
    &-item {
      cursor: pointer;
      padding: 10px 15px;
      border-radius: 4px;
      margin: 0 1px;

      &.is-active {
        background: $color-primary;
        position: relative;
        color: #fff;

        &:hover {
          background: $color-primary;
          color: #fff;
        }

        &:before {
          border: 6px solid transparent;
          border-bottom-color: #fff;
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          margin-left: -6px;
          z-index: 1;
        }
      }

      &:hover {
        background: #eee;
      }
    }
  }

  /deep/ .el-table {
    tr.disabled {
      background: #f2f2f2;
      color: #b5b5b5;
    }
  }
}