/*
 * 模块 : 测绘成果上传、检查、确认、验收等相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-07-25
 * 版本 : version 1.0
 */
/* eslint-disable */
// 组件
import MdbUpload from "components/business/MdbUpload/index.vue";
// Api
import Api from "api/business/index.js";
import CheckLineApi from "api/business/check-line.js";
import PlanningPointApi from "api/business/planning-point.js";
// 工具
import { scrollTo } from "utils/scroll-to.js";
import { downloadFileByStream } from "utils/index.js";

export default {
  components: {
    MdbUpload,
  },
  data() {
    return {
      // 通过按钮
      passBtn: {
        visible: true,
        loading: false,
      },
      // 验收按钮
      acceptBtn: {
        visible: true,
        loading: false,
      },
      // 测绘成果检查定时器
      resultTimer: null,
      // 测绘成果mdb
      surveyResult: null,
      // 测绘成果检查完成
      surveyResultCheckFinished: false,
    };
  },
  methods: {
    // 测绘成果上传成功
    resultUpload(checkId, list) {
      const { BusinessClass } = this.BaseInfo;

      this.form3.Data = [{ name: "hasResult", status: "success" }];
      console.log(BusinessClass);

      // 拨地定桩
      if (BusinessClass === "MarkPointSurveyFlow") {
        this.ContentInfo.AchievementCheckID = checkId;
        this.ContentInfo.AchievementCheckState = 0;
      }
      // 预核业务
      else if (BusinessClass === "RealEstatePreCheckSurveyFlow") {
        this.PreSurveyContentInfo.DataCheckID = checkId;
        this.PreSurveyContentInfo.DataCheckState = 0;
      }
      // 不动产预核业务即时办理
      else if (BusinessClass === "RealEstatePreCheckSurveyAutoFlow") {
        this.PreSurveyContentInfo.DataCheckID = checkId;
        this.PreSurveyContentInfo.DataCheckState = 0;
      }
      // 预测绘、实核业务
      else {
        this.ContentInfo.DataCheckID = checkId;
        this.ContentInfo.DataCheckState = 0;
      }

      this.getSurveyResultCheckState(checkId);
      scrollTo(0);
    },
    // 获取成果检查状态
    getSurveyResultCheckState(checkId) {
      if (this.resultTimer) {
        this.clearTimer(this.resultTimer);
      }

      this.surveyResultCheckFinished = false;

      const { BusinessClass } = this.BaseInfo;

      let GetSurveyResultCheckState =
        BusinessClass === "MarkPointSurveyFlow"
          ? PlanningPointApi.GetSurveyResultCheckState
          : CheckLineApi.GetSurveyResultCheckState;

      this.resultTimer = setInterval(() => {
        GetSurveyResultCheckState(checkId, BusinessClass)
          .then((res) => {
            const { Data, StateCode } = res;
            if (StateCode === 1) {
              if (Data !== 0) {
                this.clearTimer(this.resultTimer);
                this.surveyResultCheckFinished = true;

                setTimeout(() => {
                  // 拨地定桩
                  if (BusinessClass === "MarkPointSurveyFlow") {
                    this.ContentInfo.AchievementCheckID = checkId;
                    this.ContentInfo.AchievementCheckState = Data;
                  }
                  // 预核业务
                  else if (BusinessClass === "RealEstatePreCheckSurveyFlow") {
                    this.PreSurveyContentInfo.DataCheckID = checkId;
                    this.PreSurveyContentInfo.DataCheckState = 0;
                  }
                  //不动产预核业务即时办理
                  else if (BusinessClass === "RealEstatePreCheckSurveyAutoFlow") {
                    this.PreSurveyContentInfo.DataCheckID = checkId;
                    this.PreSurveyContentInfo.DataCheckState = 0;
                  }
                  // 预测绘、实核业务
                  else {
                    this.ContentInfo.DataCheckID = checkId;
                    this.ContentInfo.DataCheckState = Data;
                  }

                  // 检查通过
                  if (Data === 1 || Data < 0) {
                    this.$message.success("您上传的测绘成果已通过系统检查");
                    this.reload();
                  }
                  // 检查不通过
                  if (Data === 2) {
                    this.reload();
                    this.form3.Data = [];
                  }
                }, 900);
              }
            } else {
              console.log(res);
            }
          })
          .catch((err) => console.log(err));
      }, 10000);
    },
    // 获取成果检查报告
    downloadSurveyResultErrorReport(name) {
      let checkID = null;

      const { BusinessClass } = this.BaseInfo;

      // 拨地定桩
      if (BusinessClass === "MarkPointSurveyFlow") {
        checkID = this.ContentInfo.AchievementCheckID;
      }
      // 预核业务
      else if (BusinessClass === "RealEstatePreCheckSurveyFlow") {
        checkID = this.PreSurveyContentInfo.DataCheckID;
      }
      // 不动产预核业务即时办理
      else if (BusinessClass === "RealEstatePreCheckSurveyAutoFlow") {
        checkID = this.PreSurveyContentInfo.DataCheckID;
      }
      // 预测绘、实核业务     
      else {
        checkID = this.ContentInfo.DataCheckID;
      }

      this.downLoading = true;
      CheckLineApi.DownloadErrorReport(checkID)
        .then((res) => {
          const fileName = `${name}成果检查报告(${checkID}).docx`;
          downloadFileByStream(res, fileName);

          this.downLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.downLoading = false;
        });
    },
    // 确认测绘成果
    async pass(step) {
      const valid = await this.checkStepForm(step);
      if (!valid) return false;

      this.$confirm("确认通过吗？", "温馨提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      })
        .then(async () => {
          this.passBtn.loading = true;
          Api.ConfirmSurveyResult(this.businessID, this.CurrentAction.ID)
            .then((res) => {
              const { StateCode, Data, Message } = res;

              if (StateCode === 1) {
                this.$message.success("成果确认成功");
                this.refreshStep(Data);
              } else {
                this.$message.error(Message);
              }
              this.passBtn.loading = false;
            })
            .catch((err) => {
              console.log(err);
              this.passBtn.loading = false;
            });
          scrollTo(0);
        })
        .catch((err) => console.log(err));
    },
    // 成果验收
    accept() {
      const { BusinessClass } = this.BaseInfo;
      const ActionId = this.CurrentAction.ID;
      let confirmMsg = "确认验收完成";
      let successMsg = "成果验收完成";
      //不动产预核业务即时办理/不动产实核业务
      if (BusinessClass === "RealEstatePreCheckSurveyAutoFlow" || BusinessClass === "RealEstateActualSurveyFlow") {
        confirmMsg = "确认提交备案";
        successMsg = "提交备案成功";
      }
      if (BusinessClass === "MeasurePutLinePreCheckFlow") {
        confirmMsg = "确认提交验线";
        successMsg = "提交验线成功";
      }
      this.$confirm(`${confirmMsg}？`, "温馨提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      })
        .then(async () => {
          this.acceptBtn.loading = true;
          Api.AcceptSurveyProjectResult(this.businessID,ActionId)
            .then((res) => {
              const { StateCode, Data, Message } = res;
              if (StateCode === 1) {
                this.$message.success(successMsg);
                this.refreshStep(Data);
              } else {
                this.$message.error(Message);
              }
              this.acceptBtn.loading = false;
            })
            .catch((err) => {
              console.log(err);
              this.acceptBtn.loading = false;
            });
          scrollTo(0);
        })
        .catch((err) => console.log(err));
    },
  },
};
