<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="`${type === 1 ? '变更' : '添加'}${title}`"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    width="750px"
    @close="close"
    class="project-licence-get-container"
  >
    <div>
      <tip v-if="submitLoading" class="mb-20"
        >温馨提示：系统获取信息时间大概需要5~30秒，请耐心等待。</tip
      >
      <tip v-else type="default" class="mb-20">
        <p class="mt-0">
          请输入工程规划许可证号或者项目编号（附图编号），点击"确认{{
            type === 1 ? "变更" : "添加"
          }}"按钮后，系统将自动读取对应的工程规划许可证信息进行{{
            type === 1 ? "变更" : "添加"
          }}。
        </p>
        <p class="mb-0">工程规划许可证号示例：450101202050579、4501052024GG0218437</p>
        <p class="mb-0" v-if="businessClass==='CouncilMeasurePutLinePreCheckFlow'|| businessClass==='CouncilPlanCheckFlow'">工程规划许可证项目编号（附图编号）示例：SGH0101201800000</p>
        <p class="mb-0" v-else>工程规划许可证项目编号（附图编号）示例：GC0113202000492</p>
      </tip>
      <el-form ref="form" :model="form" :rules="rules" label-width="0">
        <el-form-item prop="Code">
          <el-input
            v-model.trim="form.Code"
            placeholder="请输入工程规划许可证号或工程规划许可证项目编号（附图编号）"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submit('form')"
        >确认{{ type === 1 ? "变更" : "添加" }}</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// 验证
import { validProjectLicencee,validAgrLicencee } from "utils/validate";
// Api
import Api from "api/business/index.js";

export default {
  name: "ProjectLicenceGetDialog",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 类型 1 变更 0 新增
    type: {
      type: Number,
      default: 0,
    },
    // 业务id
    businessId: {
      type: String,
      default: null,
    },
    // 业务逻辑类
    businessClass: {
      type: String,
      default: null,
    },
    // 记录列表
    list: {
      type: Array,
      default: () => [],
    },
    // 标题
    title: {
      type: String,
      default: "工程规划许可证",
    },
    // 工规证类型
    licenceType: {
      type: String,
      default: "放线",
    },
  },
  data() {
    const validateCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入工程规划许可证号或工程规划许可证项目编号（附图编号）"));
      }
      // else if (!validProjectLicencee(value)) {
      //   callback(new Error("工程规划许可证号格式错误"));
      // }
      else {
        callback();
      }
    };

    return {
      form: {
        Code: null,
      },
      rules: {
        Code: [
          {
            required: true,
            validator: validateCode,
            trigger: "blur",
          },
        ],
      },
      showHint: true,
      submitLoading: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init("工程规划许可证");
      }
    },
  },
  methods: {
    // 初始化
    init(name) {
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    // 重置表单
    reset() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // 关闭存储弹窗
    close() {
      this.reset();
      this.$emit("cancel");
      this.$emit("update:visible", false);
    },
    // 提交表单
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { form, list, type, businessId, businessClass } = this;

          const isExist = list.find(
            (e) => e.Code === form.Code || e.AppendixImgNumber === form.Code
          );
          if (isExist && type === 0) {
            this.$message.warning("该工程规划许可证已存在");
            return false;
          }

          this.submitLoading = true;

          Api.GetProjectPlanPermissionInfo(form.Code, businessId, businessClass)
            .then((res) => {
              const { StateCode, Data, Message } = res;
              if (StateCode === 1) {
                this.$message.success("工程规划许可证信息获取成功");
                this.$emit("submit", Data);
                this.$emit("update:visible", false);
              }
              // 查不到信息
              else if (StateCode === 2) {
                // console.log(businessClass);
                let msg = "根据许可证号未能获取到工程规划许可信息，您可自行添加工程规划许可证信息，或输入新的证号重新获取";
                if (businessClass === "RealEstatePreCheckSurveyAutoFlow") {//不动产预核业务即时办理
                  if(validAgrLicencee(form.Code)){//农垦的工程规划许可证证号
                     msg = "该《建设工程规划许可证》由广西壮族自治区农垦局核发，请自行添加，或输入新的证号重新获取";
                  }else{
                     this.$message.warning(Message);
                     this.submitLoading = false;
                     return;
                  }
                }
                this.$confirm(
                  msg,
                  "温馨提示",
                  {
                    confirmButtonText: "自行添加",
                    cancelButtonText: "重新获取",
                  }
                )
                  .then((res) => {
                    console.log(this.licenceType);
                    this.$emit("add-lience", this.licenceType);
                    this.$emit("update:visible", false);
                  })
                  .catch((err) => {
                    this.form.Code = null;
                  });
              } else if (StateCode === 3) {
                // console.log(businessClass);
                let msg = Message;
                this.$alert(
                  msg,
                  "温馨提示",
                  {
                    confirmButtonText: "确定"
                  }
                );
              } else {
                this.$message.warning(Message);
              }
              this.submitLoading = false;
            })
            .catch((err) => {
              console.log(err);
              this.submitLoading = false;
              this.$message.error("服务器繁忙，请稍后重试");
            });
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-bottom: 10px;
  padding-top: 10px;
}
</style>
