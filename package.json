{"name": "sdc-pc-web", "version": "1.0.0", "description": "南宁市多测合一信息化管理", "author": "xLong <<EMAIL>>", "private": true, "scripts": {"dev": "vue-cli-service serve", "build:practice": "vue-cli-service build", "build:practice_and_publish": "vue-cli-service build && call C:/codes/practice/SDCPCWeb_web/publish_dist.bat", "build:release_and_publish": "vue-cli-service build --mode release && call C:/codes/release/SDCPCWeb_web/publish_dist.bat", "build:stage": "vue-cli-service build --mode staging", "build:release": "vue-cli-service build --mode release", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@types/proj4": "^2.5.0", "axios": "0.18.1", "element-ui": "2.13.2", "js-cookie": "2.2.0", "node-sass": "^4.14.1", "ol": "^6.4.3", "path-to-regexp": "2.4.0", "proj4": "^2.6.2", "vue": "2.6.10", "vue-router": "3.0.2", "vuex": "3.1.0", "webpack": "^4.43.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.5.3", "@vue/cli-plugin-eslint": "^3.12.1", "@vue/cli-plugin-unit-jest": "3.5.3", "@vue/cli-service": "3.5.3", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.7.6", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-polyfill": "^6.26.0", "chalk": "2.4.2", "chokidar": "2.1.5", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "1.3.1", "lint-staged": "8.1.5", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}