<template>
  <!-- eslint-disable -->
  <div class="list-upload-container">
    <!-- 图片列表 -->
    <div class="list-upload">
      <file-list
        :file-list="fileList"
        :can-delete="canDelete"
        :can-download="canDownload"
        :limit="limit"
        @preview="preview"
        @delete="del"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <el-upload
        v-if="!disabled && fileList.length < limit"
        ref="fileUpload"
        multiple
        :action="action"
        :headers="headers"
        :data="data"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
      >
        <div slot="default" class="list-upload-add-btn">
          <i class="el-icon-plus"></i>上传文件
        </div>
        <div slot="file" slot-scope="{file}">
          <div class="file-upload-text" v-if="file.status === 'ready'">准备上传...</div>
          <div class="file-upload-text" v-if="file.status === 'fail'">上传失败</div>
          <div v-if="file.status && file.status === 'uploading'" style="position:relative">
            <!-- 原本el组件的名称 -->
            <div>
              <div class="file-name">{{ file.name }}</div>
              <i class="el-icon-close" @click="cancelUpload(file)"></i>
            </div>
            <el-progress
              class="mt-10"
              :stroke-width="2"
              :percentage="parsePercentage(file.percentage)"
            ></el-progress>
          </div>
        </div>
        <div slot="tip">
          <!-- 提示 -->
          <div class="list-upload__tip">
            <span v-if="fileFormat">文件格式：{{ fileFormat }}，</span>
            <template v-if="showFileSize">
              文件大小：
              <span>{{ getSize(fileSize) }}</span>以内
            </template>
          </div>
        </div>
      </el-upload>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
// mixins
import AttachmentUpload from "mixins/attachment/upload.js";
// 组件
import FileList from "components/common/FileList/index.vue";

function noop() {
  return true;
}

export default {
  name: "ListUpload",
  mixins: [AttachmentUpload],
  components: { FileList },
  props: {
    // 所有文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 是否显示文件大小提示文本
    showFileSize: {
      type: Boolean,
      default: true
    },
    // 文件大小限制，单位为KB
    fileSize: {
      type: Number,
      default: 150
    },
    // 上传要附带的额外参数
    data: {
      type: Object,
      default: () => {}
    },
    // 文件格式
    fileFormat: {
      type: String,
      default: ""
    },
    // 检查上传文件格式
    onCheckFormat: {
      type: Function,
      default: noop
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 最大上传个数
    limit: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      canDelete: true,
      canDownload: true
    };
  },
  watch: {
    disabled(val) {
      this.canDelete = val ? false : true;
    }
  },
  methods: {
    // 上传成功
    uploadSuccess(res, file, fileList) {
      const { StateCode, Data, Message } = res;
      if (StateCode === 1) {
        let list = [];
        if (this.limit <= 1) {
          list = [{ ...Data }];
        } else {
          list = [...this.fileList, Data];
        }

        this.$message.success("上传成功");

        this.$emit("upload-success", list);
      } else {
        this.$message.error(Message);
        return false;
      }
    },
    downloadStart(file) {
      this.$emit("download-start", file);
    },
    downloadEnd(file) {
      this.$emit("download-end", file);
    },
    downloadFail(file) {
      this.$emit("download-fail", file);
    },
    preview(file) {
      this.$emit("preview", file);
    },
    del({ file, index, list }) {
      this.$emit("delete", { file, index, list });
    }
  }
};
</script>
<style scoped lang="scss">
.list-upload {
  /deep/ .el-upload {
    width: 100%;
  }

  /deep/ .el-upload-list__item {
    position: relative;
    transition: none !important;
    line-height: 25px !important;
    padding: 5px 10px;
    outline: none;

    &.is-success {
      display: none;
    }
  }

  /deep/ .el-progress {
    // width: 98% !important;
    left: 0;
    top: 8px;
  }

  &__tip {
    font-size: 12px;
    line-height: 16px;
    margin: 10px 0;
    color: #f0ad4e;
  }

  &-add-btn {
    border: 1px dashed #dfe6ec;
    width: 100%;
    // border-radius: 4px;
    text-align: center;
    height: 45px;
    line-height: 45px;
    cursor: pointer;
    position: relative;
    z-index: 10;
    color: $color-primary;
    margin-top: -1px;

    &:hover {
      border-color: $color-primary;
    }

    & > i {
      margin-right: 5px;
    }
  }
}
</style>

