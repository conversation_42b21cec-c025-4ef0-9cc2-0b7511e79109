<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :width="width"
      class="create-business-dialog"
      @close="close"
      :custom-class="className"
      center
    >
      <!-- 不动产预核业务（即时办结）-承诺书 -->
      <ol
        class="promise-list"
        v-if="isPromise && businessClass === 'RealEstatePreCheckSurveyAutoFlow'"
      >
        <span class="promise-title"
          >为维护不动产登记秩序，保证不动产登记结果的真实性、合法性和有效性，本公司（单位）承诺：</span
        >
        <li class="promise-list-item">
          该项目（单体）的《建设工程设计方案》已通过自然资源部门审批，并已取得自然资源部门核发的《建设工程规划许可证》及附件；
        </li>
        <li class="promise-list-item">
          该项目（单体）的建筑设计图纸将按照《建设工程设计方案》内容进行审图备案；
        </li>
        <li class="promise-list-item">
          将严格按照自然资源部门审批的《建设工程设计方案》、《建设工程规划许可证》及附件和其他批准文件等相关规划内容进行建设；
        </li>
        <li class="promise-list-item">
          已对测绘单位汇交的该项目（单体）不动产预核测绘成果进行验收，测绘单位按照相关规范对自然资源部门核发的《建设工程设计方案》内容进行测绘和面积测算；
        </li>
        <li class="promise-list-item">提供的材料与审批内容一致；</li>
        <li class="tip">
          若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a class="link" @click="openPdf(`不动产预核业务承诺书`)">点击此处</a
          >下载不动产预核业务承诺书</span
        >
      </ol>
      <!-- 不动产实核业务-承诺书 -->
      <ol
        class="promise-list"
        v-if="isPromise && businessClass === 'RealEstateActualSurveyFlow'"
      >
        <span class="promise-title"
          >为维护不动产登记秩序，保证不动产登记结果的真实性、合法性和有效性，本公司（单位）承诺：</span
        >
        <li class="promise-list-item">
          申请办理不动产实核的项目（单体）现场已满足《南宁市城市管理技术规定》4.1.4
          条规定要求，具有完整的使用功能，并能正常投入使用；
        </li>
        <li class="promise-list-item">
          提供的该项目（单体）《建筑设计方案文本》已通过自然资源部门审批；
        </li>
        <li class="promise-list-item">
          提供的该项目（单体）建筑施工图纸已通过具有相关资质的审图单位审核，并已在住建部门进行备案；
        </li>
        <li class="promise-list-item">
          提供的该项目（单体）建筑立面照片真实、完整反映项目实际情况；
        </li>
        <li class="promise-list-item">
          已对测绘单位汇交的该项目（单体）建筑不动产实核测绘成果进行验收；
        </li>
        <li class="tip">
          若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a class="link" @click="openPdf(`不动产实核业务承诺书`)">点击此处</a
          >下载不动产实核业务承诺书</span
        >
      </ol>
      <!-- 放线测量与规划验线（非房开企业）-承诺书 -->
      <ol
        class="promise-list"
        v-if="isPromise && businessClass === 'MeasurePutLinePreCheckFlow'"
      >
        <span class="promise-title"
          >为维护不动产登记秩序，保证不动产登记结果的真实性、合法性和有效性，本公司（单位）承诺：</span
        >
        <li class="promise-list-item">
          该项目（单体）的《建设工程设计方案》已通过自然资源部门审批，并已取得自然资源部门核发的《建设工程规划许可证》及附件；
        </li>
        <li class="promise-list-item">
          该项目（单体）的建筑设计图纸将按照《建设工程设计方案》内容进行审图备案；
        </li>
        <li class="promise-list-item">
          将严格按照自然资源部门审批的《建设工程设计方案》、《建设工程规划许可证》及附件和其他批准文件等相关规划内容进行建设；
        </li>
        <li class="promise-list-item">
          已按照建设工程规划许可证要求进行实地放线，并已对测绘
          单位汇交的该项目（单体）放线测量与规划验线成果进行验收；
        </li>
        <li class="promise-list-item">提供的材料与审批内容一致；</li>
        <li class="tip">
          若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a
            class="link"
            @click="openPdf(`放线测量与规划验线（非房开企业）承诺书`)"
            >点击此处</a
          >下载放线测量与规划验线（非房开企业）承诺书</span
        >
      </ol>
      <!-- 不动产预核业务--停止办理公告 -->
      <div v-if="!isPromise" style="height: 680px">
        <embed :src="src" style="width: 100%; height: 100%" />
      </div>
      <!-- 市政工程建设竣工规划核实流程-承诺书 -->
      <ol
        class="promise-list"
        v-if="isPromise && businessClass === 'CouncilPlanCheckFlow'"
      >
        <span class="promise-title"
          >为维护不动产登记秩序，保证不动产登记结果的真实性、合法性和有效性，本公司（单位）承诺：</span
        >
        <li class="promise-list-item">
          申请办理市政工程规划条件核实的项目现已满足《南宁
          市城市管理技术规定》4.1.4 条规定要求，具有完整的使用功能，
          并能正常投入使用；
        </li>
        <li class="promise-list-item">
          提供的该项目方案文本已通过自然资源部门审批；
        </li>
        <li class="promise-list-item">
          提供的该项目施工图纸已通过具有相关资质的审图单位
          审核，并已在住建部门进行备案；
        </li>
        <li class="promise-list-item">
          提供的该项目建筑立面照片真实、完整反映项目实际情况；
        </li>
        <li class="promise-list-item">
          已对测绘单位汇交的该项目核实测绘成果进行验收；
        </li>
        <li class="tip">
          若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a class="link" @click="openPdf(`市政工程建设竣工规划核实流程承诺书`)"
            >点击此处</a
          >下载市政工程建设竣工规划核实业务承诺书</span
        >
      </ol>
      <!-- 放线测量与规划验线（市政工程）--承诺书 -->
      <ol
        class="promise-list"
        v-if="
          isPromise && businessClass === 'CouncilMeasurePutLinePreCheckFlow'
        "
      >
        <span class="promise-title"
          >为维护不动产登记秩序，保证不动产登记结果的真实性、合法性和有效性，本公司（单位）承诺：</span
        >
        <li class="promise-list-item">
          该项目的《市政工程设计方案》已通过自然资源部门审批，
          并已取得自然资源部门核发的《市政工程规划许可证》及附件；
        </li>
        <li class="promise-list-item">
          该项目的设计图纸将按照《市政工程设计方案》内容进行审图备案；
        </li>
        <li class="promise-list-item">
          将严格按照自然资源部门审批的《市政工程设计方案》、《市政工程规划许可证》及附件和其他批准文件等相关规划内容进行建设；
        </li>
        <li class="promise-list-item">
          已按照《市政工程规划许可证》要求进行实地放线，并已对测绘单位汇交的该项目放线测量与规划验线成果进行验收；
        </li>
        <li class="promise-list-item">提供的材料与审批内容一致；</li>
        <li class="tip">
          若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a
            class="link"
            @click="openPdf(`放线测量与规划验线（市政工程）承诺书`)"
            >点击此处</a
          >下载放线测量与规划验线（市政工程）承诺书</span
        >
      </ol>
      <!-- 规划条件全面核实业务-承诺书 -->
      <ol
        class="promise-list"
        v-if="isPromise && businessClass === 'RealEstateOverallActualSurveyFlow'"
      >
        <span class="promise-title"
          >为维护不动产登记秩序，保证不动产登记结果的真实性、合法性和有效性，本公司（单位）承诺：</span
        >
        <li class="promise-list-item">
          申请办理竣工规划条件全面核实的项目现场已满足《南宁市城市管理技术规定》相关规定要求，
          具有完整的使用功能，并能正常投入使用。
        </li>
        <li class="promise-list-item">
          提供的该项目《建筑设计方案文本》及总平面图已通过规划审批部门审批。
        </li>
        <li class="promise-list-item">
          提供的该项目建筑施工图纸已通过具有相关资质的审图单位审核，并已在住建部门进行备案。
        </li>
        <li class="promise-list-item">
          提供的该项目建筑现场照片真实、完整反映项目实际情况。
        </li>
        <li class="promise-list-item">
          已对测绘单位汇交的该项目全面核实成果进行验收。
        </li>
        <li class="tip">
          若未履行以上承诺，由本公司（单位）承担相关法律责任及后果。
        </li>
        <span class="download-line"
          >您可以
          <a class="link" @click="openPdf(`规划条件全面核实业务承诺书`)">点击此处</a
          >下载规划条件全面核实业务承诺书</span
        >
      </ol>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm()"
          :loading="saveLoading"
          :disabled="confirmBtn.disabled"
          >{{ countDownBtn }}{{ this.confirmBtn.text }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";
const urlPrefix = process.env.VUE_APP_URL_PREFIX;
export default {
  name: "CountDownDialog",
  computed: {
    ...mapGetters(["userName", "personName"]),
    // 显示示例
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    countDownType: {
      type: String,
      default: null,
    },
    countDownBtn: {
      type: String,
      default: null,
    },
    countDownTime: {
      type: Number,
      default: 15,
    },
    title: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "",
    },
    className: {
      type: String,
      default: "",
    },
    businessClass: {
      type: String,
      default: "",
    },
    isPromise: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      confirmBtn: {
        disabled: true,
        time: 15,
        text: "",
        timer: null,
      },
      // loading
      saveLoading: false,
      src: `${urlPrefix}/static/pdf/关于南宁市不动产登记综合服务平台开通不动产预核业务即时办结功能的公告.pdf`,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  destroyed() {
    this.clearTimer(this.confirmBtn.timer);
  },
  methods: {
    // 初始化
    init() {
      this.countDown();
      const { business } = this;
    },
    //倒计时方法
    countDown() {
      this.confirmBtn.time = this.countDownTime;
      this.confirmBtn.disabled = true;
      const timeCount = this.countDownTime;
      this.confirmBtn.text = `（${timeCount}s）`;
      if (!this.confirmBtn.timer) {
        this.confirmBtn.timer = setInterval(() => {
          if (this.confirmBtn.time > 1 && this.confirmBtn.time <= timeCount) {
            this.confirmBtn.disabled = true;
            this.confirmBtn.time--;
            this.confirmBtn.text = `（${this.confirmBtn.time}s）`;
          } else {
            this.confirmBtn.disabled = false;
            // clearInterval(this.confirmBtn.timer);
            this.confirmBtn.text = ``;
            // this.confirmBtn.timer = null;
            this.clearTimer();
          }
        }, 1000);
      }
    },
    // 清除定时器
    clearTimer() {
      if (this.confirmBtn.timer) {
        clearInterval(this.confirmBtn.timer);
        this.confirmBtn.timer = null;
      }
    },
    // 打开操作指南
    openPdf(name) {
      window.open(`${urlPrefix}/static/pdf/${name}.pdf`, "_blank");
    },

    // 关闭存储弹窗
    close() {
      this.clearTimer();
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm() {
      //RealEstateActualSurveyFlow
      // if(this.businessClass === 'RealEstatePreCheckSurveyAutoFlow'){
      this.$emit("submit", true);
      // }
      this.close();
    },
  },
};
</script>

<style lang="scss" scoped>
.create-business-dialog {
  /deep/ .el-dialog__body {
    padding-bottom: 5px;
    padding-top: 0;
  }
  // /deep/ .el-dialog__title {
  //   font-weight: bold;
  //   letter-spacing: 2px;
  //   font-size: 20px;
  // }
  .promise-title {
    margin-left: -15px;
    font-size: 16px;
    padding: 5px 0;
    display: inline-block;
    color: #303133;
    text-indent: 2em;
  }
  .promise-list-item {
    list-style-type: decimal;
    padding: 5px 0;
    font-size: 16px;
    color: #303133;
  }
  .download-line {
    font-size: 14px;
    display: inline-block;
    padding-top: 5px;
    margin-left: -17.8px;
    .link {
      color: $color-primary;
      text-decoration: underline;
      cursor: pointer;
      &:hover {
        color: #09f;
      }
    }
  }
  .tip {
    font-size: 16px;
    margin-left: -17.8px;
    color: #303133;
    padding: 5px 0;
  }
}
/deep/.notice .el-dialog__footer {
  text-align: right !important;
}
// /deep/.notice .el-dialog__header {
//   display: flex;
// }
/deep/ .notice .el-dialog__title {
  // font-weight: bold;
  letter-spacing: 1px;
  font-size: 20px;
}
/deep/ .promise .el-dialog__title {
  font-weight: bold;
  letter-spacing: 2px;
  font-size: 20px;
}
</style>
