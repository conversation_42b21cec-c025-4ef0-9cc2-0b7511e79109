<template>
  <!-- eslint-disable -->
  <div>
    <!-- 解决滚动页面因改变布局抖动原因，需保留2个 -->
    <div class="xcloud-step-nav-list-container">
      <ol ref="stepList1" class="xcloud-step-nav-list">
        <li
          v-for="item in flowStepsInfo"
          :style="{ 'width': stepWidth}"
          class="xcloud-step-nav-list__item"
          :class="{ 'finished': item.IsFinished, 'active': item.IsCurrent && !item.IsFinished }"
        >
          <span>{{ item.DisplayName }}</span>
        </li>
      </ol>
    </div>

    <div v-if="fixed" class="xcloud-step-nav-list-container fixed">
      <ol ref="stepList2" class="xcloud-step-nav-list">
        <li
          v-for="item in flowStepsInfo"
          :style="{ 'width': stepWidth}"
          class="xcloud-step-nav-list__item"
          :class="{ 'finished': item.IsFinished, 'active': item.IsCurrent && !item.IsFinished }"
        >
          <span>{{ item.DisplayName }}</span>
        </li>
      </ol>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
// mixins
import ResizeMixin from "mixins/resize.js";

export default {
  name: "FlowStepsInfo",
  mixins: [ResizeMixin],
  props: {
    //步骤列表
    flowStepsInfo: {
      type: Array,
      default: []
    },
    // 固定
    fixed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      stepWidth: "100px"
    };
  },
  watch: {
    flowStepsInfo(val) {
      this.$_resizeHandler();
    }
  },
  mounted() {
    this.$_resizeHandler();
  },
  methods: {
    $_resizeHandler() {
      this.$nextTick(() => {
        let listW = 1170;
        if (this.$refs.stepList1 || this.$refs.stepList2) {
          const parentNode =
            this.$refs.stepList1.parentNode || this.$refs.stepList2.parentNode;
          listW =
            parentNode.offsetWidth >= 1170 ? 1170 : parentNode.offsetWidth;
        } else {
          listW = 1170;
        }

        this.stepWidth = listW / this.flowStepsInfo.length - 3 + "px";
      });
    }
  }
};
</script>
<style scoped lang="scss">
.xcloud-step-nav-list {
  margin: 0;
  padding: 0;
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
  max-width: 1170px;

  &-container {
    top: 75px;
    &.fixed {
      position: fixed;
      display: flex;
      justify-content: center;
      background: #fff;
      padding: 10px 10px 0 10px;
      width: 100%;
      left: 0;
      top: 132px;
      z-index: 1001; // 覆盖loading
      border-bottom: #eee 1px solid;
      box-shadow: rgba(0, 0, 0, 0.05) 0 0 8px;
      transition: all 0.2s;

      .step-nav-list__item{
        &::before{
          border-left: 20px solid #fff;
        }
      }
    }
  }

  &__item {
    padding: 0px 10px 0px 30px;
    // min-width: 220px;
    line-height: 40px;
    background: #aeadad;
    display: inline-block;
    color: #fff;
    position: relative;
    margin-left: 3px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:first-child {
      border-radius: 4px 0 0 4px;
      padding-left: 25px;
      margin-left: 0;

      &::before {
        display: none;
      }
    }

    &:last-child {
      border-radius: 0px 4px 4px 0px;
      padding-right: 25px;

      &::after {
        display: none;
      }
    }

    &::before {
      content: "";
      display: block;
      border-top: 20px solid #aeadad;
      border-bottom: 20px solid #aeadad;
      border-left: 20px solid #f4f5f7;
      position: absolute;
      left: 0px;
      top: 50%;
      margin-top: -20px;
    }

    &::after {
      content: "";
      display: block;
      border-top: 20px solid transparent;
      border-bottom: 20px solid transparent;
      border-left: 20px solid #aeadad;
      position: absolute;
      right: -20px;
      top: 0;
      top: 50%;
      margin-top: -20px;
      z-index: 1;
    }

    &.finished {
      background: #65b24e;

      &::before {
        border-top-color: #65b24e;
        border-bottom-color: #65b24e;
      }

      &::after {
        border-left-color: #65b24e;
      }
    }

    &.active {
      background: $color-primary;

      &::before {
        border-top-color: $color-primary;
        border-bottom-color: $color-primary;
      }

      &::after {
        border-left-color: $color-primary;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .xcloud-step-nav-list {
    &-container {
      &.fixed {
        top: 112px;
      }
    }
  }
}
</style>
