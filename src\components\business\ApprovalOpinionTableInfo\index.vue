<template>
  <!-- eslint-disable -->
  <el-table ref="table" class="list-approvalopinion-table" :data="approvalOpinionTableInfo" border>

    <el-table-column prop="csyj" label="初审意见" header-align="center" style="width: 50%">
      <template slot-scope="{ row }">{{
        row.CSYJ        
      }}</template>
    </el-table-column>

    <template v-if="businessClass === 'RealEstateActualSurveyFlow' || businessClass === 'CouncilPlanCheckFlow'">
      <el-table-column prop="wyyj" label="外业意见" header-align="center" style="width: 50%">
        <template slot-scope="{ row }">{{
          row.SHYJ
        }}</template>
      </el-table-column>
    </template>

  </el-table>
</template>

<script>
/* eslint-disable */

export default {  
  name: "ApprovalOpinionTableInfo",
  props: {    
    //初审意见和外业意见
    approvalOpinionTableInfo: {
      type: Array,
      default: () => [],
    },   
    //流程类型
    businessClass: {
      type: String,
      default: null,
    },
  },    
};
</script>
<style scoped lang="scss">
.list-approvalopinion-table {
  line-height: 1 !important;
}
</style>
