
/* eslint-disable */
import request from 'utils/request';

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
	// 获取正在进行的单位信息变更申请
	GetSurveyCompanyInfoModify: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/SurveyCompanyInfoModify/GetRequesting`,
			method: 'get'
		})
	},
	// 创建单位信息变更申请
	ModifyRequest: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/SurveyCompanyInfoModify/CreateModifyRequest`,
			method: 'post',
			data
		})
	},
	// 单位信息变更记录列表
	SurveyCompanyInfoModifyList: (pageIndex, pageSize) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/SurveyCompanyInfoModify/GetRequestList?pageIndex=${pageIndex}&pageSize=${pageSize}`,
			method: 'get',
		})
	},
	// 获取单位信息变更详情
	GetModifyInfo: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/SurveyCompanyInfoModify/GetModifyRequest?id=${id}`,
			method: 'get'
		})
	},
}
