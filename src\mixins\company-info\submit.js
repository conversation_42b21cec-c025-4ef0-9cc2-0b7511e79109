/*
 * 模块 : 单位信息-保存和提交相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-06-22
 * 版本 : version 1.0
 */
/* eslint-disable */

// Api
import Api from "api/company-info/index.js";
// 工具
import { objOmit } from "utils/index.js";
import { validForm } from "utils/form.js";

export default {
  data() {
    return {
      // loading
      saveLoading: false,
      submitLoading: false,
      // 禁止编辑
      disableEdit: true,
      // 保存提交失败前的数据
      tempData: {},
    };
  },
  methods: {
    // 校验tab
    async check() {
      let CompanyBaseInfo = null;
      if (this.$refs.base) {
        const baseInfoForm = this.$refs.base[0].$refs.baseInfoForm;
        const valid = await validForm(
          baseInfoForm,
          "基本信息填写有误，请检查"
        );
        if (!valid) {
          this.activeName = "base";
          return false;
        } else {
          CompanyBaseInfo = baseInfoForm.model;
        }
      }

      const CompanyEmployees = this.$refs.member[0].$refs.memberTable.tableData;

      if (CompanyEmployees && CompanyEmployees.length <= 0) {
        this.$message.error("人员信息至少录入一条记录");
        this.activeName = "member";
        return false;
      }

      // 测绘单位
      if (this.companyType === "测绘单位") {
        let CompanyQualification = null;
        if (this.$refs.qualifications) {
          const qualificationsForm = this.$refs.qualifications[0].$refs
            .qualificationsForm;
          const valid = await validForm(
            qualificationsForm,
            "测绘资质信息填写有误，请检查"
          );
          if (!valid) {
            this.activeName = "qualifications";
            return false;
          } else {
            CompanyQualification = JSON.parse(
              JSON.stringify(qualificationsForm.model)
            );
          }
        }

        /* 检测当前办理人信息是否完整（因为当前办理人是自行添加的）-start */
        const { personNo, personName } = this;

        let valid = true;
        let hasAdmin = false;
        let hasRegisteredSurveyor = false;

        CompanyEmployees.forEach(e => {
          if (e.PersonRole === "单位管理员") {
            hasAdmin = true;
          }
          if (e.PersonRole === "注册测绘师") {
            hasRegisteredSurveyor = true;
          }
          if (e.PersonNumber === personNo) {

            if(e.PersonName !== personName){
              this.$message.error(`人员信息缺少当前申请人，请添加后再提交`);
              valid = false;
            }
            if (!e.PersonName) {
              this.$message.error(`${personName}的姓名未填写，请编辑完善后再提交`);
              valid = false;
            }
            else if (!e.PersonPhone) {
              this.$message.error(`${personName}的手机号码未填写，请编辑完善后再提交`);
              valid = false;
            }
            else if (!e.PersonRole) {
              this.$message.error(`${personName}的角色未填写，请编辑完善后再提交`);
              valid = false;
            }
          }
        });
        if (!valid) {
          this.activeName = "member";
          return false;
        }

        if (!hasAdmin) {
          this.$message.error(`人员信息中缺少单位管理员，请录入后再提交`);
          this.activeName = "member";
          return false;
        }
        if (!hasRegisteredSurveyor) {
          this.$message.error(`人员信息中缺少注册测绘师，请录入后再提交`);
          this.activeName = "member";
          return false;
        }
        /* 检测当前办理人信息是否完整（因为当前办理人是自行添加的）-end */

        this.data = {
          CompanyBaseInfo,
          CompanyQualification,
          CompanyEmployees,
        };
      }
      // 建设单位
      else {
        this.data = {
          CompanyBaseInfo,
          CompanyEmployees,
        };
      }

      this.$confirm("确认提交单位信息？", "温馨提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      }).then(() => {
        this.submit(this.data);
      }).catch((err) => console.log(err));
    },
    // 保存
    save() {
      let data = null;

      const baseInfoForm = this.$refs.base[0].$refs.baseInfoForm;
      const CompanyBaseInfo = baseInfoForm.model;

      const CompanyEmployees = this.$refs.member[0].$refs.memberTable.tableData;

      // 测绘单位
      if (this.companyType === "测绘单位") {
        const qualificationsForm = this.$refs.qualifications[0].$refs.qualificationsForm;
        let CompanyQualification = JSON.parse(
          JSON.stringify(qualificationsForm.model)
        );

        data = {
          CompanyBaseInfo,
          CompanyQualification,
          CompanyEmployees
        };
      }
      // 建设单位
      else {
        data = {
          CompanyBaseInfo,
          CompanyEmployees,
        };
      }

      this.saveLoading = true;
      this.saveCompanyInfo(data);
    },
    // 保存单位信息
    saveCompanyInfo(data, showSaveMsg = true) {
      data = this.handleData(data);
      const DetailInfo = JSON.stringify(data);

      return new Promise((resolve, reject) => {
        Api.SaveCompanyInfo({
          ID: this.companyID,
          CompanyName: data.CompanyBaseInfo.CompanyName,
          CompanyType: data.CompanyBaseInfo.CompanyType,
          CompanyNo: data.CompanyBaseInfo.CreditCode,
          DetailInfo,
        }).then(async res => {
          if (res.StateCode === 1) {
            if (showSaveMsg) {
              this.$message.success("保存成功");
            }

            // 修改单位名称
            await this.$store.dispatch("user/setCompanyName", data.CompanyBaseInfo.CompanyName);

            resolve(true);
          } else {
            this.$message.error(res.Message);
            this.submitLoading = false;
            resolve(false);
          }
          this.saveLoading = false;
        }).catch((err) => {
          console.log(err);
          // this.$message.error("服务器繁忙，请稍后重试");
          this.saveLoading = false;
          this.submitLoading = false;
          resolve(false);
        });
      });
    },
    // 处理要保存和提交的数据
    handleData(data) {
      let { CompanyBaseInfo, CompanyEmployees } = data;

      // 测绘单位
      if (this.companyType === "测绘单位") {
        let { CompanyQualification } = data;

        // 应后端接口需求特殊处理AttachmentInfo字段
        let qualification = { ...CompanyQualification }; // 解决vue"Invalid prop: type check failed for prop "data". Expected Array, got String with value"报错问题
        const { QualificateCertificate, ViceQualificateCertificate, BusinessRange,
        } = qualification;
        qualification.AttachmentInfo = JSON.stringify({
          QualificateCertificate,
          ViceQualificateCertificate
        });
        qualification = objOmit(qualification, ["QualificateCertificate", "BusinessRangeNames", "BusinessRangeTree", "BusinessRangeJSON"]);
        // 应后端接口需求特殊处理BusinessRange字段，改成json字符串
        // qualification.BusinessRange = JSON.stringify(BusinessRange);

        /**
         * 2021-02-25 新需求修改
         * 取最后一级的businessClass组成数组后转成json字符串提交
         */
        let newBusinessRange = [];
        BusinessRange.forEach(e => {
          newBusinessRange.push(e.pop());
        })
        qualification.BusinessRange = JSON.stringify(newBusinessRange);
        /* 2021-02-25 新需求修改 */

        return {
          CompanyBaseInfo,
          CompanyQualification: qualification,
          CompanyEmployees
        };
      }
      // 建设单位
      else {
        return {
          CompanyBaseInfo,
          CompanyEmployees,
        };
      }
    },
    // 提交表单
    async submit(data) {
      this.submitLoading = true;

      const isSaved = await this.saveCompanyInfo(data, false);
      if (isSaved) {
        // 提交审核
        Api.SubmitCompany(this.companyID).then(async res => {
          if (res.StateCode === 1) {
            this.$message.success("已提交审核，请耐心等待结果");

            // 获取可通过的路由
            await this.$store.dispatch("permission/generateRoutes", {
              roles: ["1"],
            });

            this.init();
          } else {
            this.$message.error(res.Message);
            // 重新获取信息
            this.getRegisterInfo();
          }
          this.submitLoading = false;
        }).catch(err => {
          console.log(err);
          this.submitLoading = false;
          this.$message.error("服务器繁忙，请稍后重试");
        });
      }
    },
    // 撤销申请
    remove() {
      this.$confirm("确认撤销该申请？", "温馨提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      }).then(() => {
        Api.CloseCompanyAudit({
          ID: this.companyID,
          ResponseMessage: "",
        })
          .then(res => {
            if (res.StateCode === 1) {
              this.$message.success("撤销成功");
              // 修改单位信息
              this.$store.dispatch("user/setCompany", {
                stateCode: 0,
                companyType: null,
                companyName: null,
                companyID: null,
              });

              // 撤销后返回用户中心
              this.$router.push({ name: "UserCenter" });
            } else {
              this.$message.error(res.Message);
            }
          }).catch(err => {
            console.log(err);
            // this.$message.error("服务器繁忙，请稍后重试");
          });
      }).catch(err => console.log(err));
    },
  },
};
