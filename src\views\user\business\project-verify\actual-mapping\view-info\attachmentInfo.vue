<template>
  <el-form ref="form" :model="form" label-width="140px">
    <el-form-item label-width="160px" label="工程规划许可证件：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.ProjectLicenceImg"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '工程规划许可证件',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ProjectLicenceImg')"
        @delete="del($event, 'form', 'ProjectLicenceImg')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ProjectLicenceImg"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item
      label-width="160px"
      label="不动产权证书："
    >
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.PropertyCertificate"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '不动产权证书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PropertyCertificate')"
        @delete="del($event, 'form', 'PropertyCertificate')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PropertyCertificate"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item
      label-width="160px"
      label="土地出让合同/土地划拨决定书："
    >
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.LandContract"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '土地出让合同/土地划拨决定书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'LandContract')"
        @delete="del($event, 'form', 'LandContract')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.LandContract"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="门牌证明：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.DoorPlate"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '门牌证明',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'DoorPlate')"
        @delete="del($event, 'form', 'DoorPlate')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.DoorPlate"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="总平面图：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="dwg"
        :file-list="form.SitePlan"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '总平面图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'SitePlan')"
        @delete="del($event, 'form', 'SitePlan')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.SitePlan"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="建筑定位图：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="dwg"
        :file-list="form.BuildingLocationMap"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '建筑定位图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'BuildingLocationMap')"
        @delete="del($event, 'form', 'BuildingLocationMap')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.BuildingLocationMap"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="效果图：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.EffectPicture"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '效果图',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'EffectPicture')"
        @delete="del($event, 'form', 'EffectPicture')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.EffectPicture"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="全套建筑施工图：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="dwg"
        :file-list="form.WorkingDraw"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '全套建筑施工图',
        }"
        :on-check-format="checkDWG"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'WorkingDraw')"
        @delete="del($event, 'form', 'WorkingDraw')"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.WorkingDraw"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item
      label-width="160px"
      label="不动产预测绘成果的报告书（备案）："
    >
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.PreSurveyReport"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '不动产预测绘成果的报告书（备案）',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PreSurveyReport')"
        @delete="del($event, 'form', 'PreSurveyReport')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PreSurveyReport"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="项目跟踪卡：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.ProjectTrackCard"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '项目跟踪卡',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'ProjectTrackCard')"
        @delete="del($event, 'form', 'ProjectTrackCard')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.ProjectTrackCard"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="质量竣工预验收结论意见：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.AcceptComment"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '质量竣工预验收结论意见',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'AcceptComment')"
        @delete="del($event, 'form', 'AcceptComment')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.AcceptComment"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="方案文本：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="png / jpg / gif / bmp / pdf"
        :file-list="form.SchemeText"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '方案文本',
        }"
        :on-check-format="checkImgAndPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'SchemeText')"
        @delete="del($event, 'form', 'SchemeText')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.SchemeText"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="土地用途及占比相关佐证材料（国有建设用地出让合同、划拨决定书、不动产权证、土地使用证等）：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.LandUse"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '土地用途',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'LandUse')"
        @delete="del($event, 'form', 'LandUse')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.LandUse"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="《结清地价款通知书》、《中央非税收入统一票据》或出让土地证书。仅危旧房改限价房提供：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.AdviceNote"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '结清地价款通知书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'AdviceNote')"
        @delete="del($event, 'form', 'AdviceNote')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.AdviceNote"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="城市配套费结清证明（仅2014年以前的审批项目提供）：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.CityConstruction"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '城市配套费结清证明',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'CityConstruction')"
        @delete="del($event, 'form', 'CityConstruction')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.CityConstruction"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="土地来源（出让合同、补充协议、划拨决定书等）：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.LandSource"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '土地来源',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'LandSource')"
        @delete="del($event, 'form', 'LandSource')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.LandSource"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="规划设计条件通知书：">
      <list-upload
        v-if="isRecordBack(5) && !isReplaceMdb"
        file-format="pdf"
        :file-list="form.PlanNotice"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '规划设计条件通知书',
        }"
        :on-check-format="checkPDF"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'PlanNotice')"
        @delete="del($event, 'form', 'PlanNotice')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.PlanNotice"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="其他：">
      <list-upload
        v-if="(isRecordBack(5) && !isReplaceMdb) || isRecordAction(5)"
        file-format="png / jpg / gif / bmp / pdf / zip / rar"
        :file-list="form.Others"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '其他附件',
        }"
        :on-check-format="checkOthers"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'Others')"
        @delete="del($event, 'form', 'Others')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.Others"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import ViewInfo from 'mixins/business/view-info.js'

export default {
  name: 'ActualMappingAttachmentInfo',
  mixins: [ViewInfo],
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e
        if (e.length) {
          this.$refs[formName].clearValidate(attr)
        }
      }
      this.$emit('upload-success', e)
    }
  }
}
</script>
<style lang="scss" scoped></style>
