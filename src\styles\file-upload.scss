.file-upload {
    &__btn {
      // cursor: pointer;
      width: 100%;
      // max-width: 500px;
      line-height: 20px !important;
  
      /deep/ .el-upload-dragger {
        width: 100%;
        padding: 0 30px;
        max-width: 360px;
        height: 130px;
  
        .el-icon-upload {
          margin-top: 20px;
        }
  
        .el-upload__text {
          font-size: 12px;
        }
      }
  
      /deep/ .el-upload-list__item {
        position: relative;
        transition: none !important;
        line-height: 20px !important;
        padding: 5px 10px;
        outline: none;
  
        &:hover {
          .file-actions {
            opacity: 1;
          }
        }
      }
  
      /deep/ .el-progress {
        // width: 98% !important;
        left: 0;
        top: 8px;
      }
    }
  
    &__tip {
      font-size: 12px;
      line-height: 16px;
      margin-top: 10px;
      color: #f0ad4e;
    }
  }
  .file-upload-text {
    padding: 0 20px;
    font-size: 12px;
  }
  
  .file-item {
    position: relative;
    align-items: center;
    justify-content: space-between;
    line-height: 20px !important;
  
    &__left {
      align-items: center;
      max-width: 88%;
    }
  }
  
  .file-thumbnail {
    max-width: 25px;
    max-height: 25px;
    border-radius: 4px;
    display: block;
    // cursor: pointer;
  }
  
  .file-icon {
    min-width: 25px;
    text-align: center;
    i {
      font-size: 20px;
    }
  }
  
  .file-name {
    width: 85%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
  }
  .file-size {
    font-size: 12px;
  }
  
  .file-list-title {
    font-size: 12px;
  }
  
  .file-actions {
    cursor: default;
    opacity: 0;
    font-size: 16px;
    i {
      cursor: pointer;
      margin-left: 5px;
  
      &:hover {
        color: $color-primary;
      }
    }
  }