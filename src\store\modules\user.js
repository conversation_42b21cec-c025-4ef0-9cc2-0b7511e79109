/* eslint-disable */
import {
  getToken,
  setToken,
  removeToken,
  getCompanyID,
  setCompanyID,
  removeCompanyID,
} from "utils/auth";
import { logInfo } from "utils";
import UserApi from "api/user";
import CompanyApi from "api/company-info";
import { resetRouter } from "router";

const defaultCompany = {
  // 单位申请状态，0 未提交，1 已提交/待审核，2 审核中，3 审核通过（完成注册），4 退回修改，5 已失效，6 需要协议备案
  stateCode: 0,
  /**
   * 单位类型
   * 测绘单位：测绘单位
   * 建设单位：开发商、机关单位、一般企业
   */
  companyType: null,
  // 单位名称
  companyName: null,
  //企业证件号
  companyNo: null,
  // 单位id
  companyID: null,
  // 单位角色 单位管理员、报建员、测绘人员、注册测绘师
  companyRole: null,

};

const defaultUser = {
  isRealName: false,
  personName: null,
  userName: null,
  personNo: null,
  userId: null,
  /**
   * 用户色
   * 0 未实名用户
   * 1 单位注册未提交/已提交待审核/审核中/注册退回/已关闭（可见“单位注册”菜单）
   * 2 单位注册已审核通过（可见“单位信息”菜单）
   *  2-a 单位管理员 （可见“转移业务”菜单）
   * 3 非企业类型用户（可见“用户信息”菜单）
   * c 单位注册人员(company)
   *  c-d 建设单位(company-developer)
   *  c-m 测绘单位(company-mapping)
   */
  roles: [],
};

// 清空账户信息
function clearAccount(_commit) {
  removeToken();
  removeCompanyID();

  resetRouter();
  _commit("SET_TOKEN", null);
  _commit("SET_USER", { ...defaultUser });
  _commit("SET_COMPANY", { ...defaultCompany });
}

function setRoles(data, stateCode, companyType, companyRole) {
  return new Promise(async (resolve, reject) => {
    data.roles = [];

    // 未实名
    if (!data.isRealName) {
      data.roles = ["0"];
    }

    if (companyType) {
      let role = companyType === "测绘单位" ? ["c-m"] : ["c-d"];
      data.roles = data.roles.concat(role);
    }

    // 单位信息审核状态
    switch (stateCode) {
      // 注册完成
      case 3:
        data.roles.push("2");
        if(companyRole === "单位管理员"){
          data.roles.push("2-a");
        }
        break;
      // 退回修改
      case 4:
        try {
          const res = await CompanyApi.GetCompanyRegisterInfo();
          // 单位注册退回
          if (res.Data) {
            data.roles.push("1");
            break;
          }
          // 单位变更退回
          else {
            data.roles.push("2");
            if(companyRole === "单位管理员"){
              data.roles.push("2-a");
            }
            data.roles = data.roles.concat(role);
            break;
          }
        } catch (err) {
          console.log("roles's error");
          reject(err);
        }
        break;
      default:
        data.roles.push("1");
    }

    //企业类型账户
    if (data.userName.indexOf("qy_") !== 0) {
      let role = ["3"];
      data.roles = data.roles.concat(role);
    }
    resolve(data);
  });
}

const state = {
  token: getToken(),
  // 当前用户信息
  user: { ...defaultUser },
  // 当前单位信息
  company: { ...defaultCompany },
  // 单位列表
  companyList: [],
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_USER: (state, user) => {
    state.user = user;
  },
  SET_ROLES: (state, roles) => {
    state.user.roles = roles;
  },
  SET_COMPANY: (state, company) => {
    company.companyID ? setCompanyID(company.companyID) : removeCompanyID();
console.log(company);
    state.company = company;
  },
  SET_COMPANY_NAME: (state, companyName) => {
    state.company.companyName = companyName;
  },
  SET_COMPANY_STATE_CODE: (state, stateCode) => {
    state.company.stateCode = stateCode;
  },
  SET_COMPANY_LIST: (state, companyList) => {
    state.companyList = companyList;
  },
};

const actions = {
  // 修改角色权限
  setRoles({ commit, state }, roles) {
    commit("SET_ROLES", roles);
  },

  // 修改单位信息
  setCompany({ commit, state }, company) {
    commit("SET_COMPANY", company);
  },

  // 修改单位名称
  setCompanyName({ commit, state }, companyName) {
    commit("SET_COMPANY_NAME", companyName);
  },

  // 修改单位申请状态
  setCompanyStateCode({ commit, state }, code) {
    commit("SET_COMPANY_STATE_CODE", code);
  },

  // 修改单位列表
  setCompanyList({ commit, state }, companyList) {
    commit("SET_COMPANY_LIST", companyList ? companyList : []);
  },


  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise(async (resolve, reject) => {
      try {
        const userInfo = await UserApi.GetUser();

        if (userInfo) {
          const companyInfo = await UserApi.GetCompanyInfoByUserInfo();

          let stateCode = 0;
          let companyType = null;
          let companyRole = null;
          if (companyInfo.StateCode === 1 && companyInfo.Data) {
            const { CompanyList, CompanyType } = companyInfo.Data;
            companyType = CompanyType;

            let company = { ...defaultCompany };

            if (CompanyList.length) {
              let currentCompany = CompanyList[0];

              if (getCompanyID()) {
                const findCompany = CompanyList.find(
                  (e) => e.CID === getCompanyID()
                );

                currentCompany = findCompany ? findCompany : CompanyList[0];
              }

              const { CID, CName, CType, CRole, CNo, State } = currentCompany;
              stateCode = State;

              company = {
                stateCode,
                companyType: CType,
                companyName: CName,
                companyNo: CNo,
                companyID: CID,
                companyRole: CRole,
              };

              companyRole = CRole;
            }
            commit("SET_COMPANY", company);
            commit("SET_COMPANY_LIST", CompanyList);
          }

          let user = await setRoles(
            {
              isRealName: userInfo.IsRealName,
              personName: userInfo.PersonName,
              userName: userInfo.UserName,
              personNo: userInfo.PersonNo,
              userId: userInfo.UserId,
            },
            stateCode,
            companyType,
            companyRole 
          );

          logInfo(`用户角色：${user.roles}`);
          console.log(user.roles);
          commit("SET_USER", user);

          resolve(user);
        } else {
          clearAccount(commit);
          resolve(false);
        }
      } catch (err) {
        console.log(err);
        clearAccount(commit);
        reject(err);
      }
    });
  },

  // 登出
  logout({ commit, state }) {
    return new Promise(async (resolve, reject) => {
      try {
        // 提醒邕e登登出
        const logOff = await UserApi.LogOff();
        logOff
          ? logInfo(`邕e登登出成功：${logOff}`)
          : logInfo(`邕e登登出失败：${logOff}`);
        clearAccount(commit);
        resolve(logOff);
      } catch (err) {
        clearAccount(commit);
        logInfo(`邕e登登出失败：${err}`);
        reject(err);
      }
    });
  },

  // 登录
  login({ commit, state }, from) {
    return new Promise(async (resolve, reject) => {
      try {
        const userInfo = await UserApi.Login(from);
        if (userInfo) {
          setToken(userInfo.access_token);
          commit("SET_TOKEN", userInfo.access_token);

          // 提醒邕e登登录
          const loginWitdhToken = await UserApi.LoginWitdhToken();
          if (loginWitdhToken) {
            logInfo(`邕e登登录成功：${loginWitdhToken}`);

            const companyInfo = await UserApi.GetCompanyInfoByUserInfo();

            let stateCode = 0;
            let companyType = null;
            let companyRole = null;
            let userId = 0;
            if (companyInfo.StateCode === 1 && companyInfo.Data) {
              const { CompanyList, CompanyType, UserId } = companyInfo.Data;
              companyType = CompanyType;
              // 因为/token接口无法获取userId，影响“我的业务”详情中返回列表的判断，故从此获取
              userId = UserId;

              let company = { ...defaultCompany };

              if (CompanyList.length) {
                let currentCompany = CompanyList[0];

                if (getCompanyID()) {
                  const findCompany = CompanyList.find(
                    (e) => e.CID === getCompanyID()
                  );

                  currentCompany = findCompany ? findCompany : CompanyList[0];
                }

                const { CID, CName, CType, CRole, CNo, State } = currentCompany;
                stateCode = State;

                company = {
                  stateCode,
                  companyType: CType,
                  companyName: CName,
                  companyNo: CNo,
                  companyID: CID,
                  companyRole: CRole,
                };
              }
              companyRole = company.companyRole;
              commit("SET_COMPANY", company);
              commit("SET_COMPANY_LIST", CompanyList);
            }

            let user = await setRoles(
              {
                isRealName: userInfo.isRealName === "True" ? true : false,
                personName: userInfo.personName,
                userName: userInfo.userName,
                personNo: userInfo.personNo,
                userId,
              },
              stateCode,
              companyType,
              companyRole
            );

            logInfo(`用户角色：${user.roles}`);

            commit("SET_USER", user);

            resolve(user);
          } else {
            logInfo(`邕e登登录失败：${loginWitdhToken}`);
            clearAccount(commit);
            resolve(false);
          }
        } else {
          clearAccount(commit);
          resolve(false);
        }
      } catch (err) {
        console.log(err);
        clearAccount(commit);
        reject(err);
      }
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
