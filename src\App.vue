<template>
  <div id="app" v-loading="sysLoading" element-loading-text="系统初始化，请稍后...">
    <router-view />
  </div>
</template>

<script>
/* eslint-disable */
import { mapGetters } from "vuex";

export default {
  name: "App",
  computed: {
    ...mapGetters(["sysLoading", "currentEnv"])
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      console.log(`current environment is ${this.currentEnv}`);
      if (window.performance.navigation.type === 1) {
        this.GLOBAL.logInfo("页面被刷新");
      } else {
        this.GLOBAL.logInfo("首次进入页面");
      }
    }
  }
};
</script>
