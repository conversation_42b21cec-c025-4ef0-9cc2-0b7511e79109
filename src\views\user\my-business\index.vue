<template>
  <!-- eslint-disable -->
  <div class="my-business-container">
    <div class="tabs-title">
      <el-tabs v-model="activeName" type="card" @tab-click="tabsChange">
        <el-tab-pane v-for="(item) in tabs" :key="item.label" :label="item.label" :name="item.name"></el-tab-pane>
      </el-tabs>
      <el-button class="create-btn" type="primary" @click="toBusinessList">创建业务</el-button>
    </div>
    <div class="tabs-content">
      <transition name="fade" mode="out-in">
        <router-view />
      </transition>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/business/index.js";

export default {
  name: "CompanyAudit",
  data() {
    return {
      activeName: "BusinessInProgress",
      tabs: [
        {
          name: "BusinessInProgress",
          label: "在办业务",
          url: "/user/my-business/in-progress"
        },
        {
          name: "BusinessDone",
          label: "已办业务",
          url: "/user/my-business/done"
        }
      ],
      businessList: []
    };
  },
  watch: {
    $route(val) {
      this.init();
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.activeName = this.$route.name;
    },
    tabsChange(e) {
      const index = parseInt(e.index);
      this.$router.push({ path: this.tabs[index].url });
    },
    toBusinessList() {
      this.$router.push({ name: "BusinessList" });
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/compnay-info-tabs.scss";
.my-business-container {
  padding: 20px 0;
}
.tabs-content {
  padding: 20px;
  min-height: calc(100vh - 497px);
  background: #ffffff;
  border-left: 1px solid #dfe4ed;
  border-right: 1px solid #dfe4ed;
  border-bottom: 1px solid #dfe4ed;
  border-radius: 0 0 4px 4px;
}

.tabs-title {
  position: relative;
}

.create-btn {
  position: absolute;
  right: 0;
  top: 0;
}
</style>