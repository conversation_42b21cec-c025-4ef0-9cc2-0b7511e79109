<template>
  <el-form ref="form" :model="form" label-width="140px">

    <div>
      <el-form-item label-width="160px" label="楼层平面图面积计算框线图：" class=" is-required">
        <!-- <list-upload
          v-if="isRecordBack(5)"
          file-format="dwg"
          :file-list="form.EleAreaLine"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '报批规划指标校验图',
          }"
          :on-check-format="checkDWG"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'EleAreaLine')"
          @delete="del($event, 'form', 'EleAreaLine')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        /> -->
        <file-list
          :file-list="attachmentData.FloorAreaLine"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="CAD总平图面积计算框线图：">
        <file-list
          :file-list="attachmentData.CADAreaLine"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
    </div>

    <div v-if="needManualAudit()===true || (auditStatus =='1' || auditStatus =='2')">
      <el-form-item label-width="160px" label="工程规划许可证件及附件：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="png / jpg / gif / bmp / pdf"
          :file-list="form.ProjectLicenceImg"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '工程规划许可证件',
          }"
          :on-check-format="checkImgAndPDF"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'ProjectLicenceImg')"
          @delete="del($event, 'form', 'ProjectLicenceImg')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.ProjectLicenceImg"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="经备案的建筑设计图：">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="dwg"
          :file-list="form.BuildingDesgin"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '经备案的建筑设计图',
          }"
          :on-check-format="checkDWG"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'BuildingDesgin')"
          @delete="del($event, 'form', 'BuildingDesgin')"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.BuildingDesgin"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="建筑定位图：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="dwg"
          :file-list="form.BuildingLocationMap"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '建筑定位图',
          }"
          :on-check-format="checkDWG"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'BuildingLocationMap')"
          @delete="del($event, 'form', 'BuildingLocationMap')"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.BuildingLocationMap"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="对应楼栋的建筑单体方案文本平立剖图：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="pdf"
          :file-list="form.SimpleImg"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '对应楼栋的建筑单体方案文本平立剖图',
          }"
          :on-check-format="checkPDF"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'SimpleImg')"
          @delete="del($event, 'form', 'SimpleImg')"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.SimpleImg"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="经审查合格的施工图纸的平立剖图：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="dwg"
          :file-list="form.SectionPlan"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '经审查合格的施工图纸的平立剖图',
          }"
          :on-check-format="checkDWG"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'SectionPlan')"
          @delete="del($event, 'form', 'SectionPlan')"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.SectionPlan"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="总平面图：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="dwg"
          :file-list="form.SitePlan"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '总平面图',
          }"
          :on-check-format="checkDWG"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'SitePlan')"
          @delete="del($event, 'form', 'SitePlan')"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.SitePlan"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="门牌证明（变更门牌时需上传）：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="png / jpg / gif / bmp / pdf"
          :file-list="form.DoorPlate"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '门牌证明（变更门牌时需上传）',
          }"
          :on-check-format="checkImgAndPDF"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'DoorPlate')"
          @delete="del($event, 'form', 'DoorPlate')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.DoorPlate"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="人防许可文件，人防区域阴影图，设计服务范围说明材料：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="png / jpg / gif / bmp / pdf"
          :file-list="form.CivilDocuments"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '人防许可文件，人防区域阴影图，设计服务范围说明材料',
          }"
          :on-check-format="checkImgAndPDF"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'CivilDocuments')"
          @delete="del($event, 'form', 'CivilDocuments')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.CivilDocuments"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="授权委托书：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="png / jpg / gif / bmp / pdf"
          :file-list="form.Authorization"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '授权委托书',
          }"
          :on-check-format="checkImgAndPDF"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'Authorization')"
          @delete="del($event, 'form', 'Authorization')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.Authorization"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="配套费结清证明材料（或配套费减免材料）：" class=" is-required">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(4)"
          file-format="png / jpg / gif / bmp / pdf"
          :file-list="form.SetCosts"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '配套费结清证明材料（或配套费减免材料）',
          }"
          :on-check-format="checkImgAndPDF"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'SetCosts')"
          @delete="del($event, 'form', 'SetCosts')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.SetCosts"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
      <el-form-item label-width="160px" label="其他：">
        <list-upload
          v-if="isRecordBack(5) || isRecordAction(5)"
          file-format="png / jpg / gif / bmp / pdf / zip / rar"
          :file-list="form.Others"
          :file-size="102400"
          :data="{
            BusinessType: baseInfo.BusinessType,
            BusinessID: baseInfo.ID,
            AttachmentType: '申请材料附件',
            AttachmentCategories: '其他附件',
          }"
          :on-check-format="checkOthers"
          :disabled="baseInfo.StateCode === 4"
          @upload-success="upload($event, 'form', 'Others')"
          @delete="del($event, 'form', 'Others')"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
        <file-list
          v-else
          :file-list="attachmentData.Others"
          @preview="preview"
          @download-start="downloadStart"
          @download-end="downloadEnd"
          @download-fail="downloadFail"
        />
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
// 组件
import ViewInfo from 'mixins/business/view-info.js'

export default {
  name: 'PreMappingMergeAttachmentInfo',
  mixins: [ViewInfo],
  props: {
    // 是否需要人工审核
    needManualAudit: {
      type: Function,
      default: null
    },
    auditStatus: {
      type: String,
      default: null
    }
  },
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e
        if (e.length) {
          this.$refs[formName].clearValidate(attr)
        }
      }

      this.$emit('upload-success', e);
    },
    //获取附件
    getFiles() {
      return this.$refs['form'];
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
