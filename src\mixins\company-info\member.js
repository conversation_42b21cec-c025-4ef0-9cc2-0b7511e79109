/*
 * 模块 : 单位信息-人员信息表单配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-09-07
 * 版本 : version 1.0
 */
/* eslint-disable */
// 组件
import ImgUpload from "components/common/AttachmentUpload/ImgUpload";
// mixins
import Attachment from "mixins/company-info/attachment.js";
// vuex
import { mapGetters } from "vuex";
// 校验
import { validRealName, validIDcard, validMobile } from "utils/validate";

const urlPrefix = process.env.VUE_APP_URL_PREFIX;

export default {
    components: {
        ImgUpload
    },
    mixins: [Attachment],
    computed: {
        ...mapGetters(["companyID"])
    },
    data() {
        const validateRealName = (rule, value, callback) => {
            if (!value) {
                callback(new Error("请输入姓名"));
            } else if (!validRealName(value)) {
                callback(new Error("姓名格式错误"));
            } else {
                callback();
            }
        };

        const validateIDcard = (rule, value, callback) => {
            if (!value) {
                callback(new Error("请输入身份证号"));
            } else if (!validIDcard(value)) {
                callback(new Error("身份证号格式错误"));
            } else {
                callback();
            }
        };

        const validateMobile = (rule, value, callback) => {
            if (!value) {
                callback(new Error("请输入手机号码"));
            } else if (!validMobile(value)) {
                callback(new Error("手机号码格式有误"));
            } else {
                callback();
            }
        };

        const validateSurveyorCertificateImg = (rule, value, callback) => {
            if (
                !this.form.SurveyorCertificateImg ||
                !this.form.SurveyorCertificateImg.length
            ) {
                callback(new Error("请上传注册测绘师证书"));
            } else {
                callback();
            }
        };

        const validateSurveyorCertificateChapter = (rule, value, callback) => {
            if (
                !this.form.SurveyorCertificateChapter ||
                !this.form.SurveyorCertificateChapter.length
            ) {
                callback(new Error("请上传注册测绘师执业章"));
            } else {
                callback();
            }
        };

        return {
            title: "添加人员",
            // 证书样例
            certExampleDialog: {
                visible: false,
                imgUrl: `${urlPrefix}/static/img/registered-surveyor-no-example.jpg`
            },
            // 默认表单
            defaultForm: {
                PersonName: null,
                PersonNumber: null,
                PersonAge: null,
                PersonPhone: null,
                CompanyName: "",
                PersonRole: null,
                RegisteredSurveyorNo: null,
                ValidityTime: null
            },
            form: {},
            // 附件
            attachmentInfo: {
                SurveyorCertificateImg: [],
                SurveyorCertificateChapter: []
            },
            rules: {
                PersonName: [
                    { required: true, validator: validateRealName, trigger: "blur" }
                ],
                PersonNumber: [
                    { required: true, validator: validateIDcard, trigger: "blur" }
                ],
                PersonPhone: [
                    { required: true, validator: validateMobile, trigger: "blur" }
                ],
                PersonRole: [
                    {
                        required: true,
                        message: "请选择角色",
                        trigger: "change"
                    }
                ],
                RegisteredSurveyorNo: [
                    {
                        required: true,
                        message: "请输入注册测绘师证书编号",
                        trigger: "blur"
                    }
                ],
                ValidityTime: [
                    {
                        required: true,
                        message: "请选择注册测绘师证书有效期",
                        trigger: "change"
                    }
                ],
                SurveyorCertificateImg: [
                    {
                        required: true,
                        validator: validateSurveyorCertificateImg,
                        trigger: "change"
                    }
                ],
                SurveyorCertificateChapter: [
                    {
                        required: true,
                        validator: validateSurveyorCertificateChapter,
                        trigger: "change"
                    }
                ]
            }
        };
    },
    methods: {
        // 重置表单
        reset() {
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }

            this.form.SurveyorCertificateImg = [];
            this.form.SurveyorCertificateChapter = [];
        },
    }
};
