<template>
  <!-- eslint-disable -->
  <div class="view-info-container">
    <div class="business-info-title">
      <span>申请信息</span>
    </div>
    <el-card shadow="never">
      <el-form ref="form" :model="form" label-width="180px">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务编号：">{{ baseInfo.BusinessNumber | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务类型：">{{ baseInfo.BusinessType | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务名称：">{{ baseInfo.BusinessName | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人：">{{ baseInfo.CreatePersonName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机号：">{{ baseInfo.CreatePersonPhone | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位：">{{ baseInfo.DeveloperName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测绘单位：">{{ baseInfo.SurveyCompanyName | isNull }}</el-form-item>
              </el-col>
            </el-row>

                        <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位地址：">{{ contentInfo.CompanyAddress | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法定代表人姓名：">{{ contentInfo.LegalPersonName | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="法定代表人身份证号：">{{ contentInfo.LegalPersonNumber | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="法定代表人联系电话：">{{ contentInfo.LegalPersonPhone | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="规划条件编号：">{{ contentInfo.PlanConditionNumber | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目类型：">{{ contentInfo.ProjectType | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="是否自建房：">{{ contentInfo.IsZJ | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请人类型：">{{ contentInfo.PersonType | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="统一项目代码：">{{ contentInfo.UnifiedProjectCode | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="土地用途：">
                <dynamic-table
                  ref="landuseTable"
                  class="table-container"
                  :table-header="landuseTableHeader"
                  :table-data="contentInfo.ProjectLandUse"
                  :default-props="tableProps"
                  :showPagination="false"
                >
                </dynamic-table>
              </el-form-item>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="业务信息" name="2">
            <el-form-item label="工程规划许可证：">
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="contentInfo.ProjectPlanPermission"
                :default-props="tableProps"
                :showPagination="false"
              >
                <el-table-column prop="action" label="操作" width="160" fixed="right" align="center">
                  <template slot-scope="{ row, $index }">
                    <el-button
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index)"
                    >查看详情</el-button>
                  </template>
                </el-table-column>
              </dynamic-table>
            </el-form-item>
            <el-form-item label="不动产单元号（宗地号）：">{{ contentInfo.GroundCode | isNull }}</el-form-item>
          </el-collapse-item>
          <el-collapse-item title="附件信息" name="3" v-if="showAttachmentInfo">
            <attachment-info
              :base-info="baseInfo"
              :step="step"
              :step-list="stepList"
              :attachment-data="attachmentData"
              :actions-infos="actionsInfos"
              :current-action="currentAction"
              @upload-success="upload($event, 'form', 'Others')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import ViewInfo from "mixins/business/view-info.js";
// 组件
import AttachmentInfo from "./attachmentInfo.vue";

export default {
  name: "OverallActualMappingViewInfo",
  mixins: [ViewInfo],
  components: { AttachmentInfo },
  computed: {
    showAttachmentInfo() {
      const { step, mappingCompany } = this;
      if (step === 3 && mappingCompany) {
        return false;
      }

      return true;
    }
  },
  data() {
    return {
      licenceTableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50"
        },
        {
          title: "工程规划许可证号",
          key: "Code",
          align: "center"
        },
        {
          title: "建设单位",
          key: "ConstructCompany",
          align: "center"
        },
        {
          title: "项目名称",
          key: "ProjectName",
          align: "center"
        }
      ],
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title"
      },
      landuseTableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50"
        },
        {
          title: "用途名称",
          key: "landUse",
          align: "center"
        },
        {
          title: "占用比例（%）",
          key: "percent",
          align: "center"
        }
      ],
    };
  },
  methods: {
    // 查看工程规划许可证
    viewLicence(row, index) {
      this.$emit("view-licence", row, index);
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
