<template>
  <business-layout
    class="setout-survey-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="prevBtn"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @prev="prev"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1 && developer">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row :gutter="10">
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：" prop="BusinessName">
                  <el-input
                    v-model.trim="form1.BusinessName"
                    placeholder="请输入业务名称"
                    :disabled="BaseInfo.StateCode === 4"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业主单位：">
                  <el-input
                    v-model.trim="BaseInfo.DeveloperName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="业务信息" style="margin-top: -10px">
            <el-form-item
              label-width="210px"
              label="工程规划许可证："
              prop="ProjectPlanPermission"
            >
              <span slot="label">
                工程规划许可证
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点击查看图例"
                  placement="bottom"
                >
                  <i
                    class="el-icon-question"
                    @click="viewLegend('工规证')"
                  /> </el-tooltip
                >：</span
              >
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="form1.ProjectPlanPermission"
                :default-props="tableProps"
                :show-pagination="false"
              >
                <el-table-column
                  prop="action"
                  label="操作"
                  width="180"
                  fixed="right"
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index)"
                      >查看</el-button
                    >
                    <el-button
                      v-if="row.Add && BaseInfo.StateCode !== 4"
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="storeLicence(row, $index)"
                      >编辑</el-button
                    >
                    <el-popconfirm
                      v-if="BaseInfo.StateCode !== 4"
                      title="确认删除?"
                      @onConfirm="delLicence(row, $index)"
                    >
                      <el-button
                        slot="reference"
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        class="ml-10"
                        >删除</el-button
                      >
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </dynamic-table>
              <div
                v-if="BaseInfo.StateCode !== 4"
                class="table-add-btn"
                @click="setLicenceGetDialogVisible(true)"
              >
                <i class="el-icon-plus" />添加工程规划许可证
              </div>
            </el-form-item>
            <!-- <el-form-item
              label-width="210px"
              label="不动产单元号（宗地号）："
              prop="GroundCode"
            >
              <span slot="label"
                >不动产单元号（宗地号）
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="点击查看图例"
                  placement="bottom"
                >
                  <i
                    class="el-icon-question"
                    @click="viewLegend('不动产权证')"
                  /> </el-tooltip
                >：
              </span>
              <el-input
                v-model.trim="form1.GroundCode"
                placeholder="宗地号为19位宗地统一代码，如：450103001001GB00001"
              />
            </el-form-item> -->
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item
              label-width="190px"
              label="市政工程规划许可证件："
              prop="CouncilLicenceImg"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.CouncilLicenceImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '市政工程规划许可证件',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'CouncilLicenceImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
                @delete="del($event, 'form1', 'CouncilLicenceImg')"
              />
            </el-form-item>

            <el-form-item
              label-width="190px"
              label="施工红线图："
              prop="RedLineDrawingImg"
            >
              <list-upload
                file-format="png / jpg / gif / bmp / pdf"
                :file-list="form1.RedLineDrawingImg"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '施工红线图',
                }"
                :on-check-format="checkImgAndPDF"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'RedLineDrawingImg')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
                @delete="del($event, 'form1', 'RedLineDrawingImg')"
              />
            </el-form-item>
            <el-form-item label-width="190px" label="其他：">
              <list-upload
                file-format="png / jpg / gif / bmp / pdf / zip / rar / dwg"
                :file-list="form1.Others"
                :file-size="102400"
                :data="{
                  BusinessType: BaseInfo.BusinessType,
                  BusinessID: businessID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '其他附件',
                }"
                :on-check-format="checkOthers"
                :disabled="BaseInfo.StateCode === 4"
                @upload-success="upload($event, 'form1', 'Others')"
                @delete="del($event, 'form1', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
      <div v-if="currentStep === 2 && developer">
        <!-- 委托测绘单位 -->
        <mapping-company-list
          class="mb-20"
          :business-class="BaseInfo.BusinessClass"
          :business-id="businessID"
          :current-action-id="CurrentAction.ID"
          :flow-name="FlowInfo.FlowName"
          @select="refreshStep"
        />
      </div>
      <div v-if="currentStep === 3">
        <!-- 上传放线报告 -->
        <tip v-if="developer" class="mb-20 font-20 text-center bold"
          >等待测绘单位汇交并确认放线报告...</tip
        >
        <el-form
          v-if="mappingCompany"
          ref="form3"
          :model="form3"
          :rules="rules3"
          label-width="100px"
        >
          <tip v-if="!form3.Report.length" class="mb-20" type="default">
            请上传放线报告。
            <span v-if="form3.Report.length">
              若您要汇交的放线报告已全部上传完毕，请<template
                v-if="surveyBusinessManager || surveyAdmin"
                >联系本单位注册测绘师</template
              >登录邕e登App进入“我的授权”模块刷脸确认测绘成果
            </span>
          </tip>
          <tip v-else class="mb-20" type="success">
            若您要汇交的放线报告已全部上传完毕，请
            <template v-if="surveyBusinessManager || surveyAdmin"
              >联系本单位注册测绘师</template
            >登录邕e登App进入“我的授权”模块刷脸确认测绘成果
          </tip>
          <el-form-item label="放线报告：" prop="Report">
            <list-upload
              file-format="pdf"
              :file-list="form3.Report"
              :file-size="102400"
              :data="{
                BusinessType: BaseInfo.BusinessType,
                BusinessID: businessID,
                AttachmentType: '项目成果附件',
                AttachmentCategories: '放线报告',
              }"
              :on-check-format="checkPDF"
              @upload-success="upload($event, 'form3', 'Report')"
              @delete="del($event, 'form3', 'Report')"
              @preview="preview"
              @download-start="downloadStart"
              @download-end="downloadEnd"
              @download-fail="downloadFail"
            />
            <div class="example-container mt-20">
              <p class="example-hint">温馨提示：</p>
              <ol class="example-list">
                <!--<li class="example-list-item">
                  放线测量报告模板：
                  <a
                    class="example-list-item__link"
                    @click="
                      downloadSystemAttachment(
                        '南宁市建设工程规划放线测量报告.doc'
                      )
                    "
                    >《南宁市建设工程规划放线测量报告》</a
                  >
                </li>-->
                <!-- <li class="example-list-item">所上传的放线测量报告需盖章</li> -->
                <li class="example-list-item">
                  如有疑问，请联系南宁市不动产登记中心测绘管理部，联系电话：
                  <a class="example-list-item__phone">4306662</a>
                </li>
              </ol>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div
        v-if="currentStep === 4 && mappingCompany && BaseInfo.StateCode !== 2"
        class="mb-20"
      >
        <tip class="font-20 text-center bold">等待业主单位验收成果...</tip>
      </div>
      <div v-if="currentStep === 5 && BaseInfo.StateCode !== 2" class="mb-20">
        <tip class="font-20 text-center bold">
          成果验收已完成，系统将进行检查入库，请等待5分钟左右，待入库完成备案成功后可下载《规划验线备案信息表》
        </tip>
      </div>
      <div
        v-if="
          currentStep === 5 &&
          BaseInfo.StateCode === 2 &&
          auditFeedback &&
          auditFeedback.length
        "
        class="mb-20"
      >
        <tip type="success" class="mb-20">
          您提交的{{ FlowInfo.FlowName }}已办结，<a
            class="link"
            @click="download(auditFeedback[0])"
            >点击此处</a
          >下载{{ auditFeedback[0].AttachmentName }}
        </tip>
      </div>
      <!-- 放线报告下载 -->
      <result
        v-if="showResult()"
        class="mb-20"
        :report="form3.Report"
        :audit-feedback="auditFeedback"
        :show-report="true"
        :name="FlowInfo.FlowName"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <!-- 操作按钮 -->
    <template slot="operateBtnAfter">
      <template v-if="currentStep == 3">
        <el-button
          v-if="developer && BaseInfo.StateCode === 0"
          :loading="prevBtn.loading"
          type="primary"
          :disabled="
            ContentInfo.DataCheckID && ContentInfo.DataCheckState === 0
          "
          @click="reSelectSurveyCompany()"
        >
          <i class="iconfont icon-back mr-5" />重新委托测绘单位
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('退回业务', true)"
        >
          <i class="iconfont icon-back mr-5" />退回业务
        </el-button>
        <el-button
          v-if="mappingCompany && BaseInfo.StateCode === 1"
          type="success"
          :disabled="disabledSubmitAccept()"
          @click="submitAccept(currentStep)"
        >
          <i class="el-icon-check mr-5" />提交验收
        </el-button>
      </template>
      <template
        v-if="currentStep === 4 && developer && BaseInfo.StateCode === 1"
      >
        <el-button
          :loading="prevBtn.loading"
          type="primary"
          @click="setReasonDialogVisible('验收不通过', true)"
        >
          <i class="iconfont icon-back mr-5" />验收不通过
        </el-button>
        <el-button
          class="ml-10"
          :loading="acceptBtn.loading"
          type="success"
          @click="confirmAccept()"
        >
          <i class="el-icon-check mr-5" />{{ acceptText }}
        </el-button>
      </template>
    </template>
    <!-- 预览申请信息 -->
    <template slot="viewInfo">
      <view-info
        v-if="currentStep >= 2 || (currentStep === 1 && mappingCompany)"
        :base-info="BaseInfo"
        :content-info="ContentInfo"
        :attachment-data="form1"
        :step="currentStep"
        :step-list="stepList"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
        @view-licence="viewLicence"
      />
    </template>
    <!-- 额外内容 -->
    <template slot="extra">
      <!-- 工程规划许可证预览 -->
      <el-dialog
        title="查看工程规划许可证"
        :visible="licenceViewDialog.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
        width="1100px"
        @close="licenceViewDialog.visible = false"
      >
        <project-licence-view
          :row="licenceViewDialog.row"
          :index="licenceViewDialog.index"
        />
      </el-dialog>
      <!-- 获取工程规划许可证 -->
      <project-licence-get-dialog
        :visible.sync="licenceGetDialog.visible"
        :list="form1.ProjectPlanPermission"
        :business-id="businessID"
        :business-class="BaseInfo.BusinessClass"
        @add-lience="storeLicence(null, -1)"
        @submit="licenceGetSuccess"
        @close="setLicenceGetDialogVisible(false)"
      />
      <!-- 存储工程规划许可证 -->
      <project-licence-store-dialog
        :visible.sync="licenceStoreDialog.visible"
        :row="licenceStoreDialog.row"
        :index="licenceStoreDialog.index"
        :list="form1.ProjectPlanPermission"
        @submit="licenceStoreSuccess"
        @close="setLicenceStoreDialogVisible(false)"
      />
      <!-- 提交验收，选择注册测绘师弹窗 -->
      <select-registered-surveyor
        :visible.sync="selectRSurveyorDialog.visible"
        :business-id="BaseInfo.ID"
        :action-id="CurrentAction.ID"
        :flow-name="FlowInfo.FlowName"
        @select="getBusiness"
        @close="setSelectRSurveyorDialogVisible(false)"
      />
      <!-- 承诺书弹窗 -->
      <count-down-dialog
        :visible.sync="countDownDialog.visible"
        :title="countDownDialog.title"
        :count-down-type="countDownDialog.countDownType"
        :count-down-btn="countDownDialog.countDownBtn"
        :count-down-time="countDownDialog.countDownTime"
        :business-class="countDownDialog.businessClass"
        :width="countDownDialog.width"
        :class-name="countDownDialog.className"
        :is-promise="countDownDialog.isPromise"
        @submit="confirmPromiseInfo"
      />
      <!-- 图例弹窗 -->
      <legend-dialog
        :title="legendDialog.title"
        :visible.sync="legendDialog.visible"
        :licence-type="legendDialog.legendType"
      />
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import ViewInfo from "./viewInfo.vue";
import Result from "./result.vue";
import LegendDialog from "components/business/legendDialog/index.vue";
import CountDownDialog from "components/business/CountDownDialog/index.vue";
// mixins
import BusinessMixin from "mixins/business/index.js";
import CheckLineMixin from "mixins/business/sz-check-line.js";
import SurveyResultMixin from "mixins/business/survey-result.js";
import SelectMappingCompanyMixin from "mixins/business/select-mapping-company.js";
import SelectRegisteredSurveyorMixin from "mixins/business/select-registered-survey.js";
import AttachmentDownload from "mixins/attachment/download.js";
// 校验
import {
  validateBusinessName,
  validateAttachment,
  validateGroundCode,
} from "utils/form.js";

// Api
import Api from "api/business/index.js";

export default {
  name: "CouncilPlanningCheck",
  components: {
    ViewInfo,
    Result,
    LegendDialog,
    CountDownDialog,
  },
  mixins: [
    BusinessMixin,
    CheckLineMixin,
    SurveyResultMixin,
    SelectMappingCompanyMixin,
    SelectRegisteredSurveyorMixin,
    AttachmentDownload,
  ],
  data() {
    return {
      // 业务内容信息，不同业务内容不同
      ContentInfo: {
        ProjectPlanPermission: [],
      },
      // 成果审核反馈
      auditFeedback: [],
      // 步骤1
      form1: {
        BusinessName: null,
        // GroundCode: null,
        ProjectPlanPermission: [],
        CouncilLicenceImg: [],
        RedLineDrawingImg: [],
        Others: [],
      },
      rules1: {
        BusinessName: [
          { required: true, validator: validateBusinessName, trigger: "blur" },
        ],
        ProjectPlanPermission: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (
                !this.form1.ProjectPlanPermission ||
                !this.form1.ProjectPlanPermission.length
              ) {
                callback(new Error("请添加工程规划许可证"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        CouncilLicenceImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "CouncilLicenceImg",
                "市政工程规划许可证件"
              ),
            trigger: "change",
          },
        ],
        RedLineDrawingImg: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form1",
                "RedLineDrawingImg",
                "施工红线图"
              ),
            trigger: "change",
          },
        ],
      },
      // 步骤3
      form3: {
        Report: [],
      },
      rules3: {
        Report: [
          {
            required: true,
            validator: (rule, value, callback) =>
              validateAttachment(
                rule,
                value,
                callback,
                this,
                "form3",
                "Report",
                "放线报告"
              ),
            trigger: "change",
          },
        ],
      },
      // 宗地号、工程规划许可证图例弹窗
      legendDialog: {
        visible: false,
        legendType: "宗地号",
        title: "查看图例",
      },
      // 承诺书弹窗
      countDownDialog: {
        visible: false,
        businessClass: null,
        title: "承诺书",
        countDownType: "承诺书",
        countDownBtn: "我承诺",
        countDownTime:
          process.env.VUE_APP_ENV === "development" ||
          process.env.VUE_APP_ENV === "practice"
            ? 3
            : 15,
        width: "600px",
        className: "promise",
        isPromise: true,
      },
      isConfirm: false,
      acceptText: "验收完成",
      getDataTimer: null,
    };
  },
   destroyed() {
    this.clearTimer(this.getDataTimer);
  },
  methods: {
    // 额外处理请求数据
    handleApiData(Data) {
      const { ProjectPlanPermission } = Data.ContentInfo;
      this.ContentInfo = {
        ...Data.ContentInfo,
        ProjectPlanPermission: ProjectPlanPermission
          ? JSON.parse(ProjectPlanPermission)
          : [],
      };
    },
    refreshStep(Data, step = null) { //覆盖mixins/business/index.js里的该方法
      const {
        BaseInfo,
        ContentInfo,
        FlowInfo,
        ActionsInfos,
        Attachments,
        CurrentAction
      } = Data

      this.BaseInfo = BaseInfo
      this.Attachments = Attachments
      this.CurrentAction = CurrentAction
      this.FlowInfo = FlowInfo
      this.ActionsInfos = ActionsInfos
      const ExtendInfo = JSON.parse(BaseInfo.ExtendInfo)
      this.ExtendInfo = { ...this.ExtendInfo, ...ExtendInfo }

      // 额外处理请求数据
      this.handleApiData(Data)

      const Actions = [...FlowInfo.FlowActionInfo.Actions]
      const index = Actions.findIndex((e) => e.ID === CurrentAction.ActionId)

      const currentStep = step || (index > 0 ? index + 1 : 1)
      this.handleStepList(currentStep, FlowInfo.FlowActionInfo.Actions)

      if (ContentInfo) {
        this.handleFormData(currentStep)
      }

      // 当成果备案步骤还在办理状态时
      if (currentStep === 5 && BaseInfo.StateCode !== 2) {
        this.getResultData();
      }
    },
    // 处理表单数据
    handleFormData(currentStep) {
      const { BaseInfo, ContentInfo, Attachments } = this;
      const { BusinessName } = BaseInfo;
      const { GroundCode, ProjectPlanPermission } = ContentInfo;

      // 处理附件
      let CouncilLicenceImg = [];
      let RedLineDrawingImg = [];
      let Report = [];
      let Others = [];
      this.auditFeedback = [];

      if (Attachments.length) {
        Attachments.forEach((e) => {
          if (e.AttachmentType === "申请材料附件") {
            switch (e.AttachmentCategories) {
              case "市政工程规划许可证件":
                CouncilLicenceImg.push(e);
                break;
              case "施工红线图":
                RedLineDrawingImg.push(e);
                break;
              case "其他附件":
                Others.push(e);
                break;
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
          if (e.AttachmentType === "项目成果附件") {
            switch (e.AttachmentCategories) {
              case "放线报告":
                Report.push(e);
                break;
              case "不动产预核测绘成果审核反馈":
                this.auditFeedback.push(e);
              default:
                this.GLOBAL.logInfo("附件分类AttachmentCategories不存在");
            }
          }
        });
      }

      this.form1 = {
        BusinessName,
        // GroundCode,
        ProjectPlanPermission,
        CouncilLicenceImg,
        RedLineDrawingImg,
        Others,
      };

      this.form3 = {
        Report,
      };
    },
    // 处理要提交的业务数据
    handleBusinessData() {
      const { businessID, form1 } = this;
      const { BusinessName, GroundCode, ProjectPlanPermission } = form1;

      this.BaseInfo.BusinessName = BusinessName;

      const data = {
        BaseInfo: this.BaseInfo,
        ContentInfo: {
          ID: businessID,
          // GroundCode,
          ProjectPlanPermission: JSON.stringify(ProjectPlanPermission),
        },
      };

      return data;
    },
    // 显示成果数据
    showResult() {
      const { currentStep } = this;

      if (currentStep === 4 || currentStep === 5) {
        return true;
      }

      return false;
    },
    // 检查doc和pdf
    checkDOCandPDF(file) {
      const fileName = file.name;
      const suffix = fileName.substring(fileName.length - 5);

      // 文件格式
      if (file.type === "application/pdf") return true;
      if (file.type === "application/msword") return true;
      if (suffix.indexOf(".docx") >= 0) return true;

      this.$message.warning("文件只能是 doc / docx / pdf 格式");
      return false;
    },
    // 禁用提交验收和确认通过（测试按钮）
    disabledSubmitAccept() {
      return !this.form3.Report.length;
    },
    //查看图例
    viewLegend(name) {
      console.log(name);
      this.legendDialog.visible = true;
      this.legendDialog.legendType = name;
      this.legendDialog.title = `查看【${name}】图例`;
      console.log(this.legendDialog.visible);
    },
    //验收完成
    confirmAccept() {
      if (this.isConfirm) {
        this.accept();
      } else {
        this.countDownDialog.businessClass = this.BaseInfo.BusinessClass;
        this.countDownDialog.visible = true;
      }
    },
    //承诺书
    confirmPromiseInfo(isConfirm) {
      this.isConfirm = isConfirm;
      if (this.isConfirm) {
        this.acceptText = "提交验线";
      }
    },
    // 获取成果数据
    getResultData() {
      if (this.getDataTimer) {
        this.clearTimer(this.getDataTimer);
      }
      this.getDataTimer = setInterval(() => {
        Api.GetBusiness(this.businessID)
          .then((res) => {
            const { StateCode, Data, Message } = res;
            if (StateCode === 1) {
              if (Data.BaseInfo.StateCode === 2) {
                this.refreshStep(Data);

                this.clearTimer(this.getDataTimer);
              }
            } else {
              this.clearTimer(this.getDataTimer);
              this.$message.warning(Message);
            }
          })
          .catch((err) => {
            console.log(err);
            this.clearTimer(this.getDataTimer);
          });
      }, 5000);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";

.company-table {
  /deep/ .el-table--medium {
    th,
    td {
      padding: 5px 0;
    }
  }
}
.el-icon-question {
  color: $color-primary;
  cursor: pointer;
  padding-left: 2px;
}
</style>
