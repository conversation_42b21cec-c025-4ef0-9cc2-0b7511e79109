<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="`${row ? '编辑' : '添加'}${title}`"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    width="1200px"
    @close="close"
    class="project-licence-store-container"
  >
    <el-form
      v-if="form"
      ref="form"
      :rules="rules"
      class="licence-form"
      :model="form"
      label-width="0"
    >
      <table class="table" cellpadding="0" cellspacing="0">
        <tr>
          <th colspan="3">
            <span class="required">工程规划许可证号</span>
          </th>
          <td colspan="13">
            <el-form-item prop="Code" class="mb-15">
              <el-input
                v-if="businessClass === 'RealEstatePreCheckSurveyAutoFlow'"
                v-model.trim="form.Code"
                placeholder="请输入工程规划许可证号(示例：450021202100001)"
              ></el-input>
              <el-input
                v-else
                v-model.trim="form.Code"
                placeholder="请输入工程规划许可证号(示例：450101201001244)"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <th colspan="3">
            <span class="required">建设单位</span>
          </th>
          <td colspan="7">
            <el-form-item prop="ConstructCompany" class="mb-15">
              <el-input
                v-model="form.ConstructCompany"
                placeholder="请输入建设单位"
              ></el-input>
            </el-form-item>
          </td>
          <th colspan="2">
            <span class="required">建设地址</span>
          </th>
          <td colspan="4">
            <el-form-item prop="Address" class="mb-15">
              <el-input
                v-model="form.Address"
                placeholder="请输入建设地址"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <th colspan="3">
            <span class="required">项目名称</span>
          </th>
          <td colspan="7">
            <el-form-item prop="ProjectName" class="mb-15">
              <el-input
                v-model="form.ProjectName"
                placeholder="请输入项目名称"
              ></el-input>
            </el-form-item>
          </td>
          <th colspan="2">
            <span class="required">
              附图编号
              <br />&nbsp;&nbsp;&nbsp;（项目编号）
            </span>
          </th>
          <td colspan="4">
            <el-form-item prop="AppendixImgNumber" class="mb-15">
              <el-input
                v-model.trim="form.AppendixImgNumber"
                placeholder="请输入编号(示例：GC0101201001700)"
              ></el-input>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <template v-if="form.Buildings.length">
            <th rowspan="2" class="th-fixed-width">建筑类别</th>
            <th rowspan="2" class="th-fixed-width">
              <div>面积</div>
              <div class="mt-5">(㎡)</div>
            </th>
            <th rowspan="2" class="th-fixed-width">
              <div>操作</div>
              <div class="mt-5"></div>
            </th>
          </template>
          <template v-else>
            <th rowspan="2" class="th-fixed-width">建筑类别</th>
            <th rowspan="2" colspan="2" class="th-fixed-width">
              <div>面积</div>
              <div class="mt-5">(㎡)</div>
            </th>
          </template>
          <th rowspan="2">建筑性质</th>
          <th rowspan="2">建筑结构</th>
          <th colspan="2">
            <div>总建筑面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
          <th rowspan="2">
            <div>基底面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
          <th colspan="2">层数</th>
          <th rowspan="2">
            <span class="required">建筑总高度(m)</span>
          </th>
          <th rowspan="2">
            <span class="required">栋数</span>
          </th>
          <th rowspan="2">
            <div>投资</div>
            <div class="mt-5">(万元)</div>
          </th>
          <!-- <th colspan="3">套型</th> -->
          <th colspan="3">其他</th>
        </tr>
        <tr>
          <th>
            <span class="required">地上</span>
          </th>
          <th>地下</th>
          <th>
            <span class="required">地上</span>
          </th>
          <th>地下</th>
          <th>名称</th>
          <th>层数</th>
          <th>
            <div>面积</div>
            <div class="mt-5">(㎡)</div>
          </th>
        </tr>
        <tr>
          <td colspan="3" style="padding: 0; vertical-align: top">
            <div
              class="table-div"
              v-for="(item, index) in form.Buildings"
              :key="'BuildingType' + index"
            >
              <div class="th-fixed-width">
                <el-input
                  type="textarea"
                  v-model="item.Type"
                  placeholder="请输入"
                  autosize
                ></el-input>
              </div>
              <div class="th-fixed-width">
                <el-input
                  type="textarea"
                  v-model.trim="item.Area"
                  placeholder="请输入"
                  autosize
                ></el-input>
              </div>
              <div
                class="table-del-btn"
                @click="delBuildings(index)"
                style="vertical-align: middle"
              >
                <i class="el-icon-circle-close mr-5"></i>删除
              </div>
            </div>
            <div class="table-add-btn" @click="addBuildings">
              <i class="el-icon-plus"></i>添加
            </div>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model="form.BuildingNature"
              placeholder="请输入"
              autosize
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model="form.BuildingStructure"
              placeholder="请输入"
              autosize
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.TotalArea.Aboveground"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'TotalArea', 'Aboveground')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.TotalArea.Underground"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'TotalArea', 'Underground')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.BaseArea"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'BaseArea')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.Floors.Aboveground"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'Floors', 'Aboveground')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.Floors.Underground"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'Floors', 'Underground')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.BuildingHeight"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'BuildingHeight')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.BuildingBlockNumber"
              :min="1"
              placeholder="请输入"
              @change="limitMinValue($event, 'BuildingBlockNumber')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.Invest"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'Invest')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model="form.Others.Name"
              placeholder="请输入"
              autosize
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.Others.Number"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'Others', 'Number')"
            ></el-input>
          </td>
          <td>
            <el-input
              type="textarea"
              v-model.trim="form.Others.Area"
              :min="0"
              placeholder="请输入"
              @change="limitMinValue($event, 'Others', 'Area')"
            ></el-input>
          </td>
        </tr>
        <tr>
          <th colspan="3">备注</th>
          <td colspan="13">
            <el-input
              type="textarea"
              v-model="form.Remark"
              placeholder="请输入"
              autosize
            ></el-input>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit('form')"
        >确认{{ row ? "编辑" : "添加" }}</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// 验证
import { validProjectLicencee, validAppendixImgNumber,validAgrLicencee} from "utils/validate";
// Api
import Api from "api/business/index.js";

export default {
  name: "ProjectLicenceStoreDialog",

  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前项
    row: {
      type: Object,
      default: "",
    },
    // 索引
    index: {
      type: Number,
      default: -1,
    },
    // 记录列表
    list: {
      type: Array,
      default: () => [],
    },
    // 标题
    title: {
      type: String,
      default: "工程规划许可证",
    },
    // 工规证类型
    licenceType: {
      type: String,
      default: "放线",
    },
    // 业务逻辑类
    businessClass: {
      type: String,
      default: null,
    },
  },
  data() {
    const validateCode = (rule, value, callback) => {
      // AgrLicenceNo
      const { businessClass } = this;
      //不动产预核业务即时办理流程
      if (businessClass === "RealEstatePreCheckSurveyAutoFlow") {
          if (!value) {
          callback(new Error("请输入工程规划许可证号"));
        } 
        else if (!validAgrLicencee(value)) {
          callback(new Error("格式错误，示例：450021202100001"));
        } 
        else {
          callback();
        }
      } else {
        if (!value) {
          callback(new Error("请输入工程规划许可证号"));
        } else if (!validProjectLicencee(value)) {
          callback(new Error("格式错误，示例：450101201001244"));
        } else {
          callback();
        }
      }
    };

    // const validAppendixNumber = (rule, value, callback) => {
    //   if (!value) {
    //     callback(new Error("请输入附图编号（项目编号）"));
    //   } else if (!validAppendixImgNumber(value)) {
    //     callback(new Error("格式错误，示例：GC0101201001700"));
    //   } else {
    //     callback();
    //   }
    // };

    return {
      defaultLicenceForm: {
        //是否自行添加
        IsAddNew:true,
        // 工程规划许可证号
        Code: "",
        // 建设单位
        ConstructCompany: "",
        // 建设地址
        Address: "",
        // 项目名称
        ProjectName: "",
        // 附图编号
        AppendixImgNumber: "",
        // 不同建筑类别
        Buildings: [
          // {
          //   // 建筑类别
          //   Type: "",
          //   // 面积(㎡)
          //   Area: ""
          // }
        ],
        // 建筑性质
        BuildingNature: "",
        // 建筑结构
        BuildingStructure: "",
        // 建筑总面积
        TotalArea: {
          //地上
          Aboveground: "",
          // 地下
          Underground: 0,
        },
        // 基地面积
        BaseArea: "",
        // 层数
        Floors: {
          // 地上
          Aboveground: "",
          // 地下
          Underground: 0,
        },
        // 建筑总高度
        BuildingHeight: "",
        // 栋数
        BuildingBlockNumber: "",
        // 投资（万元）
        Invest: "",
        // 套型
        SetType: [
          // {
          //   // 套型
          //   Name: "",
          //   // 套数
          //   Number: "",
          //   // 面积
          //   Area: ""
          // }
        ],
        // 其他
        Others: {
          // 名称
          Name: "",
          // 层数
          Number: 0,
          // 面积
          Area: "",
        },
        // 备注
        Remark: "",
        Add: true,
      },
      form: "",
      // 没修改前的建筑类别
      defaultBuildings: [],
      rules: {
        Code: [
          {
            required: true,
            validator: validateCode,
            trigger: "blur",
          },
        ],
        ConstructCompany: [
          {
            required: true,
            message: "请输入建设单位",
            trigger: "blur",
          },
        ],
        Address: [
          {
            required: true,
            message: "请输入建设地址",
            trigger: "blur",
          },
        ],
        ProjectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        AppendixImgNumber: [
          {
            required: true,
            // validator: validAppendixNumber,
            message: "请输入附图编号（项目编号）",
            trigger: "blur",
          },
        ],
      },
      NumberReg: /^[0-9]+.?[0-9]*$/,
      edit: false,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init("工程规划许可证");
      }
    },
  },
  methods: {
    // 初始化
    init(name) {
      const { row, defaultLicenceForm } = this;
      this.form = JSON.parse(JSON.stringify(row ? row : defaultLicenceForm));
      this.defaultBuildings = this.row ? [...this.row.Buildings] : [];
      console.log(this);
    },
    // 重置表单
    reset() {
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    // 关闭存储弹窗
    close() {
      const { form, edit, index, defaultBuildings } = this;

      this.reset();
      this.edit = false;
      this.$emit("cancel");
      this.$emit("update:visible", false);
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { form, index, row, list } = this;
          const {
            Buildings,
            TotalArea,
            Floors,
            BuildingHeight,
            BuildingBlockNumber,
          } = form;

          let error = false;

          if (Buildings.length) {
            for (let i = 0; i < Buildings.length; i++) {
              let e = Buildings[i];
              if (!e.Type) {
                this.$message.warning(`请输入建筑类别`);
                error = true;
                break;
              }
              if (!e.Area) {
                this.$message.warning(`请输入建筑类别为${e.Type}的面积`);
                error = true;
                break;
              }
            }

            if (error) {
              return false;
            }
          }
          if (!TotalArea.Aboveground) {
            this.$message.warning("请输入地上总建筑面积");
            return false;
          }
          // if (!TotalArea.Underground) {
          //   this.$message.warning("请输入地下总建筑面积");
          //   return false;
          // }
          if (!Floors.Aboveground) {
            this.$message.warning("请输入地上层数");
            return false;
          }

          if (isNaN(Floors.Aboveground) == true) {
            this.$message.warning("地上层数请输入小数");
            return false;
          }
          // if (!Floors.Underground) {
          //   this.$message.warning("请输入地下层数");
          //   return false;
          // }
          if (Floors.Underground && isNaN(Floors.Underground) == true) {
            this.$message.warning("地下层数请输入小数");
            return false;
          }
          if (!BuildingHeight) {
            this.$message.warning("请输入建筑总高度");
            return false;
          }
          if (!BuildingBlockNumber) {
            this.$message.warning("请输入栋数");
            return false;
          } else {
            if (BuildingBlockNumber.indexOf(".") >= 0) {
              this.$message.warning("栋数请输入整数");
              return false;
            }
          }

          let params = { ...form };

          const isExist = list.find((e) => e.Code === form.Code);
          if (isExist && !row) {
            this.$message.warning("该工程规划许可证已存在");
            return false;
          }

          this.edit = true;
          this.$emit("submit", params, index);
          this.$emit("update:visible", false);
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
    limitMinValue(value, attr, attr2 = "") {
      if (value < 0 || !this.NumberReg.test(value)) {
        if (attr2) {
          this.form[attr][attr2] = 0;
          return;
        }

        this.form[attr] = 0;
      }
    },
    addBuildings() {
      this.form.Buildings.push({ Type: "", Area: "" });
    },
    delBuildings(index) {
      this.form.Buildings.splice(index, 1);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-bottom: 10px;
  padding-top: 10px;
}

.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  th {
    background: #f8f8f8;
    color: #909399;
  }
  th,
  td {
    text-align: center;
    padding: 5px 0;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
  }

  td {
    .table-div:last-child {
      div {
        border-bottom: none;
      }
    }
  }

  .th-fixed-width {
    min-width: 65px;
    width: 65px;
    max-width: 65px;
  }

  &-add-btn {
    // border: 1px dashed #dfe6ec;
    width: 100%;
    // border-radius: 4px;
    text-align: center;
    margin-top: -1px;
    height: 45px;
    line-height: 45px;
    cursor: pointer;
    position: relative;
    z-index: 10;
    color: $color-primary;
    margin-top: -1px;

    // &:hover {
    //   border-color: $color-primary;
    // }

    & > i {
      margin-right: 5px;
    }
  }

  &-del-btn {
    min-width: 65px;
    padding: 5px;
    line-height: inherit;
    color: $color-primary;
    cursor: pointer;
  }
}

.table-div {
  width: 100%;
  display: table;
  line-height: 1;
  min-height: 35px;

  div {
    display: table-cell;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    padding: 5px 0;

    &:last-child {
      border-right: none;
    }
  }
}

.licence-form {
  /deep/ .el-input__inner {
    border: none;
    padding: 5px 10px;
  }

  /deep/ .el-input {
    border: none;
    padding: 0;
  }

  /deep/ .el-textarea {
    border: none;
  }

  /deep/ .el-textarea__inner {
    border: none;
    padding: 5px;
    min-height: 40px !important;
    // height: auto !important;
  }

  /deep/ .el-form-item {
    margin-bottom: 0;
    padding: 0;
    border: none;
  }
  /deep/ .el-form-item__error {
    padding-left: 10px;
  }
}

.td-input {
  /deep/ .el-form-item__error {
    padding-left: 0;
  }
}

.required {
  &::before {
    content: "*";
    color: #ff4949;
    margin-right: 4px;
  }
}
</style>