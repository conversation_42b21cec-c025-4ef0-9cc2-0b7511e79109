<template>
  <!-- eslint-disable -->
  <div class="business-list-container">
    <!-- 分类 -->
    <div class="classify flex">
      <ul class="tabs flex">
        <li
          v-for="(item, index) in tabs"
          :key="'tab' + index"
          class="tabs-item"
          :class="{ 'is-active': activeTabIndex === index }"
          @click="tabChange(index)"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="search">
        <el-input
          placeholder="请输入业务编号、业务名称、单位名称"
          v-model="key"
          clearable
          @clear="getList(1, page.pageSize)"
          style="width:400px"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="search"
          ></el-button>
        </el-input>
      </div>
    </div>
    <!-- 表格 -->
    <dynamic-table
      ref="businessTbable"
      class="mt-20"
      v-loading="listLoading"
      element-loading-text="加载中，请稍等..."
      :table-header="tableHeader"
      :table-data="listData"
      :default-props="tableProps"
      :row-class-name="tableRowClassName"
      :showPagination="true"
      :total="page.total"
      :page-no.sync="page.pageNo"
      :page-size.sync="page.pageSize"
      :page-sizes.sync="page.pageSizes"
      @pagination="getList"
    >
      <el-table-column width="50" label="序号" align="center">
        <template slot-scope="{ $index }">{{
          $index + 1 + page.pageSize * (page.pageNo - 1)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="BusinessNumber"
        label="业务编号"
        width="150"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="BusinessName"
        label="业务名称"
        width="200"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="BusinessType"
        label="业务类型"
        width="200"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="LinkName"
        label="当前环节"
        width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="DeveloperName"
        label="业主单位"
        width="250"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="SurveyCompanyName"
        label="测绘单位"
        width="250"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="CreateTime"
        label="创建时间"
        width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="StartTime"
        label="开始时间"
        width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="SignatureTime"
        label="签收时间"
        width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="FinishTime"
        label="完成时间"
        width="170"
        align="center"
      >
        <template slot-scope="{ row }">
          <span v-if="row.StateCode === 2">{{ row.FinishTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="FinishTime"
        label="关闭时间"
        width="170"
        align="center"
      >
        <template slot-scope="{ row }">
          <span v-if="row.StateCode === 4">{{ row.FinishTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="StateCode"
        width="100"
        label="状态"
        fixed="right"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.StateCode === 0" class="tag-yellow" effect="dark"
            >待签收</el-tag
          >
          <el-tag v-if="row.StateCode === 1" class="tag-blue" effect="dark"
            >办理中</el-tag
          >
          <el-tag v-if="row.StateCode === 2" type="success" effect="dark"
            >已完成</el-tag
          >
          <el-tag v-if="row.StateCode === 3" type="error" effect="dark"
            >已退回</el-tag
          >
          <el-tag v-if="row.StateCode === 4" type="info" effect="dark"
            >已关闭</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="action"
        label="操作"
        width="120"
        fixed="right"
        align="center"
      >
        <template slot-scope="{ row, $index }">
          <el-button
            type="text"
            icon="el-icon-document"
            @click="view(row, $index)"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </dynamic-table>
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import DynamicTable from "components/common/Table/DynamicTable";
// mixins
import Page from "mixins/page.js";
import Table from "mixins/table.js";
import MyBusiness from "mixins/my-business/index.js";
// Api
import Api from "api/my-business/index.js";

export default {
  name: "MyBusiness",
  components: { DynamicTable },
  mixins: [Page, Table, MyBusiness],
  computed: {
    // 获取列表接口
    apiGetList(PageIndex, PageSize) {
      return (PageIndex, PageSize) =>
        Api.GetMyBusinessList(
          this.key,
          1,
          PageIndex,
          PageSize,
          this.tabs[this.activeTabIndex].status
        );
    },
  },
  data() {
    return {
      // tab
      activeTabIndex: 0,
      tabs: [
        {
          name: "全部",
          attr: "Total",
          num: 0,
          status: -1,
        },
        {
          name: "待签收",
          attr: "DQS",
          num: 0,
          status: 0,
        },
        {
          name: "办理中",
          attr: "BLZ",
          num: 0,
          status: 1,
        },
        {
          name: "已完成",
          attr: "YWC",
          num: 0,
          status: 2,
        },
        {
          name: "已退回",
          attr: "YTH",
          num: 0,
          status: 3,
        },
        {
          name: "已关闭",
          attr: "YGB",
          num: 0,
          status: 4,
        },
      ],
    };
  },
  methods: {
    // 设置表格样式
    tableRowClassName({ row, rowIndex }) {
      return row.StateCode == 4 ? "disabled" : "";
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/my-business.scss";
</style>
