<template>
  <!-- eslint-disable -->
  <el-card class="company-register-container overspread-card" shadow="never">
    <el-form ref="form" :model="form" :rules="rules" label-width="150px">
      <description title="基本信息" style="margin-top: -20px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="单位名称：" prop="CompanyName">
              <el-input
                v-model.trim="form.CompanyName"
                placeholder="请输入单位名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位类型：">
              <!-- <el-input v-model.trim="form.CompanyType" disabled></el-input> -->
              <el-tag
                v-if="form.CompanyType"
                :class="form.CompanyType == '测绘单位' ? '' : 'tag-blue'"
                >{{ form.CompanyType }}</el-tag
              >
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="统一社会信用代码：" prop="CreditCode">
              <el-input
                v-model.trim="form.CreditCode"
                placeholder="请输入统一社会信用代码"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </description>
      <description title="法定代表人信息">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="姓名：" prop="LegalPersonName">
              <el-input
                v-model.trim="form.LegalPersonName"
                placeholder="请输入法定代表人姓名"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号：" prop="LegalPersonNumber">
              <el-input
                v-model.trim="form.LegalPersonNumber"
                placeholder="请输入法定代表人身份证号"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </description>
      <description title="联系方式">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人：" prop="Contacter">
              <el-input
                v-model.trim="form.Contacter"
                placeholder="请输入联系人姓名"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话：" prop="ContacterPhone">
              <el-input
                v-model.trim="form.ContacterPhone"
                placeholder="请输入联系电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="单位地址：" prop="CompanyAddress">
              <el-input
                v-model="form.CompanyAddress"
                placeholder="请输入地址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </description>
      <div style="text-align:center;">
        <el-button
          type="primary"
          @click="submitForm('form')"
          :loading="saveLoading"
          >下一步</el-button
        >
      </div>
    </el-form>
  </el-card>
</template>

<script>
/* eslint-disable */
// 验证
import {
  validRealName,
  validBusinessLicence,
  validIDcard,
  validPhone,
} from "utils/validate";
// Api
import Api from "api/company-info/index.js";
import UserApi from "api/user/index.js";
// vuex
import { mapGetters } from "vuex";
// 工具
import { getInfoByIDCard } from "utils/index.js";
import store from "@/store";

export default {
  name: "CompanyRegister",
  computed: {
    ...mapGetters(["personName", "userName", "personNo", "roles"]),
  },
  data() {
    const validateRealName = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入真实姓名"));
      } else if (!validRealName(value)) {
        callback(new Error("真实姓名格式错误"));
      } else {
        callback();
      }
    };

    const validateBusinessLicence = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入统一社会信用代码"));
      } else if (!validBusinessLicence(value)) {
        callback(new Error("统一社会信用代码格式错误"));
      } else {
        callback();
      }
    };

    const validateIDcard = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入身份证号"));
      } else if (!validIDcard(value)) {
        callback(new Error("身份证号格式错误"));
      } else {
        callback();
      }
    };

    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入联系电话"));
      } else if (!validPhone(value)) {
        callback(new Error("联系电话格式有误"));
      } else {
        callback();
      }
    };

    return {
      // loading
      saveLoading: false,
      readLoading: false,
      // 表单
      form: {
        CompanyName: null,
        CompanyType: null,
        CreditCode: null,
        LegalPersonName: null,
        LegalPersonNumber: null,
        Contacter: null,
        ContacterPhone: null,
        CompanyAddress: null,
      },
      // 规则
      rules: {
        CompanyName: [
          { required: true, message: "请输入单位名称", trigger: "blur" },
        ],
        CreditCode: [
          {
            required: true,
            validator: validateBusinessLicence,
            trigger: "blur",
          },
        ],
        LegalPersonName: [
          { required: true, validator: validateRealName, trigger: "blur" },
        ],
        LegalPersonNumber: [
          {
            required: true,
            validator: validateIDcard,
            trigger: "blur",
          },
        ],
        Contacter: [
          { required: true, validator: validateRealName, trigger: "blur" },
        ],
        ContacterPhone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        CompanyAddress: [
          { required: true, message: "请输入单位地址", trigger: "blur" },
        ],
      },
      // 是否包含当前申请人
      includeCurrentApplyer: false,
      // 人员信息
      CompanyEmployees: [],
      // 默认单位信息
      defaultCompany: {
        stateCode: 0,
        companyType: null,
        companyName: null,
        companyID: null,
      },
    };
  },
  beforeRouteEnter(to, from, next) {
    if (store.getters.companyID && store.getters.companyType) {
      next("/user/company-register/info");
    }
    // 已通过刷新页面，解决store未获取到最新状态导致“已通过的单位仍进到单位注册页面”的问题
    else if (store.getters.companyCode === 3) {
      window.location.href = window.location.href;
      window.location.reload;
    } else next();
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });

      this.getRegisterInfo();

      this.form.CompanyType = "测绘单位";
      this.form.Contacter = this.personName;
      this.form.ContacterPhone = this.userName;
    },
    // 获取正在申请的单位注册信息
    async getRegisterInfo() {
      const { defaultCompany, roles, $store, $router, $message } = this;

      try {
        const companyInfo = await UserApi.GetCompanyInfoByUserInfo();

        // 已绑定单位
        if (companyInfo.StateCode === 1 && companyInfo.Data) {
          const { CompanyList, CompanyType } = companyInfo.Data;

          let company = { ...defaultCompany };

          if (CompanyList.length) {
            let currentCompany = CompanyList[0];

            const { CID, CName, CType, CRole, State } = currentCompany;

            company = {
              stateCode: State,
              companyType: CType, // 因为只有测绘单位注册，所以类型都是测绘单位
              companyName: CName,
              companyID: CID,
              companyRole: CRole,
            };

            // 审核通过进入“单位信息”页面
            if (State === 3) {
              // 修改单位信息
              await $store.dispatch("user/setCompany", company);
              await $store.dispatch("user/setCompanyList", CompanyList);

              let newRoles = [...roles];
              newRoles.splice(newRoles.findIndex((e) => e === "1"), 1);

              // 已完成注册
              let role =
                CompanyType === "测绘单位" ? ["c-m", "2"] : ["c-d", "2"];
              newRoles = newRoles.concat(role);

              // 修改路由权限
              await $store.dispatch("permission/generateRoutes", {
                roles: newRoles,
              });
              // 修改角色
              await $store.dispatch("user/setRoles", newRoles);

              $message.success(`您的账号已绑定【${CName}】`);
              $router.push({ name: "CompanyInfo" });
            } else {
              $router.push({ name: "CompanyRegisterInfo" });
            }
          }
        } else {
          return false;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.setCurrentUserAsAdimin();
          const CompanyBaseInfo = { ...this.form };
          const { userName, personName, CompanyEmployees } = this;
          const { CompanyType, CompanyName, CreditCode } = this.form;

          // 添加其他要传的参数
          let params = {
            CreateUserName: userName,
            CreateUserPersonNo: personName,
            CompanyType,
            CompanyName,
            CompanyNo: CreditCode,
            DetailInfo: JSON.stringify({
              CompanyBaseInfo,
              CompanyEmployees,
            }),
          };

          this.saveLoading = true;

          Api.CompanyRegister(params)
            .then((res) => {
              const { StateCode, Data, Message } = res;
              if (StateCode == 1) {
                const { CompanyType, CompanyName, ID } = Data;

                // 修改单位信息
                this.$store.dispatch("user/setCompany", {
                  stateCode: StateCode,
                  companyType: CompanyType,
                  companyName: CompanyName,
                  companyID: ID,
                });

                // 下一步完善全部信息
                this.$router.push({ name: "CompanyRegisterInfo" });
              } else {
                this.$message.error(Message);
              }

              this.saveLoading = false;
            })
            .catch((err) => {
              console.log(err);
              this.saveLoading = false;
            });
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
    // 添加当前申请人为单位管理员
    setCurrentUserAsAdimin() {
      this.includeCurrentApplyer = false;
      const { CompanyEmployees, userName, personName, personNo } = this;

      const { gender, age } = personNo
        ? getInfoByIDCard(personNo)
        : { gender: null, age: null };

      if (!CompanyEmployees.length) {
        this.CompanyEmployees.push({
          PersonRole: "单位管理员",
          CompanyName: this.form.CompanyName,
          PersonName: personName,
          PersonNumber: personNo,
          PersonPhone: userName,
          PersonSex: gender,
          PersonAge: age,
        });
      } else {
        this.CompanyEmployees.forEach((e) => {
          if (e.PersonNumber == personNo) this.includeCurrentApplyer = true;
        });
        if (!this.includeCurrentApplyer) {
          this.CompanyEmployees.push({
            PersonRole: "单位管理员",
            CompanyName: this.form.CompanyName,
            PersonName: personName,
            PersonNumber: personNo,
            PersonPhone: userName,
            PersonSex: gender,
            PersonAge: age,
          });
        }
      }
    },
  },
};
</script>
