<template>
  <!-- eslint-disable -->
  <div class="maintain-container">
    <div class="announcement-container">
      <el-row>
        <el-col class="announcement__wrapper" :xs="24" :sm="24" :md="20" :lg="20" :xl="20">
          <div class="announcement__box">
            <h1>公告</h1>
            <div id="countdown"></div>
            <hr />
            <div class="row">
              <div class="col-md-12 text-left">
                <h2 style="text-align: center">关于系统暂停服务的通知</h2>
                <h3 style="margin-bottom: 0">尊敬的办事群众、各有关单位：</h3>
                <h3
                  style="text-indent: 2em;"
                >2020年9月30日（星期三）20:00至10月4日（星期日）20:00南宁市自然资源局将对互联网网络安全进行升级。届时，南宁市不动产登记信息系统、南宁市不动产登记综合服务（“邕e登”）平台及“邕e登”APP、“爱南宁”APP的不动产登记业务相关功能、各受理点“24小时不打烊”自助综合机、自助查档机、自助打证机将暂停服务。由此带来的不便，敬请谅解。</h3>
                <h3 style="text-indent: 2em">特此通知</h3>
                <h3 style="text-align:right">
                  南宁市不动产登记中心
                  <br />2020年9月30日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </h3>
              </div>
            </div>
            <hr />
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 轮播图 -->
    <div class="banner-container">
      <div style="position: relative;">
        <div class="tp-banner">
          <ul>
            <li v-for="(item, index) in bannerList" :key="index" data-transition="fade" data-start="0">
              <img :src="item.imgUrl" :data-lazyload="item.imgUrl" data-fullwidthcentering="on" />
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "SysMaintain",
  data() {
    return {
      bannerList: [
        {
          imgUrl: require("@/assets/cover-images/1.jpg")
        },
        {
          imgUrl: require("@/assets/cover-images/2.jpg")
        },
        // {
        //   imgUrl: require("@/assets/cover-images/3.jpg")
        // },
        // {
        //   imgUrl: require("@/assets/cover-images/4.jpg")
        // }
      ]
    };
  },
  mounted() {
    $(".tp-banner").revolution({
      delay: 3000,
      startwidth: 1170,
      startheight: 800,
      navigationType: "none",
      navigationArrows: "none"
    });
  }
};
</script>
<style lang="scss" scoped>
/deep/ .tp-bannertimer {
  display: none;
}

/deep/ .tp-bgimg {
  background-position: 100% 100% !important;
  background-size: 100% 100% !important;
  height: 800px !important;
}
/deep/ .tp-banner {
  min-height: 800px;
}

/deep/ .tp-revslider-slidesli, .active-revslide, .current-sr-slide-visible{
height: 800px !important;
}

.maintain-container {
  position: relative;
}

.announcement {
  &-container {
    position: absolute;
    z-index: 100;
    top: 2%;
  }

  &__wrapper {
    margin: 10% 0;
  }

  &__box {
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.3);
    border: rgba(255, 255, 255, 0.1) 10px solid;
    box-shadow: rgba(0, 0, 0, 0.1) 0 0 20px inset;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    color: #333;

    h1 {
      text-align: center;
      font-weight: 300;
      font-size: 50px;
      line-height: 50px;
    }

    hr {
      border: 0;
      margin: 40px 0;
      border-bottom: rgba(255, 255, 255, 0.5) 1px solid;
    }
  }
}

@media screen and (min-width: 987px) {
  .announcement-container {
    left: 50%;
    margin-left: -410px;
  }
}
</style>
