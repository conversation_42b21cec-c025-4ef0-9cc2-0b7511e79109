<template>
  <!-- eslint-disable -->
  <div>
    <div class="company-info-container tabs">
      <el-tabs v-model="activeName" type="card" class="tabs-title">
        <el-tab-pane
          v-for="item in tabs"
          :key="item.label"
          :label="item.label"
          :name="item.name"
        >
          <component
            :ref="item.name"
            :is="item.component"
            :active-tab-name="activeName"
            :data="data[item.data]"
            :loading="pageLoading"
            :company-info-type="companyType"
            :company-info-name="companyName"
            use-type="change"
            @change="change"
          />
        </el-tab-pane>
      </el-tabs>
      <div class="operate-btn-container">
        <el-button type="default" @click="$router.push({ name: 'CompanyInfo' })"
          >返回单位列表</el-button
        >
        <el-button type="primary" :loading="submitLoading" @click="check"
          >提交变更</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import Surveyor from "./registered-surveyor/index.vue";
import ChangeApplyInfo from "./change-apply-info/index.vue";
// mixins
import CompanyInfoTabs from "mixins/company-info/tabs.js";
// Api
import CompanyInfoApi from "api/company-info/index.js";
// Api
import CompanyChangeApi from "api/company-change/index.js";
// 工具
import { objOmit } from "utils/index.js";
import { validForm } from "utils/form.js";

export default {
  name: "CompanyChange",
  components: {
    Surveyor,
    ChangeApplyInfo,
  },
  mixins: [CompanyInfoTabs],
  data() {
    return {
      submitLoading: false,
      // 禁止编辑
      disableEdit: true,
    };
  },
  methods: {
    init() {
      if (!this.companyID) {
        this.$message.warning("无法获取该单位信息，请刷新页面重试");
        return;
      }

      this.mappingTabs = [
        {
          label: "基本信息",
          name: "base",
          data: "CompanyBaseInfo",
          component: "BaseInfo",
        },
        {
          label: "测绘资质信息",
          name: "qualifications",
          data: "CompanyQualification",
          component: "Qualifications",
        },
        {
          label: "注册测绘师信息",
          name: "surveyor",
          data: "CompanyEmployees",
          component: "Surveyor",
        },
        {
          label: "变更申请信息",
          name: "changeApplyInfo",
          data: "ChangeApplyInfo",
          component: "ChangeApplyInfo",
        },
      ];

      this.initTabs();
      this.getCompanyInfo();
    },
    // 获取注册成功后的单位信息
    getCompanyInfo() {
      this.setPageLoading(true);
      CompanyInfoApi.GetCompanyDetailsInfo(this.companyID)
        .then(async (res) => {
          this.disableEdit = true;
          if (res.StateCode === 1) {
            const { DetailInfo } = res.Data;

            let data = { ...DetailInfo };
            data.CompanyEmployees = DetailInfo.CompanyEmployees.filter(
              (e) => e.PersonRole === "注册测绘师"
            );

            this.data = { ...data, ChangeApplyInfo: { ModifyReason: null } };
            this.companyName = this.data.CompanyBaseInfo.CompanyName;

            if (!this.data) {
              this.initTabs();
            }
          } else {
            this.$message.error(res.Message);
          }
          this.setPageLoading(false);
        })
        .catch((err) => {
          this.disableEdit = true;
          this.setPageLoading(false);
          this.$message.error("服务器繁忙，请刷新重试");
        });
    },
    /**
     * tab页内容改变
     * @param {*} activeTabName 当前tab页
     * @param {*} changeData 获取改变的值
     * @param {*} remark 备注
     */
    change(activeTabName, changeData, remark) {
      this.setPageLoading(true);

      if (activeTabName === "base") {
        if (remark === "单位名称变更") {
          this.companyName = changeData;
        }
      }

      if (activeTabName === "surveyor") {
        if (remark === "注册测绘师信息Tab变更") {
          this.data.CompanyEmployees = changeData;
        }
      }

      this.setPageLoading(false);
    },
    // 校验tab
    async check() {
      let CompanyBaseInfo = null;
      if (this.$refs.base) {
        const baseInfoForm = this.$refs.base[0].$refs.baseInfoForm;
        const valid = await validForm(baseInfoForm, "基本信息填写有误，请检查");
        if (!valid) {
          this.activeName = "base";
          return false;
        } else {
          CompanyBaseInfo = baseInfoForm.model;
        }
      }

      const CompanyEmployees = this.$refs.surveyor[0].$refs.surveyorTable
        .tableData;

      if (CompanyEmployees && CompanyEmployees.length <= 0) {
        this.$message.error("注册测绘师至少录入一条记录");
        this.activeName = "surveyor";
        return false;
      }

      let CompanyQualification = null;
      if (this.$refs.qualifications) {
        const qualificationsForm = this.$refs.qualifications[0].$refs
          .qualificationsForm;
        const valid = await validForm(
          qualificationsForm,
          "测绘资质信息填写有误，请检查"
        );
        if (!valid) {
          this.activeName = "qualifications";
          return false;
        } else {
          CompanyQualification = JSON.parse(
              JSON.stringify(qualificationsForm.model)
            );
        }
      }

      let CompanyChangeApplyInfo = null;
      if (this.$refs.changeApplyInfo) {
        const changeApplyInfoForm = this.$refs.changeApplyInfo[0].$refs
          .changeApplyInfoForm;
        const valid = await validForm(
          changeApplyInfoForm,
          "变更说明内容未填写，请检查"
        );
        if (!valid) {
          this.activeName = "changeApplyInfo";
          return false;
        } else {
          CompanyChangeApplyInfo = changeApplyInfoForm.model;
        }
      }

      const data = this.handleData({
        CompanyBaseInfo,
        CompanyQualification,
        CompanyEmployees,
        ModifyReason: CompanyChangeApplyInfo.ModifyReason,
      });

      this.$confirm(
        `<p>确认提交单位变更申请？</p><p>您的变更说明：${
          CompanyChangeApplyInfo.ModifyReason
        }</p>`,
        "温馨提示",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消",
        }
      )
        .then((res) => {
          this.submit(data);
        })
        .catch((err) => console.log(err));
    },
    // 处理要提交的数据
    handleData(data) {
      let {
        CompanyBaseInfo,
        CompanyQualification,
        CompanyEmployees,
        ModifyReason,
      } = data;

      // 应后端接口需求特殊处理AttachmentInfo字段
      let qualification = { ...CompanyQualification }; // 解决vue"Invalid prop: type check failed for prop "data". Expected Array, got String with value"报错问题
      const {
        QualificateCertificate,
        ViceQualificateCertificate,
        BusinessRange,
      } = qualification;
      qualification.AttachmentInfo = JSON.stringify({
        QualificateCertificate,
        ViceQualificateCertificate,
      });
      qualification = objOmit(qualification, [
        "QualificateCertificate",
        "BusinessRangeNames",
        "BusinessRangeTree",
        "BusinessRangeJSON",
      ]);
      // 应后端接口需求特殊处理BusinessRange字段，改成json字符串
      // qualification.BusinessRange = JSON.stringify(BusinessRange);

      /**
       * 2021-02-25 新需求修改
       * 取最后一级的businessClass组成数组后转成json字符串提交
       */
      let newBusinessRange = [];
      BusinessRange.forEach((e) => {
        newBusinessRange.push(e.pop());
      });
      qualification.BusinessRange = JSON.stringify(newBusinessRange);
      /* 2021-02-25 新需求修改 */

      return {
        CompanyBaseInfo,
        CompanyQualification: qualification,
        CompanyEmployees,
        ModifyReason,
      };
    },
    // 提交表单
    submit(data) {
      this.submitLoading = true;

      CompanyChangeApi.ModifyRequest(data)
        .then(async (res) => {
          if (res.StateCode === 1) {
            this.$message.success("已提交审核，请耐心等待结果");

            this.$router.push({ name: "CompanyInfo" });
          } else {
            this.$message.error(res.Message);
            // 重新获取信息
            this.getCompanyInfo();
          }
          this.submitLoading = false;
        })
        .catch((err) => {
          console.log(err);
          this.submitLoading = false;
          this.$message.error("服务器繁忙，请稍后重试");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/compnay-info-tabs.scss";
</style>
