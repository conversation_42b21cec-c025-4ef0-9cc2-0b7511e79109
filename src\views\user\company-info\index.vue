<template>
  <!-- eslint-disable -->
  <div>
    <div class="company-info-container tabs">
      <div class="operate-btn-container" v-if="companyType === '测绘单位'">
        <el-button
          v-if="companyRole === '单位管理员' || companyRole === '注册测绘师'"
          type="primary"
          @click="$router.push({ name: 'RegisteredSurveyorAuth' })"
        >
          <i class="iconfont icon-shouquanguanli mr-5"></i>注册测绘师授权
        </el-button>
        <template v-if="companyRole === '单位管理员'">
          <el-button
            type="warning"
            @click="$router.push({ name: 'CompanyChangeHistory' })"
          >
            <i class="iconfont icon-jilu mr-5"></i>变更记录
          </el-button>
          <el-button
            type="primary"
            :loading="applyChangeLoading"
            @click="applyChange()"
          >
            <i class="iconfont icon-change mr-5"></i>申请变更
          </el-button>
          <el-button
            type="primary"
            :loading="withDrawCompanyLoading"
            @click="withDrawCompany()"
          >
            <i class="iconfont icon-change mr-5"></i>注销单位
          </el-button>
        </template>
      </div>
      <el-tabs v-model="activeName" type="card" class="tabs-title">
        <el-tab-pane
          v-for="item in tabs"
          :key="item.label"
          :label="item.label"
          :name="item.name"
        >
          <component
            :ref="item.name"
            :is="item.component"
            :active-tab-name="activeName"
            :data="data[item.data]"
            :loading="pageLoading"
            :disable-edit="true"
            :company-info-type="companyType"
            :company-info-name="companyName"
            use-type="info"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import Member from "./member-manage";
// mixins
import CompanyInfoTabs from "mixins/company-info/tabs.js";
// Api
import CompanyInfoApi from "api/company-info/index.js";
import CompanyChangeApi from "api/company-change/index.js";

export default {
  name: "CompanyInfo",
  components: {
    Member,
  },
  mixins: [CompanyInfoTabs],
  data() {
    return {
      applyChangeLoading: false,
      logOffLoading: false,
      withDrawCompanyLoading: false,
    };
  },
  watch: {
    companyID(val) {
      // 解决因为本地项目因注销后token不存在，接口报错401的问题
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    init() {
      if (!this.companyID) {
        this.$message.warning("无法获取该单位信息，请刷新页面重试");
        return;
      }
      this.initTabs();
      this.getCompanyInfo();
    },
    // 获取注册成功后的单位信息
    getCompanyInfo() {
      this.setPageLoading(true);
      CompanyInfoApi.GetCompanyDetailsInfo(this.companyID)
        .then(async (res) => {
          this.disableEdit = true;
          if (res.StateCode === 1) {
            const { DetailInfo } = res.Data;

            this.data = DetailInfo;
            // console.log(this.data.CompanyQualification.BusinessRangeJSON);

            if (!this.data) {
              this.initTabs();
            }
          } else {
            this.$message.error(res.Message);
          }
          this.setPageLoading(false);
        })
        .catch((err) => {
          this.disableEdit = true;
          this.setPageLoading(false);
          this.$message.error("服务器繁忙，请刷新重试");
        });
    },
    applyChange() {
      this.applyChangeLoading = true;
      CompanyChangeApi.GetSurveyCompanyInfoModify()
        .then((res) => {
          const { Data, StateCode, Message } = res;
          if (StateCode === 1) {
            // 判断是否有正在进行的变更申请
            if (!Data) {
              this.$router.push({ name: "MappingCompanyChange" });
            } else {
              this.$message.warning("有正在变更的申请，不能申请变更");
            }
          } else {
            this.$message.error(Message);
          }
          this.applyChangeLoading = false;
        })
        .catch((err) => {
          this.applyChangeLoading = false;
          this.$message.error("服务器繁忙，请刷新重试");
        });
    },
    //注销名录库
    withDrawCompany() {
      this.$router.push({ name: "CompanyWithDraw" });
      /*
      this.withDrawCompanyLoading = true;
      CompanyInfoApi.GetCompanyWithDrawInfo()
        .then((res) => {
          const { Data, StateCode, Message } = res;
          if (StateCode === 1) {
            // 判断是否有正在进行的变更申请
            if (!Data) {
              this.$router.push({ name: "CompanyWithDraw" });
            } else {
              this.$message.warning("有正在变更的申请，不能申请变更");
            }
          } else {
            this.$message.error(Message);
          }
          this.withDrawCompanyLoading = false;
        })
        .catch((err) => {
          this.withDrawCompanyLoading = false;
          this.$message.error("服务器繁忙，请刷新重试");
        });
        */
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/compnay-info-tabs.scss";
</style>
