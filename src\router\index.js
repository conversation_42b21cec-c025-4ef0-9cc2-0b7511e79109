/* eslint-disable */
import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* LayoutDefault */
import LayoutDefault from "@/layout/default";

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * 固定路由
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/redirect",
    component: LayoutDefault,
    hidden: true,
    children: [
      {
        path: "/redirect/:path*",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404"),
    hidden: true,
  },
  // 邕e登
  {
    path: process.env.VUE_APP_BASE_URL,
    component: () => import("@/views/home/<USER>"),
    name: "BDCHome",
    meta: {
      title: "网站首页",
      // icon: "el-icon-s-home"
    },
  },
  // 内部Home
  {
    path: "/",
    component: LayoutDefault,
    redirect: "/home",
    children: [
      {
        path: "home",
        component: () => import("@/views/home/<USER>"),
        name: "Home",
        hidden: true,
        meta: {
          title: "网站首页",
          // icon: "el-icon-s-home"
        },
      }
    ],
  },
  {
    path: "/",
    component: LayoutDefault,
    children: [
      {
        path: "contact",
        component: () => import("@/views/contact/index"),
        name: "Contact",
        hidden: true,
        meta: { title: "联系我们" },
      },
      {
        path: "about",
        component: () => import("@/views/about/index"),
        name: "About",
        hidden: true,
        meta: { title: "关于我们" },
      },
      {
        path: "reset-password",
        component: () => import("@/views/reset-password/index"),
        name: "ResetPassword",
        hidden: true,
        meta: { title: "重置密码" },
      },
      {
        path: "maintain",
        component: () => import("@/views/maintain/index"),
        name: "SysMaintain",
        hidden: true,
        meta: { title: "系统公告" },
      },
      {
        path: "test",
        component: () => import("@/views/test/index"),
        name: "Test",
        hidden: true,
        meta: { title: "测试页面" },
      },
    ],
  }
];

/* Router Modules */
const requireRouter = require["context"](
  // 其组件目录的相对路径
  "./modules",
  // 是否查询其子目录
  false,
  // 匹配基础组件文件名的正则表达式
  /\w+\.js$/
);

/**
 * 动态路由
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = requireRouter.keys().map((fileName) => {
  // 获取组件配置
  return requireRouter(fileName).default || requireRouter(fileName);
});
export const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: [...constantRoutes],
    mode: "history",
    base: "/sdc",
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
