<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="800px"
    @close="close"
    class="guide-dialog-container"
  >
    <template v-if="business">
      <base-survey-data-download-flow
        v-if="business.businessClass === 'BaseSurveyDataDownloadFlow'"
      />
      <measure-put-line-flow
        v-if="business.businessClass === 'MeasurePutLineFlow'"
      />
      <real-estate-pre-survey-flow
        v-if="
          business.businessClass === 'RealEstatePreSurveyFlow' ||
          business.businessClass === 'RealEstatePreCheckSurveyFlow'
        "
      />
      <real-estate-actual-survey-flow
        v-if="business.businessClass === 'RealEstateActualSurveyFlow'"
      />
      <mark-point-survey-flow
        v-if="business.businessClass === 'MarkPointSurveyFlow'"
      />
      <measure-put-line-pre-check-flow
        v-if="business.businessClass === 'MeasurePutLinePreCheckFlow'"
      />
      <real-estate-pre-survey-result-change-flow
        v-if="business.businessClass === 'RealEstatePreSurveyResultChangeFlow'"
      />
      <real-estate-pre-survey-building-change-flow
        v-if="business.businessClass ==='RealEstatePreSurveyBuildingTableChangeFlow'"
      />
      <real-estate-actual-building-change-flow
        v-if="business.businessClass === 'RealEstateActualBuildingTableChangeFlow'"
      />
      <real-estate-actual-result-change-flow
        v-if="business.businessClass === 'RealEstateActualResultChangeFlow'"
      />
      <real-estate-pre-survey-auto-flow
        v-if="business.businessClass === 'RealEstatePreCheckSurveyAutoFlow'"
      />
      <council-plan-check-flow
        v-if="business.businessClass === 'CouncilPlanCheckFlow'"
      />
       <council-measure-put-line-pre-check-flow
        v-if="business.businessClass === 'CouncilMeasurePutLinePreCheckFlow'"
      />
      <real-estate-overall-actual-survey-flow
        v-if="business.businessClass === 'RealEstateOverallActualSurveyFlow'"
      />
    </template>
    <empty v-else />
  </el-dialog>
</template>

<script>
/* eslint-disable */
import BaseSurveyDataDownloadFlow from "./baseSurveyDataDownloadFlow.vue";
import MeasurePutLineFlow from "./measurePutLineFlow.vue";
import RealEstatePreSurveyFlow from "./realEstatePreSurveyFlow.vue";
import RealEstateActualSurveyFlow from "./realEstateActualSurveyFlow.vue";
import MarkPointSurveyFlow from "./markPointSurveyFlow.vue";
import MeasurePutLinePreCheckFlow from "./measurePutLinePreCheckFlow";
import RealEstatePreSurveyResultChangeFlow from "./realEstatePreSurveyResultChangeFlow";
import RealEstatePreSurveyBuildingChangeFlow from "./realEstatePreSurveyBuildingChangeFlow";
import RealEstateActualBuildingChangeFlow from "./realEstateActualBuildingChangeFlow";
import RealEstateActualResultChangeFlow from "./realEstateActualResultChangeFlow";
import RealEstatePreSurveyAutoFlow from "./realEstatePreSurveyAutoFlow";
import CouncilPlanCheckFlow from "./councilPlanCheckFlow";
import CouncilMeasurePutLinePreCheckFlow from "./councilMeasurePutLinePreCheckFlow";
import RealEstateOverallActualSurveyFlow from "./realEstateOverallActualSurveyFlow.vue";
export default {
  name: "GuideDialog",
  components: {
    BaseSurveyDataDownloadFlow,
    MeasurePutLineFlow,
    RealEstatePreSurveyFlow,
    RealEstateActualSurveyFlow,
    MarkPointSurveyFlow,
    MeasurePutLinePreCheckFlow,
    RealEstatePreSurveyResultChangeFlow,
    RealEstatePreSurveyBuildingChangeFlow,
    RealEstateActualBuildingChangeFlow,
    RealEstateActualResultChangeFlow,
    RealEstatePreSurveyAutoFlow,
    CouncilPlanCheckFlow,
    CouncilMeasurePutLinePreCheckFlow,
    RealEstateOverallActualSurveyFlow,
  },
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 业务逻辑类
    businessClass: {
      type: String,
      default: null,
    },
    // 当前业务
    business: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: "业务指南",
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      const { business } = this;
      this.title = `【${business.label}】业务指南`;
    },
    // 关闭存储弹窗
    close() {
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0 20px 20px 20px;
}
</style>
