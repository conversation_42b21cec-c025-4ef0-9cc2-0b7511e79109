/*
 * 模块 : 所有附件相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-07-06
 * 版本 : version 1.0
 */
/* eslint-disable */

// Api
import Api from "api/public/index.js";

export default {
  data() {
    return {
      downLoading: false,
      // 图片预览弹窗
      imgDialog: {
        visible: false,
        title: null,
        imgUrl: null
      },
      // pdf预览弹窗
      pdfDialog: {
        visible: false,
        title: null,
        pdfUrl: null
      }
    };
  },
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e;
        if (e.length) {
          this.$refs[formName].clearValidate(attr);
        }
      }
    },
    // 开始下载
    downloadStart(file) {
      this.downLoading = true;
    },
    // 下载成功
    downloadEnd(file) {
      this.downLoading = false;
    },
    // 下载失败
    downloadFail(file) {
      this.downLoading = false;
      this.$message.error("服务器繁忙，请稍后重试");
    },
    // 预览图片
    previewImg(file) {
      this.imgDialog = {
        visible: true,
        title: file.AttachmentName + file.AttachmentExt,
        imgUrl: Api.ShowImg(file.ID)
      };
    },
    // 预览PDF
    previewPdf(file) {
      this.pdfDialog = {
        visible: true,
        title: file.AttachmentName + file.AttachmentExt,
        pdfUrl: Api.ShowPDF(file.ID)
      };
    },
    // 取消预览
    cancelPreview() {
      this.imgDialog = {
        visible: false,
        title: null,
        imgUrl: null
      };

      this.pdfDialog = {
        visible: false,
        title: null,
        pdfUrl: null
      };
    },
    // 删除
    del(e, formName, attr) {
      if (!e.file.ID) return false;

      Api.DeleteAttachment(e.file.ID).then(res => {
        if (formName) {
          this.$refs[formName].model[attr] = e.list;
          if (e.list.length) {
            this.$refs[formName].clearValidate(attr);
          }
        }
        this.GLOBAL.logInfo(`附件删除成功`);
      }).catch(err => {
        console.log(err);
      });
    }
  },
};
