/*
 * 模块 : 单位信息Tabs页相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-08-14
 * 版本 : version 1.1
 */
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";
// 工具
import { getInfoByIDCard } from "utils/index.js";
/// 组件
import BaseInfo from "views/user/company-info/tab/base-info";
import Qualifications from "views/user/company-info/tab/qualifications";

export default {
  components: {
    BaseInfo,
    Qualifications,
  },
  computed: {
    ...mapGetters([
      "personName",
      "userName",
      "personNo",
      "roles",
      "companyStateCode",
      "companyID",
      "companyType",
      "companyRole",
      "pageLoading",
      "currentEnv"
    ]),
  },
  data() {
    return {
      activeName: "base",
      tabs: [],
      // 测绘单位tabs
      mappingTabs: [
        {
          label: "基本信息",
          name: "base",
          data: "CompanyBaseInfo",
          component: "BaseInfo",
        },
        {
          label: "测绘资质信息",
          name: "qualifications",
          data: "CompanyQualification",
          component: "Qualifications",
        },
        {
          label: "人员信息",
          name: "member",
          data: "CompanyEmployees",
          component: "Member",
        },
      ],
      // 建设单位tabs
      developTabs: [
        {
          label: "基本信息",
          name: "base",
          data: "CompanyBaseInfo",
          component: "BaseInfo",
        },
        {
          label: "人员信息",
          name: "member",
          data: "CompanyEmployees",
          component: "Member",
        },
      ],
      // 信息数据
      data: {},
      // 测绘单位数据
      mappingData: {
        // 基本信息
        CompanyBaseInfo: {},
        // 资质信息
        CompanyQualification: {},
        // 人员信息
        CompanyEmployees: [],
      },
      // 建设单位数据
      developData: {
        // 基本信息
        CompanyBaseInfo: {},
        // 人员信息
        CompanyEmployees: [],
      },
      // 审核原因
      responseMsg: null,
      // 保存单位名称，因为会影响人员信息的详情
      companyName: "",
    };
  },
  created() {
    this.init();
  },
  methods: {
    initTabs() {
      // 初始化tabs
      if (this.companyType === "测绘单位") {
        this.tabs = [...this.mappingTabs];
        this.data = { ...this.mappingData };
      } else {
        this.tabs = [...this.developTabs];
        this.data = { ...this.developData };
      }
    },
    // 页面加载
    setPageLoading(val) {
      this.$store.dispatch("app/setPageLoading", val);
    },
    /**
     * tab页内容改变
     * @param {*} activeTabName 当前tab页
     * @param {*} changeData 获取改变的值
     * @param {*} remark 备注
     */
    change(activeTabName, changeData, remark) {
      this.setPageLoading(true);

      if (activeTabName === "base") {
        if (remark === "单位名称变更") {
          this.companyName = changeData;
        }
      }

      if (activeTabName === "member") {
        if (remark === "邕e登读取人员信息") {
          let includeCurrentLogin = false;

          const { userName, personName, personNo } = this;

          const { CompanyName, CompanyAdmins } = changeData;
          if (CompanyAdmins.length) {
            let CompanyEmployees = [...this.data.CompanyEmployees];
            CompanyAdmins.forEach((admin) => {
              let isExist = true;
              CompanyEmployees.forEach((employee) => {
                if (admin.IDNo == personNo || employee.PersonNumber == personNo)
                  includeCurrentLogin = true;
                if (employee.PersonNumber !== admin.IDNo) isExist = false;
              });
              if (!isExist) {
                const { gender, age } = admin.IDNo
                  ? getInfoByIDCard(admin.IDNo)
                  : { gender: null, age: null };
                this.data.CompanyEmployees.push({
                  PersonRole: "单位管理员",
                  CompanyName,
                  PersonName: admin.Name,
                  PersonNumber: admin.IDNo,
                  PersonPhone: admin.Phone,
                  PersonSex: gender,
                  PersonAge: age,
                });
              }
            });
          }

          // 添加当前申请人为单位管理员
          if (!includeCurrentLogin) {
            const { gender, age } = personNo
              ? getInfoByIDCard(personNo)
              : { gender: null, age: null };

            this.data.CompanyEmployees.push({
              PersonRole: "单位管理员",
              CompanyName,
              PersonName: personName,
              PersonNumber: personNo,
              PersonPhone: userName,
              PersonSex: gender,
              PersonAge: age,
            });
          }
        } else if (remark === "人员信息Tab变更") {
          this.data.CompanyEmployees = changeData;
        }
      }
      this.setPageLoading(false);
    },
  },
};
