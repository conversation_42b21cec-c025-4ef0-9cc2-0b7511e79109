.result {
    &-title {
        color: #333;
        margin-top: 30px;
        margin-bottom: 20px;
        text-align: center;
        position: relative;

        >span {
            position: relative;
            background: #fff;
            padding: 0 20px;
            z-index: 10;
            font-size: 16px;
            color: $color-primary;
            font-weight: bold;
        }

        &::after {
            position: absolute;
            z-index: 1;
            top: 10px;
            content: "";
            display: block;
            background: #dcdfe6;
            width: 100%;
            height: 1px;
        }
    }

    &-file {
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: #f8f8f8;
        border-radius: 4px;
    }
}