<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    width="1170px"
    @close="close"
    class="property-list-container"
  >
    <div
      v-if="info"
      v-loading="downLoading"
      element-loading-text="文件下载中，请稍等..."
    >
      <table class="table" cellspacing="0" cellpadding="0">
        <tr>
          <th>坐落</th>
          <td colspan="5">{{ info.ZuoL }}</td>
        </tr>
        <tr>
          <th>项目名称</th>
          <th>幢号</th>
          <th>地上层数</th>
          <th>地下层数</th>
          <th>总层数</th>
          <th>总套数</th>
        </tr>
        <tr>
          <td>{{ info.XiangMMC }}</td>
          <td>{{ info.ZhuangH }}</td>
          <td>{{ info.DiSCS }}</td>
          <td>{{ info.DiXCS }}</td>
          <td>{{ info.DiSCS * 1 + info.DiXCS * 1 }}</td>
          <td>{{ info.ZongTS }}</td>
        </tr>
      </table>
      <table class="table mt-20" cellspacing="0" cellpadding="0">
        <tr>
          <th>序号</th>
          <th>室号部位</th>
          <th>起始层</th>
          <th>终止层</th>
          <th>单元号</th>
          <th>房号</th>
          <th>
            套内面积
            <br />(㎡)
          </th>
          <th>
            分摊面积
            <br />(㎡)
          </th>
          <th>
            建筑面积
            <br />(㎡)
          </th>
          <th>户型</th>
          <th>户型结构</th>
          <th>房屋性质</th>
          <th>规划用途</th>
          <th>层高</th>
        </tr>
        <template v-if="info.LouPB && info.LouPB.length">
          <tr v-for="(item, index) in info.LouPB" :key="'loupan' + index">
            <td>{{ item.XuH }}</td>
            <td>{{ item.ShiHBW }}</td>
            <td>{{ item.QiSC }}</td>
            <td>{{ item.ZhongZC }}</td>
            <td>{{ item.DanYH }}</td>
            <td>{{ item.FangH }}</td>
            <td>{{ item.TaoNMJ }}</td>
            <td>{{ item.FenTMJ }}</td>
            <td>{{ item.JianZMJ }}</td>
            <td>{{ item.HuX }}</td>
            <td>{{ item.XuHJG }}</td>
            <td>{{ item.FangWXZ }}</td>
            <td>{{ item.GuiHYT }}</td>
            <td>{{ item.CengG }}</td>
          </tr>
        </template>
      </table>
    </div>
    <div slot="footer">
      <el-button @click="close">关闭</el-button>
      <template v-if="info && file">
        <el-button type="primary" @click="download(file)">下载</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/public/index.js";
// 工具
import { downloadFileByUrl, downloadFileByStream } from "utils/index.js";

export default {
  name: "PropertyInfoTableDialog",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 记录列表
    info: {
      type: Object,
      default: null,
    },
    // 下载文件
    file: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      title: "楼盘信息表",
      downLoading: false,
    };
  },
  methods: {
    // 关闭存储弹窗
    close() {
      this.$emit("cancel");
      this.$emit("update:visible", false);
    },
    // 下载文件
    async download(file) {
      if (!file || !file.ID) {
        this.$message.error("文件不存在，无法下载");
        return;
      }

      this.downLoading = true;

      // 通过key值获取文件绝对路径下载
      try {
        const res = await Api.GetAttachmentDownloadKey(file.ID);

        const { StateCode, Data } = res;
        const { AttachmentName, AttachmentExt } = file;

        if (StateCode === 1) {
          downloadFileByUrl(
            Api.DownloadAttachmentByKey(Data, AttachmentName + AttachmentExt),
            AttachmentName + AttachmentExt
          );
          this.$emit("download-end", file);
        } else {
          this.$emit("download-fail", file);
        }
         this.downLoading = false;
      } catch (err) {
        this.$emit("download-fail", file);
        this.downLoading = false;
      }

      // 通过文件流下载
      // Api.DownloadAttachment(file.ID)
      //   .then((res) => {
      //     const { AttachmentName, AttachmentExt } = file;
      //     downloadFileByStream(res, AttachmentName + AttachmentExt);
      //     this.downLoading = false;
      //   })
      //   .catch((err) => {
      //     this.downLoading = false;
      //   });
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  .inside {
    border-top: none;
    border-left: none;

    tr:last-child {
      th,
      td {
        border-bottom: none;
      }
    }
  }

  th {
    background: #f8f8f8;
    color: #909399;
    // min-width: 130px;
    // width: 130px;
    // max-width: 130px;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    // min-width: 130px;
  }
}
</style>