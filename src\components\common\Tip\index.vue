<template>
  <!-- eslint-disable -->
  <div :class="['tip-container', 'tip-' + type]">
    <slot />
  </div>
</template>

<script>
/* eslint-disable */
export default {
  name: "Tip",
  props: {
    // 提示类型
    type: {
      type: String,
      default: "default"
    }
  }
};
</script>

<style lang="scss" scoped>
.tip {
  &-container {
    padding: 8px 16px;
    font-size: 16px;
    border-radius: 4px;
    background-color: #e8e8e8;
    border-left: #b2b2b2 4px solid;

    &.tip-default {
      background-color: #bcebfd;
      border-left: #2c80a9 4px solid;
      color: #072f63;
    }

    &.tip-success {
      background-color: #e7f5e0;
      border-left: #67c23a 4px solid;
      color: #3d9214;
    }

    &.tip-error {
      background-color: #ffe8ea;
      border-left: #fe5a5a 4px solid;
      color: #b50000;
    }
  }
}
</style>
