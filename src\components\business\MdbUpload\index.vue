<template>
  <!-- eslint-disable -->
  <div class="file-upload-container">
    <div class="file-upload">
      <el-upload
        class="file-upload__btn"
        ref="fileUpload"
        drag
        :action="`${uploadUrl}?id=${id}`"
        :file-list="fileList"
        :headers="headers"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadFail"
        :limit="limit + 1"
      >
        <div slot="default">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
        </div>
        <div slot="file" slot-scope="{file}">
          <div class="file-upload-text" v-if="file.status === 'ready'">准备上传...</div>
          <div class="file-upload-text" v-if="file.status === 'fail'">上传失败</div>
          <div v-if="file.status && file.status === 'uploading'" style="position:relative">
            <!-- 原本el组件的名称 -->
            <div>
              <div class="file-name">{{ file.name }}</div>
              <!-- <i class="el-icon-close" @click="cancelUpload(file)"></i> -->
            </div>
            <el-progress
              class="mt-10"
              :stroke-width="2"
              :percentage="parsePercentage(file.percentage)"
            ></el-progress>
          </div>
        </div>
        <div slot="tip">
          <!-- 提示 -->
          <div class="file-upload__tip">文件格式：mdb，文件大小：{{ getSize(102400) }}以内</div>
        </div>
      </el-upload>
    </div>
  </div>
</template>
<script>
/* eslint-disable */
// mixins
import AttachmentUpload from "mixins/attachment/upload.js";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  name: "MdbUpload",
  mixins: [AttachmentUpload],
  props: {
    // 所有文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 是否显示文件大小提示文本
    showFileSize: {
      type: Boolean,
      default: true
    },
    // 文件大小限制，单位为KB
    fileSize: {
      type: Number,
      default: 150
    },
    // 业务id
    id: {
      type: String | Number,
      default: null
    },
    // 上传地址
    uploadUrl: {
      type: String,
      // 默认是不动产预测绘成果上传地址
      default: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostSurveyProjectResult`
    },
    // 最大上传个数
    limit: {
      type: Number,
      default: 1
    }
  },
  created(){
    this.createUpload = false;
  },
  methods: {
    // 检查上传文件格式
    onCheckFormat(file) {
      const fileName = file.name;
      const suffix = fileName.substring(fileName.length - 5);

      // 文件格式
      if (suffix.indexOf(".mdb") >= 0) return true;

      this.$message.warning("文件只能是 mdb 格式");
      return false;
    },
    // 上传成功
    uploadSuccess(res, file, fileList) {
      const { StateCode, Data, Message } = res;
      // console.log(res, file, fileList);

      if (StateCode === 1) {
        this.$message.success("文件已上传");
        this.$emit("upload-success", Data, fileList);
      } else {
        console.log(res);
        this.$message.error(Message);
        return false;
      }
    },
    // 上传失败
    uploadFail(res, file, fileList) {
      console.log(res);
      this.$message.error("服务器繁忙，请稍后重试");

      this.$refs.fileUpload.uploadFiles = [];
    }
    // 取消上传
    // cancelUpload(file) {
    //   console.log(file);
    //   this.$refs.fileUpload.abort(file);
    //   this.$refs.fileUpload.uploadFiles = [];
    // }
  }
};
</script>
<style scoped lang="scss">
@import "~@/styles/file-upload.scss";
/deep/ .el-upload-list__item {
  position: relative;
  transition: none !important;
  line-height: 25px !important;
  padding: 5px 10px;
  outline: none;

  &.is-success {
    display: none;
  }
}
</style>

