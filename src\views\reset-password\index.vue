<template>
  <!-- eslint-disable -->
  <div class="my-business-container">
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="95px"
      class="demo-ruleForm"
    >
      <el-form-item label="手机号码：" prop="Mobile">
        <el-input
          v-model.trim="form.Mobile"
          placeholder="请输入手机号码"
          @input="mobileChange"
          @keyup.enter.native="submitForm('form')"
        ></el-input>
      </el-form-item>
      <el-form-item label="验证码：" prop="Code">
        <div class="code-input flex">
          <el-input
            v-model.trim="form.Code"
            style="margin-right: 10px"
            placeholder="请输入验证码（6位数字）"
            @keyup.enter.native="submitForm('form')"
          ></el-input>
          <el-button
            type="success"
            :disabled="!codeBtn.enabled"
            @click="getCode()"
            >{{ codeBtn.text }}</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="新密码：" prop="Password">
        <el-input
          type="password"
          v-model.trim="form.Password"
          :placeholder="passwordPla"
          autocomplete="off"
          @keyup.enter="submitForm('form')"
        ></el-input>
      </el-form-item>
      <el-form-item label="确认密码：" prop="ConfirmPassword">
        <el-input
          type="password"
          v-model.trim="form.ConfirmPassword"
          placeholder="请输入确认密码"
          autocomplete="off"
          @keyup.enter.native="submitForm('form')"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="default" @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="submitForm('form')" :loading="loading"
          >确认修改</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
/* eslint-disable */
// 验证
import { validPassword, isEqual, validMobile } from "utils/validate";
// Api方法
import Api from "api/public";

export default {
  name: "ResetPassword",
  data() {
    const validateMobile = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入手机号码"));
      } else if (!validMobile(value)) {
        callback(new Error("手机号码格式有误"));
      } else {
        callback();
      }
    };
    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入新密码"));
      } 
      /**
       *  新密码验证规则统一由后端制定
       *  else if (!validPassword(value)) {
       *    callback(new Error("密码格式为6-16位字母或数组的组合"));
       *  }
       */
      else {
        callback();
      }
    };
    const validateComfirPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入确认密码"));
      } else if (!isEqual(value, this.form.Password)) {
        callback(new Error("两次输入密码不一致"));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      // 验证码按钮
      codeBtn: {
        enabled: false,
        text: "获取验证码",
      },
      form: {
        Mobile: null,
        Code: null,
        Password: null,
        ConfirmPassword: null,
      },
      rules: {
        Mobile: [
          {
            required: true,
            validator: validateMobile,
            trigger: "blur",
          },
        ],
        Code: [{ required: true, message: "请输入验证码", trigger: "blur" }],
        Password: [
          {
            required: true,
            validator: validateNewPassword,
            trigger: "blur",
          },
        ],
        ConfirmPassword: [
          {
            required: true,
            validator: validateComfirPassword,
            trigger: "blur",
          },
        ],
      },
      passwordPla: "请输入新密码",
    };
  },
  mounted() {
    this.getUsedPasswordRegex();
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          Api.ResetPwd(this.form)
            .then((res) => {
              if (res.StateCode == 1) {
                this.$message.success("密码重置成功！");
                this.$router.push({ name: "Home" });
              } else this.$message.error(res.Message);
            })
            .catch((err) => {
              console.log(err);
              // this.$message.error("服务器繁忙，请稍后重试");
            });
        }
      });
    },
    mobileChange(e) {
      this.codeBtn.enabled = validMobile(e) ? true : false;
    },
    getCode() {
      Api.GetVertificateCodeByMobile({
        Mobile: this.form.Mobile,
      })
        .then((res) => {
          if (res.StateCode === 1) {
            this.codeBtn.enabled = false;

            let count = 59;
            let timer = setInterval(() => {
              this.codeBtn.text = `${count}后重新获取`;
              count--;

              if (count <= -1) {
                clearInterval(timer);
                this.codeBtn.enabled = this.form.Mobile ? true : false;
                this.codeBtn.text = `获取验证码`;
              }
            }, 1000);

            this.$message.success("验证码发送成功，请注意查收");
          } else {
            this.$message.error(res.Message);
          }
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("服务器繁忙，请稍后重试");
        });
    },
    //获取密码校验规则
    getUsedPasswordRegex() {
      Api.GetUsedPasswordRegex()
        .then((res) => {
          if (res.StateCode == 1) {
            if (
              res.Data !== "" &&
              res.Data !== undefined &&
              res.Data !== null &&
              res.Data.length !== 0
            ) {
              this.passwordPla = `请输入新密码（${res.Data[0].Business}）`;
            }
          } else this.$message.error(res.Message);
        })
        .catch((err) => {
          console.log(err);
          // this.$message.error("服务器繁忙，请稍后重试");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.my-business-container {
  padding: 40px 0;
  width: 500px;
  margin: auto;
  .code-input {
    justify-content: space-around;
  }
}
.step-bar {
}
</style>
