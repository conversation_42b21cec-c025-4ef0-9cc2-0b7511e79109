<template>
  <!-- eslint-disable -->
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <tip class="mb-20">温馨提示：测绘单位所能承担的测绘作业范围以测绘资质证书登记的为准</tip>
      <el-form-item label="测绘单位" prop="MappingCompany">
        <el-input
          placeholder="请输入单位名称进行搜索"
          v-model="companyKey"
          style="width: 50%"
          clearable
          @clear="getCompanyList(1, page.pageSize)"
        >
          <el-button slot="append" icon="el-icon-search" @click="getCompanyList(1, page.pageSize)"></el-button>
        </el-input>
      </el-form-item>
    </el-form>
    <dynamic-table
      ref="licenceTable"
      class="company-table mt-20"
      v-loading="companyListLoading"
      element-loading-text="加载中，请稍等..."
      :table-header="companyTableHeader"
      :table-data="companyList"
      :default-props="tableProps"
      :showPagination="true"
      :total="page.total"
      :page-no.sync="page.pageNo"
      :page-size.sync="page.pageSize"
      :page-sizes.sync="page.pageSizes"
      @pagination="getCompanyList"
    >
      <el-table-column label="资质等级" width="120" align="center">
        <template slot-scope="{ row }">
          <div>{{ row.QualificationLevel }}</div>
          <el-button
            v-if="row.QualificationLevel"
            slot="reference"
            type="text"
            size="mini"
            icon="el-icon-picture"
            @click="showCertificateImgDialog(row.CompanyId)"
          >查看证书</el-button>
        </template>
      </el-table-column>
      <el-table-column label="联系人" prop="Contacter" width="100" align="center"></el-table-column>
      <el-table-column label="联系电话" prop="ContacterPhone" width="120" align="center"></el-table-column>
      <el-table-column prop="action" label="操作" width="100" fixed="right" align="center">
        <template slot-scope="{ row, $index }">
          <el-button
            type="text"
            icon="el-icon-check"
            @click="selectMappingCompany(row, $index)"
          >选择委托</el-button>
        </template>
      </el-table-column>
    </dynamic-table>
    <el-dialog
      title="查看资质证书（副本）"
      :visible.sync="certificateImgDialog.visible"
      @close="certificateImgDialog.visible = false"
      width="800px"
      class="certificate-img-dialog"
    >
      <div
        class="certificate-img-dialog__content"
        v-loading="imgLoading"
        element-loading-text="加载中，请稍等..."
      >
        <img class="certificate-img-dialog__img" :src="imgUrl" @error="setDefaultImg" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import DynamicTable from "components/common/Table/DynamicTable";
// mixins
import Page from "mixins/page.js";
// Api
import Api from "api/business/index.js";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  name: "MappingCompanyList",
  components: {
    DynamicTable
  },
  mixins: [Page],
  props: {
    // 业务Class
    businessClass: {
      type: String,
      default: null
    },
    // 业务ID
    businessId: {
      type: String,
      default: null
    },
    // 业务流程ID
    currentActionId: {
      type: String,
      default: null
    },
    // 业务名称
    flowName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      imgLoading: false,
      // 测绘单位
      companyKey: "",
      companyListLoading: false,
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title"
      },
      companyTableHeader: [
        {
          title: "单位名称",
          key: "CompanyName",
          align: "center"
        },
        {
          title: "统一信用代码",
          key: "CreditCode",
          align: "center"
        },
        {
          title: "信用等级",
          key: "CreditRating",
          width: "100",
          align: "center"
        }
      ],
      companyList: [],
      // 资质证书副本
      certificateImgDialog: {
        visible: false,
        id: null
      },
      // 图片预览
      imgUrl: null,
      defaultImg: require("@/assets/images/no-found-pic.jpg"),
      // 步骤2
      form: {
        MappingCompany: null
      },
      rules: {
        MappingCompany: [
          {
            required: true,
            validator: (rule, value, callback) => {
              callback();
            },
            trigger: "change"
          }
        ]
      }
    };
  },
  watch: {
    businessClass(val) {
      this.getCompanyList(1, this.page.pageSize);
    }
  },
  created() {
    this.getCompanyList(1, this.page.pageSize);
  },
  methods: {
    // 获取测绘单位列表
    getCompanyList(PageIndex, PageSize) {
      const { businessClass, companyKey } = this;

      this.companyListLoading = true;
      Api.GetSurveyCompanyList({
        BusinessClass: businessClass,
        PageIndex,
        PageSize,
        CompanyName: companyKey
      })
        .then(res => {
          const { Data, StateCode, Message } = res;
          if (StateCode === 1) {
            this.companyList = Data.DataTable;
            this.setPage(Data.Page);
          } else {
            this.$message.error(Message);
          }

          this.companyListLoading = false;
        })
        .catch(err => {
          console.log(err);
          this.companyListLoading = false;
        });
    },
    // 选择测绘单位
    selectMappingCompany(row, index) {
      const { businessId, currentActionId, flowName, businessClass } = this;

      var tipText = `您将委托<span style="color: #F07057">【${row.CompanyName}】</span>进行<span style="color: #27b0f1">【${flowName}】</span>，请确认`;
      if (row.QualificationLevel == '乙' && (businessClass == 'CouncilMeasurePutLinePreCheckFlow' || businessClass == 'CouncilPlanCheckFlow')){
        tipText = `您将委托<span style="color: #F07057">【${row.CompanyName}】</span>进行<span style="color: #27b0f1">【${flowName}】</span>，该单位作业限制范围：不得从事二等及以上控制测量、国家建设重点工程的规划测量、单个建筑物 10万平方米及以上的建筑工程测量、特大型水利水电工程测量、4 千米及以上隧道工程测量。请确认`;
      }

      this.$confirm(
        tipText,
        "温馨提示",
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: "确认",
          cancelButtonText: "取消"
        }
      )
        .then(() => {
          Api.SelectSurveyCompany(businessId, currentActionId, row.CreditCode)
            .then(res => {
              const { StateCode, Data, Message } = res;
              if (StateCode === 1) {
                this.$message.success("委托成功");
                this.$emit("select", Data);
              } else {
                this.$message.error(Message);
              }
            })
            .catch(err => {
              console.log(err);
            });
        })
        .catch(err => console.log(err));
    },
    showCertificateImgDialog(id) {
      this.imgLoading = true;
      this.imgUrl = null;
      Api.GetQualificationImage(id)
        .then(res => {
          this.imgUrl = res;
          if (res) {
            const reader = new FileReader();
            reader.readAsDataURL(res);
            reader.onload = e => {
              this.imgUrl = e.target.result;
            };
          } else {
            this.imgUrl = "";
          }
          this.imgLoading = false;
        })
        .catch(err => {
          console.log(err);
          this.imgUrl = "";
          this.imgLoading = false;
        });
      this.certificateImgDialog = {
        visible: true,
        id
      };
    },
    // 设置默认图片
    setDefaultImg(e) {
      e.currentTarget.src = this.defaultImg;
      e.currentTarget.onerror = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.certificate-img-dialog {
  &__content {
    min-height: 200px;
    text-align: center;
  }

  &__img {
    max-width: 100%;
  }
}
</style>
