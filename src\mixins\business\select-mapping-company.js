/*
 * 模块 : 委托测绘单位相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-11-13
 * 版本 : version 1.0
 */
/* eslint-disable */
// 组件
import MappingCompanyList from "components/business/MappingCompanyList";
// Api
import Api from "api/business/index.js";

export default {
    components: {
        MappingCompanyList
    },
    methods: {
        //重新委托测绘单位
        reSelectSurveyCompany() {
            this.$confirm("确认要重新委托测绘单位吗？", "温馨提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.prevBtn.loading = true;

                    Api.ReSelectSurveyCompany(this.businessID, this.CurrentAction.ID)
                        .then(res => {
                            const { StateCode, Message } = res;
                            if (StateCode === 1) {
                                this.$message.success("操作成功！可重新委托测绘单位");
                            } else {
                                this.$message.error(Message);
                            }
                            this.getBusiness();
                            this.prevBtn.loading = false;
                        })
                        .catch(err => {
                            console.log(err);
                            this.prevBtn.loading = false;
                        });
                })
                .catch((err) => console.log(err));
        },
    },
};
