/*
 * 模块 : 我的业务列表相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-07-22
 * 版本 : version 1.0
 */
/* eslint-disable */

// Api
import BusinessApi from "api/business/index.js";
// JSON
import BussinessList from "mock/bussinessList.json";
// vuex
import { mapGetters } from "vuex";

export default {
  computed: {
    ...mapGetters(["companyRole", "isRealName"]),
  },
  data() {
    return {
      key: "",
      tableHeader: [],
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title",
      },
      // 列表数据
      listData: [],
      // 业务列表
      businessList: [
        {

        }
      ],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList(1, this.page.pageSize);
      this.businessList = BussinessList;
    },
    getBusinessList() {
      this.businessList = [];
      BusinessApi.GetBusinessList()
        .then((res) => {
          const { StateCode, Data, Message } = res;
          if (StateCode === 1) {
            Data.forEach((e) => {
              if (e.children) {
                e.children.forEach((c) => {
                  this.businessList.push(c);
                });
              } else {
                this.businessList.push(e);
              }
            });
          }
        })
        .catch((err) => {
          console.log(err);
          // this.$message.error("服务器繁忙，请稍后重试");
        });
    },
    // 删除一行
    delRow(row) {
      this.selectList = [row];
      this.del();
    },
    // 删除
    del() {
      if (!this.selectList.length) {
        this.$message.warning("请选择要删除的选项");
        return false;
      }

      this.$alert("功能建设中，敬请期待").catch(err => console.log(err));
      this.$refs.businessTbable.$refs.table.clearSelection();

      // 删除成功要清除selectList
      this.clearSelect();
    },
    // 查看详情
    view(row, index) {
      const { businessList } = this;
      const { ID, BusinessClass } = row;
      if (!businessList.length) {
        this.$message.warning("暂无对应业务类型的内容可查看");
        return false;
      }

      for (let i = 0; i < businessList.length; i++) {
        const e = businessList[i];
        // console.log(e);
        if (e.businessClass === BusinessClass) {
          this.$router.push({ path: e.url, query: { id: ID } });
          return true;
        }
      }
    },
    // 切换tab
    tabChange(index) {
      this.activeTabIndex = index;
      this.getList(1, this.page.pageSize);
    },
  },
};
