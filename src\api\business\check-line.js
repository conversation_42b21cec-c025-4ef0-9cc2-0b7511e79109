
/* eslint-disable */
import request from 'utils/request';

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
	// 获取成果检查状态
	GetSurveyResultCheckState:(id, businessClass) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetSurveyResultCheckState?checkId=${id}&businessClass=${businessClass}`,
			method: 'get'
		})
	},
	// 获取成果检查报告
	DownloadErrorReport:(id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/DownloadErrorReport?dataCheckId=${id}`,
			method: 'get',
			responseType: "blob",
            headers: { 
                'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
		})
	}
}