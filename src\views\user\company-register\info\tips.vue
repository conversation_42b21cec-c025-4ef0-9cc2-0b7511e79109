<template>
  <!-- 已提交单位注册申请 -->
  <!-- eslint-disable -->
  <tip
    v-if="companyStateCode > 0 && companyStateCode !== 6"
    :type="companyStateCode <= 3 ? 'default' : 'error'"
    class="info-tips mt-15"
  >
    尊敬的 {{ personName }}，您的单位注册信息
    <!-- 未提交 -->
    <template v-if="companyStateCode === 1">已提交，请耐心等待审核</template>
    <!-- 已提交/待审核 -->
    <template v-if="companyStateCode === 2"><i class="el-icon-loading mr-5"></i>正在审核中，请耐心等待结果</template>
    <!-- 注册完成 -->
    <template v-if="companyStateCode === 3">已审过通过。</template>
    <!-- 退回修改 -->
    <template v-if="companyStateCode === 4">
      审核不通过，
      <span v-if="responseMsg">原因：{{ responseMsg }}。</span>请修改后重新提交注册
    </template>
  </tip>
</template>

<script>
/* eslint-disable */
// vuex
import { mapGetters } from "vuex";

export default {
  name: "CompanyRegisterTips",
  props: {
    responseMsg: {
      type: String,
      default: null
    },
    companyStateCode: {
      type: Number,
      default: 0
    }
  },
  computed: {
    ...mapGetters(["personName"])
  }
};
</script>
<style lang="scss" scoped>
.info-tips{
  font-size: 16px;
}
</style>
