<template>
  <div>
    <div v-if="currentAction.FlowNotice" class="info-tips mb-10">
      <tip type="error">{{ currentAction.FlowNotice }}</tip>
    </div>
    <div v-if="stateCode === 4 && extendInfo.CloseReason" class="info-tips mb-10">
      <tip type="error">该业务已关闭，原因：{{ extendInfo.CloseReason }}</tip>
    </div>
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "BusinessTips",
  props: {
    stateCode: {
      type: Number,
      default: 0,
    },
    currentStep: {
      type: Number,
      default: 1,
    },
    extendInfo: {
      type: Object,
      default: () => ({
        CloseReason: null,
        BackReason: null,
      }),
    },
    currentAction: {
      type: Object,
      default: () => ({
        FlowNotice: null,
      }),
    },
  },
};
</script>
<style lang="scss" scoped>
.info-tips{
  font-size: 16px;
}
</style>
