<template>
  <!-- eslint-disable -->
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      class="create-business-dialog"
      @close="close"
    >
      <tip
        v-if="
          business && business.businessClass === 'BaseSurveyDataDownloadFlow'
        "
        type="default"
        class="mb-20"
        >测绘数据申请业务主要为办理市政类项目前期规划方案设计的业主提供相关的测绘数据下载服务。</tip
      >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="95px"
        @submit.native.prevent
      >
        <el-form-item label="业务名称：" prop="BusinessName">
          <el-input
            v-model="form.BusinessName"
            placeholder="请输入业务名称"
            @keyup.enter.native="submitForm('form')"
          ></el-input>
          <div v-if="showExample">例：南宁市XX区XX路XX号XX小区xx栋（号楼）</div>
          <div v-if="showExample1">注意：请填写工程规划许可证上的项目名称</div>
          <div v-if="showExample2">例：广西南宁市XX区XX路XX号XX小区xx栋（号楼）</div>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="submitForm('form')"
          :loading="saveLoading"
          >创建</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/business/index.js";
// 校验
import { validateBusinessName } from "utils/form.js";
// vuex
import { mapGetters } from "vuex";

export default {
  name: "CreateBusiness",
  computed: {
    ...mapGetters(["userName", "personName"]),
    // 显示示例
    showExample() {
      const { businessClass } = this.business;
      return (
        businessClass === "MeasurePutLineFlow" ||
        businessClass === "RealEstatePreSurveyFlow" ||
        businessClass === "RealEstateActualSurveyFlow" ||
        businessClass === "RealEstatePreCheckSurveyFlow" ||
        businessClass === "RealEstatePreSurveyBuildingTableChangeFlow" ||
        businessClass === "RealEstatePreSurveyResultChangeFlow" ||
        businessClass === "RealEstateActualBuildingTableChangeFlow" ||
        businessClass === "RealEstateActualResultChangeFlow" ||
        businessClass === "MeasurePutLinePreCheckFlow"
      );
    },
    showExample1() {
      const { businessClass } = this.business;
      return (
        businessClass === "CouncilPlanCheckFlow" ||
        businessClass === "CouncilMeasurePutLinePreCheckFlow"
      );
    },
    showExample2() {
      const { businessClass } = this.business;
      return (
        businessClass === "RealEstatePreCheckSurveyAutoFlow"
      );
    },
  },  
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false,
    },
    // 当前业务
    business: {
      type: Object,
      default: () => ({
        businessClass: null,
      }),
    },
    selectList: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      title: "创建业务",
      // loading
      saveLoading: false,
      // 表单
      form: {
        BusinessName: null,
      },
      // 规则
      rules: {
        BusinessName: [
          { 
            required: true, 
            validator: (rule, value, callback) =>
              validateBusinessName(
                rule,
                value,
                callback,
                this.business.businessClass
              ), 
            trigger: "blur" 
          },
        ],
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    },
  },
  methods: {
    // 初始化
    init() {
      this.$nextTick(() => {
        this.$refs["form"].clearValidate();
      });

      const { business } = this;
      this.title = `【${business.label}】创建业务`;
      // this.form.BusinessName = business.label;
    },
    // 关闭存储弹窗
    close() {
      this.form.BusinessName = null;
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { business, form } = this;
          const businessClass = business.businessClass;
          let params = { ...form, BusinessClass: businessClass };
          if (
            businessClass === "RealEstatePreSurveyBuildingTableChangeFlow" ||
            businessClass === "RealEstatePreSurveyResultChangeFlow" ||
            businessClass === "RealEstateActualBuildingTableChangeFlow" ||
            businessClass === "RealEstateActualResultChangeFlow"
          ) {
            params = {
              ...form,
              BusinessClass: businessClass,
              ExtendInfo: this.selectList,
            };
          }
          this.saveLoading = true;
          Api.CreateNewBusiness(params)
            .then((res) => {
              const { StateCode, Data, Message } = res;
              if (StateCode == 1) {
                // 成功回传父组件
                this.$emit("submit", business, Data);
                this.close();
              } else {
                this.$message.error(Message);
              }
              this.saveLoading = false;
            })
            .catch((err) => {
              console.log(err);
              this.saveLoading = false;
            });
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.create-business-dialog {
  /deep/ .el-dialog__body {
    padding-bottom: 5px;
  }
}
</style>
