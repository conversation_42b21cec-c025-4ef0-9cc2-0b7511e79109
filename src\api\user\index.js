
/* eslint-disable */
import request from 'utils/request';
import qs from "qs";

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
    // 多测合一/邕e登登录
	Login: (data) => {
		return request({
			url: `${VUE_APP_SERVER_API}/token`,
            method: 'post',
            // json转formData格式
            data: qs.stringify(data),
            headers: {
                'content-type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
		})
    },
    // 用Token登录邕e登
	LoginWitdhToken: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/suapi/Login`,
            method: 'post'
		})
    },
    // 邕e登登出
	LogOff: () => {
		return request({
			url: `${VUE_APP_SERVER_API}/suapi/LogOff`,
            method: 'post'
		})
    },
    // 获取用户信息
    GetUser: () => {
        return request({
			url: `${VUE_APP_SERVER_API}/suapi/GetUserInfo`,
            method: 'get',
		})
    },
    // 修改密码
    ChangePwd: (data) => {
        return request({
			url: `${VUE_APP_SERVER_API}/api/Account/MobileChangePassWord`,
            method: 'post',
            data
		})
    },
    // 根据用户信息获取单位信息
    GetCompanyInfoByUserInfo: () => {
        return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/CompanyRegister/GetCompanyInfoByUserInfo`,
            method: 'post'
		})
    },
}
