<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="projectScope.length">
      <div class="result-title mt-0">
        <span>【{{ name }}】项目范围坐标</span>
      </div>
      <div
        v-for="(item, index) in projectScope"
        :key="'projectScope' + index"
        class="result-file flex mb-10"
      >
        <div>{{ item.AttachmentName + item.AttachmentExt }}</div>
        <div>
          <el-button
            type="primary"
            icon="el-icon-view"
            class="ml-10"
            :loading="scopePreviewDialog.loading"
            @click="previewScope(item)"
          >预览范围</el-button>
        </div>
      </div>
    </template>
    <template v-if="baseDataFiles.length">
      <div class="result-title mt-0">
        <span>【{{ name }}】基础数据</span>
      </div>
      <tip class="mb-20" v-if="baseDataUploadTime">您申请的数据于{{ baseDataUploadTime }}提取完成，请在48小时内点击下方的“提取数据”按钮进行数据提取，过期后不可提取。</tip>
      <div
        v-for="(item, index) in baseDataFiles"
        :key="'baseDataFile' + index"
        class="result-file flex mb-10"
      >
        <div>
          <span v-if="item.AttachmentLength > 0">{{ item.AttachmentName + item.AttachmentExt }}</span>
          <span v-else>该范围内无可提取的{{ item.AttachmentName }}</span>
        </div>
        <div v-if="item.AttachmentLength > 0">
          <el-button class="ml-10" type="primary" @click="getData(item)">
            <i class="el-icon-download mr-5"></i>提取数据
          </el-button>
        </div>
      </div>
    </template>
    <!-- 预览项目范围坐标弹窗 -->
    <scope-preview-dialog
      :visible.sync="scopePreviewDialog.visible"
      :coordinates="coordinateList"
      :show-footer="false"
      @cancel="closeScopeDialog"
    />
    <!-- 提取数据弹窗 -->
    <get-data-dialog
      :visible.sync="getDataDialog.visible"
      :title="getDataDialog.title"
      :data="getDataDialog.row"
    />
  </div>
</template>

<script>
/* eslint-disable */
import Api from "api/public/index.js";
// mixins
import ScopePreview from "mixins/business/scope-preview.js";
import AttachmentDownload from "mixins/attachment/download.js";
// 组件
import ScopePreviewDialog from "components/business/ScopePreview/index.vue";
import GetDataDialog from "components/business/GetDataDialog/index.vue";

export default {
  name: "StakingScope",
  components: {
    ScopePreviewDialog,
    GetDataDialog
  },
  computed: {
    // 测绘数据提取时间
    baseDataUploadTime(){
      return this.baseDataFiles.length ? this.baseDataFiles[0].UploadTime : null;
    }
  },
  mixins: [ScopePreview, AttachmentDownload],
  props: {
    projectScope: {
      type: Array,
      default: () => []
    },
    baseDataFiles: {
      type: Array,
      default: () => []
    },
    name: {
      type: String,
      default: "拔地定桩"
    },
    step: {
      type: Number,
      default: 1
    },
    dataCheckState: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      getDataDialog: {
        visible: false
      }
    };
  },
  methods: {
    getData(row) {
      this.getDataDialog = {
        visible: true,
        title: `提取${row.AttachmentName}`,
        row
      };
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
</style>

