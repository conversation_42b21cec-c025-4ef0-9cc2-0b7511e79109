
/* eslint-disable */
import request from 'utils/request';

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API
export default {
	// 获取业务列表
	GetMyBusinessList: (q, type, pageIndex, pageSize, stateCode) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/GetMyBusinessList?q=${q}&type=${type}&pageIndex=${pageIndex}&pageSize=${pageSize}&stateCode=${stateCode}`,
			method: 'get'
		})
	},
	// 签收业务
	AcceptBusiness: (id) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/AcceptBusiness?id=${id}`,
			method: 'post'
		})
	},
	// 退回业务
	BackBusiness: (id, actionId, backReason) => {
		return request({
			url: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/BackBusiness?id=${id}&actionId=${actionId}&backReason=${backReason}`,
			method: 'post'
		})
	},
}