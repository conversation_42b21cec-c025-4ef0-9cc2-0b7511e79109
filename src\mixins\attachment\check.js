/*
 * 模块 : 检查业务附件格式相关配置
 * 作者 : 罗永梅（<EMAIL>）
 * 日期 : 2020-07-20
 * 版本 : version 1.0
 */
/* eslint-disable */

export default {
    data() {
        return {
            imgReg: /\.(bmp|jpg|jpeg|png|gif|webp|JPG|PNG|GIF)$/,
        };
    },
    methods: {
        getSuffix(file) {
            const fileName = file.name;
            const suffix = fileName.substring(fileName.length - 5);
            return suffix;
        },
        // 检查jpg格式
        checkImg(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (this.imgReg.test(suffix)) return true;

            this.$message.warning("文件只能是 png / jpg / gif / bmp 格式");
            return false;
        },
        // 检查pdf格式
        checkPDF(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (file.type === "application/pdf") return true;
            if (suffix.indexOf(".pdf") >= 0) return true;

            this.$message.warning("文件只能是 pdf 格式");
            return false;
        },
        // 检查mdb格式
        checkMDB(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (suffix.indexOf(".mdb") >= 0) return true;

            this.$message.warning("文件只能是 mdb 格式");
            return false;
        },
        // 检查dwg格式
        checkDWG(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (suffix.indexOf(".dwg") >= 0) return true;

            this.$message.warning("文件只能是 dwg 格式");
            return false;
        },
        // 检查png / jpg / gif / bmp / pdf 格式
        checkImgAndPDF(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (file.type === "application/pdf") return true;
            if (suffix.indexOf(".pdf") >= 0) return true;
            if (this.imgReg.test(suffix)) return true;

            this.$message.warning("文件只能是 png / jpg / gif / bmp / pdf 格式");
            return false;
        },
        // 检查png / jpg / gif / bmp / dxf格式
        checkImgAndDXF(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (suffix.indexOf(".dxf") >= 0) return true;
            if (this.imgReg.test(suffix)) return true;

            this.$message.warning("文件只能是 png / jpg / gif / bmp / dxf 格式");
            return false;
        },
        // 检查png / jpg / gif / bmp / pdf / dwg格式
        checkDWGAndImgAndPDF(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (file.type === "application/pdf") return true;
            if (suffix.indexOf(".pdf") >= 0) return true;
            if (this.imgReg.test(suffix)) return true;
            if (suffix.indexOf(".dwg") >= 0) return true;

            this.$message.warning(
                "文件只能是 png / jpg / gif / bmp / pdf / dwg 格式"
            );
            return false;
        },
        // 检查xls格式
        checkXls(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (suffix.indexOf(".xls") >= 0) return true;
            if (suffix.indexOf(".xlsx") >= 0) return true;
            this.$message.warning(
                "文件只能是 xls / xlsx格式"
            );
            return false;
        },
        //检查rar / zip格式
        checkRarAndZip(file){
            const suffix=this.getSuffix(file);

            //文件格式
            if(suffix.indexOf(".rar")>=0) return true;
            if(suffix.indexOf(".zip")>=0) return true;
            this.$message.warning("文件只能是 rar / zip格式");
            return false;
        },
        // 检查其他附件
        checkOthers(file) {
            const suffix = this.getSuffix(file);

            // 文件格式
            if (file.type === "application/pdf") return true;
            if (suffix.indexOf(".pdf") >= 0) return true;
            if (this.imgReg.test(suffix)) return true;
            if (suffix.indexOf(".zip") >= 0) return true;
            if (suffix.indexOf(".rar") >= 0) return true;
            if (suffix.indexOf(".dwg") >= 0) return true;

            this.$message.warning(
                "文件只能是 png / jpg / gif / bmp / pdf / zip / rar / dwg 格式"
            );
            return false;
        },
    },
};