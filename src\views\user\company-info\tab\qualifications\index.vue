<template>
  <!-- eslint-disable -->
  <div class="qualifications-container">
    <div v-if="!loading">
      <!-- 预览 -->
      <el-form v-if="disableEdit" :model="form" :label-width="labelWidth">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="测绘资质证书编号：">
              <div class="certificate-no flex">
                <span>{{ form.CertificateNo | isNull }}</span>
                <i
                  class="iconfont icon-question ml-10"
                  @click="certExampleDialog.visible = true"
                ></i>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="证书有效期至：">{{
              form.ValidityTime | isNull
            }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="测绘最高资质等级："
              prop="QualificationLevel"
              >{{ form.QualificationLevel | isNull }}</el-form-item
            >
          </el-col>
          <el-col :span="24">
            <el-form-item label="多测合一业务范围：">
              <template v-if="form.BusinessRange && form.BusinessRange.length">
                <el-tag
                  v-for="(tag, index) in form.BusinessRange"
                  :key="'businessRangeTag' + index"
                  type="success"
                  class="mr-10"
                >
                  <span v-for="(item, i) in tag" :key="'businessRange' + i">
                    {{ item }}
                    <i v-if="i < tag.length - 1">/</i>
                  </span>
                </el-tag>
              </template>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="资质证书（正本）：">
              <img-list
                v-if="
                  form.QualificateCertificate &&
                    form.QualificateCertificate.length
                "
                :img-list="form.QualificateCertificate"
                @preview="preview"
              />
              <span v-else>未上传文件</span>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="资质证书（副本）：">
              <img-list
                v-if="
                  form.ViceQualificateCertificate &&
                    form.ViceQualificateCertificate.length
                "
                :img-list="form.ViceQualificateCertificate"
                @preview="preview"
              />
              <span v-else>未上传文件</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 存储 -->
      <el-form
        v-else
        ref="qualificationsForm"
        :model="form"
        :rules="rules"
        :label-width="labelWidth"
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="测绘资质证书编号：" prop="CertificateNo">
              <div class="certificate-no flex">
                <el-input
                  v-model="form.CertificateNo"
                  placeholder="请输入测绘资质证书编号"
                ></el-input>
                <i
                  class="iconfont icon-question ml-10"
                  @click="certExampleDialog.visible = true"
                ></i>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="证书有效期至：" prop="ValidityTime">
              <el-date-picker
                type="date"
                placeholder="请选择证件有效期"
                v-model="form.ValidityTime"
                value-format="yyyy-MM-dd"
                class="width-100"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="测绘最高资质等级：" prop="QualificationLevel">
              <el-select
                v-model="form.QualificationLevel"
                placeholder="请选择测绘最高资质等级"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, index) in qualificationLevelOptions"
                  :key="index"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="多测合一业务范围：" prop="BusinessRange">
              <el-cascader
                v-model="form.BusinessRange"
                :options="businessOptions"
                :props="businessProps"
                :disabled="businessLoading"
                v-loading="businessLoading"
                element-loading-spinner="el-icon-loading"
                clearable
                class="width-100"
                placeholder="请选择多测合一业务范围"
                @change="change"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item
              label="资质证书（正本）："
              prop="QualificateCertificate"
            >
              <img-upload
                :img-list="form.QualificateCertificate"
                :show-size-hint="false"
                :file-size="10240"
                :limit="1"
                :data="{
                  BusinessType: 'CompanyRegisterRequest',
                  BusinessID: companyID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '资质证书正本',
                }"
                @upload-success="
                  upload($event, 'qualificationsForm', 'QualificateCertificate')
                "
                @preview="preview"
                @delete="
                  del($event, 'qualificationsForm', 'QualificateCertificate')
                "
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item
              label="资质证书（副本）："
              prop="ViceQualificateCertificate"
            >
              <img-upload
                :img-list="form.ViceQualificateCertificate"
                :show-size-hint="false"
                :file-size="10240"
                :limit="1"
                :data="{
                  BusinessType: 'CompanyRegisterRequest',
                  BusinessID: companyID,
                  AttachmentType: '申请材料附件',
                  AttachmentCategories: '资质证书副本',
                }"
                @upload-success="
                  upload(
                    $event,
                    'qualificationsForm',
                    'ViceQualificateCertificate'
                  )
                "
                @preview="preview"
                @delete="
                  del(
                    $event,
                    'qualificationsForm',
                    'ViceQualificateCertificate'
                  )
                "
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 图片预览 -->
    <img-preview
      :visible.sync="imgDialog.visible"
      :title="imgDialog.title"
      :img-url="imgDialog.imgUrl"
      @close="cancelPreview"
    />
    <el-dialog
      title="资质证书样例"
      :append-to-body="true"
      :visible.sync="certExampleDialog.visible"
      @close="certExampleDialog.visible = false"
    >
      <img style="width:100%" :src="certExampleDialog.imgUrl" />
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/company-info/index.js";
// 组件
import ImgUpload from "components/common/AttachmentUpload/ImgUpload";
// mixins
import Attachment from "mixins/company-info/attachment.js";
// vuex
import { mapGetters } from "vuex";

const urlPrefix = process.env.VUE_APP_URL_PREFIX;

export default {
  name: "CompanyQualifications",
  components: {
    ImgUpload,
  },
  mixins: [Attachment],
  computed: {
    ...mapGetters(["companyID"]),
  },
  props: {
    // 当前激活类型
    activeTabName: {
      type: String,
      default: "qualifications",
    },
    // 数据
    data: {
      type: Object,
      default: () => ({}),
    },
    // 加载
    loading: {
      type: Boolean,
      default: true,
    },
    // 禁止编辑
    disableEdit: {
      type: Boolean,
      default: false,
    },
    // 组件使用类型，register 单位注册 info 单位信息 change 单位信息变更申请
    useType: {
      type: String,
      default: "info",
    },
    // 仅单位信息变更使用，new 新单位信息 old 原单位信息
    infoType: {
      type: String,
      default: "new",
    },
  },
  data() {
    const validateQualificateCertificate = (rule, value, callback) => {
      if (
        !this.form.QualificateCertificate ||
        !this.form.QualificateCertificate.length
      ) {
        callback(new Error("请上传资质证书（正本）"));
      } else {
        callback();
      }
    };

    const validateViceQualificateCertificate = (rule, value, callback) => {
      if (
        !this.form.ViceQualificateCertificate ||
        !this.form.ViceQualificateCertificate.length
      ) {
        callback(new Error("请上传资质证书（副本）"));
      } else {
        callback();
      }
    };

    return {
      // 默认表单
      defaultForm: {
        CertificateNo: null,
        ValidityTime: null,
        QualificationLevel: null,
        BusinessRangeNames: [],
        BusinessRange: [],
      },
      form: {},
      // 附件
      attachmentInfo: {
        QualificateCertificate: [],
        ViceQualificateCertificate: [],
      },
      labelWidth: "150px",
      // 证书样例
      certExampleDialog: {
        visible: false,
        imgUrl: `${urlPrefix}/static/img/certificate-example.jpg`,
      },
      // 规则
      rules: {
        CertificateNo: [
          {
            required: true,
            message: "请输入测绘资质证书编号",
            trigger: "blur",
          },
        ],
        ValidityTime: [
          { required: true, message: "请选择证书有效期", trigger: "change" },
        ],
        QualificationLevel: [
          {
            required: true,
            message: "请选择测绘最高资质等级",
            trigger: "change",
          },
        ],
        BusinessRange: [
          {
            required: true,
            message: "请选择多测合一业务范围",
            trigger: "change",
          },
        ],
        QualificateCertificate: [
          {
            required: true,
            validator: validateQualificateCertificate,
            trigger: "change",
          },
        ],
        ViceQualificateCertificate: [
          {
            required: true,
            validator: validateViceQualificateCertificate,
            trigger: "change",
          },
        ],
      },
      // 业务树形
      businessOptions: [],
      // 业务选项属性
      businessProps: {
        multiple: true,
        label: "label",
        value: "value",
      },
      businessLoading: false,
      // 资质最高等级
      qualificationLevelOptions: ["甲", "乙", "丙", "丁"],
      // 图片预览弹窗
      imgDialog: {
        visible: false,
        title: null,
        imgUrl: null,
      },
    };
  },
  watch: {
    loading(val) {
      if (!val) {
        this.init();
      }
    },
    infoType(val) {
      // 修复因有时差导致信息未变更的BUG
      setTimeout(() => this.init(), 0);
    },
  },
  methods: {
    // 初始化
    init() {
      const { disableEdit, useType } = this;
      // 解决后端接口出错时表单校验问题
      if (this.$refs.qualificationsForm) {
        this.$refs.qualificationsForm.clearValidate();
      }

      if (!disableEdit) {
        this.$nextTick(() => {
          this.$refs.qualificationsForm.clearValidate();
        });

        this.businessLoading = true;
        // PublicApi.GetDictionaryList("多测合一业务范围", "tree")
        //   .then(res => {
        //     const { StateCode, Data, Message } = res;

        //     if (StateCode === 1) {
        //       this.businessOptions = Data;
        //     } else {
        //       console.log(res);
        //     }
        //     this.businessLoading = false;
        //   })
        //   .catch(err => {
        //     console.log(err);
        //     this.businessLoading = false;
        //   });

        /**
         * 2021-02-25 新需求修改
         * 新接口取多测合一业务范围
         */
        Api.GetSurveyFlows()
          .then((res) => {
            const { StateCode, Data } = res;

            if (StateCode === 1) {
              this.businessOptions = Data;
            } else {
              console.log(res);
            }
            this.businessLoading = false;
          })
          .catch((err) => {
            console.log(err);
            this.businessLoading = false;
          });
        /* 2021-02-25 新需求修改 */
      }

      this.setForm();

      // 处理BusinessRange和QualificationContent字段，json字符串转对象
      // if (typeof this.form.BusinessRange === "string") {
      //   this.form.BusinessRange = JSON.parse(this.form.BusinessRange);
      // }

      /**
       * 2021-02-25 新需求修改
       * 取BusinessRangeNames做多测合一业务范围显示
       */
      // 单位信息或正在注册并且禁用（即审核状态）
      if (useType === "info" || disableEdit) {
        if (typeof this.form.BusinessRangeNames === "string") {
          this.form.BusinessRange = this.form.BusinessRangeNames
            ? JSON.parse(this.form.BusinessRangeNames)
            : [];
        }
      }
      // 单位注册或者单位信息变更申请
      else {
        if (typeof this.form.BusinessRangeTree === "string") {
          console.log(this.form.BusinessRangeTree);
          this.form.BusinessRange = this.form.BusinessRangeTree
            ? JSON.parse(this.form.BusinessRangeTree)
            : [];
        }
      }

      /* 2021-02-25 新需求修改 */
    },
    change(e) {
      console.log(e);
    },
  },
};
</script>
<style lang="scss" scoped>
.certificate-no {
  align-items: center;

  .icon-question {
    cursor: pointer;
    &:hover {
      color: $color-primary;
    }
  }
}
</style>
