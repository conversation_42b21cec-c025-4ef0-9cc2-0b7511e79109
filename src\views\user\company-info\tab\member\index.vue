<template>
  <!-- eslint-disable -->
  <div class="member-container">
    <!-- 操作按钮 -->
    <div v-if="!disableEdit" class="operate-btn-container flex mb-20">
      <div>
        <el-button class="mr-10" type="primary" @click="showStore(null, -1)">添加</el-button>
        <el-popconfirm title="确认删除?" @onConfirm="del()">
          <el-button slot="reference" type="warning" :disabled="selectList.length === 0">批量删除</el-button>
        </el-popconfirm>
      </div>
      <div class="hint">
        <i class="el-icon-warning mr-5"></i>温馨提示：
        <!-- 测绘单位 -->
        <template
          v-if="companyInfoType === '测绘单位'"
        >人员信息需录入“单位管理员”和“注册测绘师”2种角色人员，且当前申请人角色需为“单位管理员”</template>
        <!-- 建设单位 -->
        <template v-else>人员信息需录入当前申请人，且角色为“单位管理员”</template>
      </div>
    </div>
    <!-- 表格 -->
    <dynamic-table
      ref="memberTable"
      class="table-container"
      :table-header="tableHeader"
      :table-data="listData"
      :default-props="tableProps"
      :showPagination="false"
      @selection-change="getSelectList"
    >
      <el-table-column prop="PersonRole" label="角色" width="120" align="center">
        <template slot-scope="{ row }">
          <el-tag v-if="row.PersonRole === '单位管理员'">{{ row.PersonRole }}</el-tag>
          <el-tag v-if="row.PersonRole === '报建员'" type="success">{{ row.PersonRole }}</el-tag>
          <el-tag v-if="row.PersonRole === '测绘人员'" type="success">{{ row.PersonRole }}</el-tag>
          <el-tag v-if="row.PersonRole === '注册测绘师'" type="warning">{{ row.PersonRole }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="!disableEdit" type="selection" width="55" fixed="left"></el-table-column>
      <el-table-column
        v-if="listData.length"
        prop="action"
        label="操作"
        width="130"
        fixed="right"
        align="center"
      >
        <template slot-scope="{ row, $index }">
          <template v-if="!disableEdit">
            <el-button type="text" icon="el-icon-edit" @click="showStore(row, $index)">编辑</el-button>
            <el-popconfirm title="确认删除?" @onConfirm="delRow(row)">
              <el-button slot="reference" type="text" icon="el-icon-delete">删除</el-button>
            </el-popconfirm>
          </template>
          <el-button v-else type="text" icon="el-icon-document" @click="showStore(row, $index)">查看详情</el-button>
        </template>
      </el-table-column>
    </dynamic-table>
    <!-- 存储弹窗 -->
    <store-dialog
      :visible.sync="storeDialog.visible"
      :row="storeDialog.row"
      :list="listData"
      :index="storeDialog.index"
      :disable-edit="disableEdit"
      :company-info-type="companyInfoType"
      :company-info-name="companyInfoName"
      @cancel="cancelStore"
      @submit="storeSuccess"
    />
  </div>
</template>

<script>
/* eslint-disable */
import DynamicTable from "components/common/Table/DynamicTable";
import StoreDialog from "./store.vue";
// mixins
import CompanyManageTable from "mixins/company-info/table.js";
// 工具
import { objOmit } from "utils";

export default {
  name: "CompanyMember",
  components: { DynamicTable, StoreDialog },
  mixins: [CompanyManageTable],
  data() {
    return {
      tableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50"
        },
        {
          title: "姓名",
          key: "PersonName",
          align: "center"
        },
        {
          title: "身份证号码",
          key: "PersonNumber",
          align: "center"
        },
        {
          title: "手机号码",
          key: "PersonPhone",
          align: "center"
        }
      ]
    };
  },
  watch: {
    activeTabName(val) {
      this.clearSelect();
      this.init();
      this.$refs.memberTable.$refs.table.clearSelection();
    }
  },
  methods: {
    // 删除
    del() {
      if (!this.selectList.length) {
        this.$message.warning("请选择要删除的选项");
        return false;
      }

      this.selectList.forEach(selection => {
        const index = this.listData.findIndex(e => e === selection);
        this.listData.splice(index, 1);
      });

      this.$emit("change", "base", this.listData, "人员信息Tab变更");

      // 删除成功要清除selectList
      this.clearSelect();
    },
    // 存储操作成功
    // type: 1 编辑 0 添加
    storeSuccess(type, params, index) {
      // 应后端接口需求特殊处理AttachmentInfo字段
      const { SurveyorCertificateImg, SurveyorCertificateChapter } = params;
      params.AttachmentInfo = JSON.stringify({
        SurveyorCertificateImg,
        SurveyorCertificateChapter
      });
      params = objOmit(params, [
        "SurveyorCertificateImg",
        "SurveyorCertificateChapter"
      ]);

      if (type === 1) {
        this.$set(this.listData, index, params);
        this.$emit("change", "member", this.listData, "人员信息Tab变更");
      } else {
        this.listData.push(params);
        this.$emit("change", "member", this.listData, "人员信息Tab变更");
      }

      this.storeDialog.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.operate-btn-container {
  justify-content: space-between;
  align-items: center;

  .hint {
    font-size: 12px;
    color: #f07057;
  }
}
</style>

