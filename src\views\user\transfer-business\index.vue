<template>
  <!-- eslint-disable -->
  <div class="transfer-business-container">
    <div class="transfer-business-content">
      <div class="classify flex">
        <div class="select flex">
          <el-select
            v-model="searchCompanyNo"
            filterable
            placeholder="请选择公司"
            class="width-100"
            @change="changeCompany()"
            style="margin-right: 20px"
          >
            <el-option
              v-for="(item, index) in selectCompanyList"
              :key="index"
              :label="item.CName"
              :value="item.CNo"
            ></el-option>
          </el-select>
          <el-select
            v-model="searchUserNo"
            filterable
            placeholder="请选择用户"
            class="width-100"
            @change="changeUser()"
          >
            <el-option
              v-for="(item, index) in list"
              :key="index"
              :label="item.PersonName"
              :value="item.PersonNumber"
            ></el-option>
          </el-select>
        </div>
        <el-button
          class="transfer-btn"
          type="primary"
          @click="transfer()"
          :disabled="selectList.length === 0"
          >批量移交</el-button
        >
      </div>
      <!-- 表格 -->
      <dynamic-table
        ref="transferTable"
        class="mt-20"
        v-loading="listLoading"
        element-loading-text="加载中，请稍等..."
        :table-header="tableHeader"
        :table-data="listData"
        :default-props="tableProps"
        :showPagination="true"
        :total="page.total"
        :page-no.sync="page.pageNo"
        :page-size.sync="page.pageSize"
        :page-sizes.sync="page.pageSizes"
        @pagination="getList"
        @selection-change="getSelectList"
      >
        <el-table-column
          type="selection"
          width="55"
          fixed="left"
        ></el-table-column>
        <!-- <el-table-column width="50" label="序号" align="center">
          <template slot-scope="{ $index }">{{
            $index + 1 + page.pageSize * (page.pageNo - 1)
          }}</template>
        </el-table-column> -->
        <el-table-column
          prop="BusinessNumber"
          label="业务编号"
          width="150"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="BusinessName"
          label="业务名称"
          width="200"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="BusinessType"
          label="业务类型"
          width="200"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="CreatePersonName"
          label="创建人"
          width="170"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="DeveloperName"
          label="业主单位"
          width="250"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="SurveyCompanyName"
          label="测绘单位"
          width="250"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="CreateTime"
          label="创建时间"
          width="170"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="FinishTime"
          label="完成时间"
          width="170"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="StateCode"
          width="100"
          label="状态"
          fixed="right"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-tag v-if="row.StateCode === 0" class="tag-yellow" effect="dark"
              >待签收</el-tag
            >
            <el-tag v-if="row.StateCode === 1" class="tag-blue" effect="dark"
              >办理中</el-tag
            >
            <el-tag v-if="row.StateCode === 2" type="success" effect="dark"
              >已完成</el-tag
            >
            <el-tag v-if="row.StateCode === 3" type="error" effect="dark"
              >已退回</el-tag
            >
            <el-tag v-if="row.StateCode === 4" type="info" effect="dark"
              >已关闭</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          v-if="listData.length"
          prop="action"
          label="操作"
          width="165"
          fixed="right"
          align="center"
        >
          <template slot-scope="{ row, $index }">
            <el-button
              type="text"
              icon="el-icon-document"
              @click="transfer(row)"
              >移交</el-button
            >
          </template>
        </el-table-column>
      </dynamic-table>
    </div>
    <template>
      <el-dialog
        title="选择用户"
        :visible="selectUserVisible"
        :close-on-click-modal="false"
        :append-to-body="true"
        class="switch-company-container"
        width="550px"
        @close="close"
      >
        <div>
          <tip type="default"> 请选择要移交的用户 </tip>
          <el-form class="mt-20" @submit.native.prevent>
            <el-form-item>
              <el-select
                v-model="selectUserNo"
                filterable
                placeholder="请选择用户"
                class="width-100"
                @change="changeTransferUser"
              >
                <el-option
                  v-for="(item, index) in transferUserList"
                  :key="index"
                  :label="item.PersonName"
                  :value="item.PersonNumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer">
          <el-button @click="close">取消</el-button>
          <el-button
            type="primary"
            @click="confirmTransfer()"
            :loading="transferLoading"
            >确认移交</el-button
          >
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
/* eslint-disable */
// Api
import Api from "api/transfer-business/index.js";
import companyApi from "api/company-info/index.js";
import DynamicTable from "components/common/Table/DynamicTable";
import Page from "mixins/page.js";
import Table from "mixins/table.js";
import MyBusiness from "mixins/my-business/index.js";
import { mapGetters } from "vuex";

export default {
  name: "TransferBusiness",
  components: { DynamicTable },
  mixins: [Page, Table, MyBusiness],
  computed: {
    ...mapGetters(["companyNo", "personNo", "userName", "companyList"]),
    // 获取列表接口
    apiGetList(PageIndex, PageSize) {
      return (PageIndex, PageSize) =>
        Api.GetUserBusinessList({
          companyNo: this.searchCompanyNo, //企业证件号
          phone: this.searchUserPhone, //查询的用户的手机号
          userType: this.searchUserType,
          pageIndex: PageIndex, //当前页码，从1开始
          pageSize: PageSize, //每页数量，默认20
        });
    },
  },
  data() {
    return {
      // activeName: "BusinessInProgress",
      listLoading: false,
      tableHeader: [],
      listData: [],
      businessList: [],
      selectUserVisible: false,
      transferLoading: false,
      selectUserPhone: null,
      selectUserNo: null,
      list: [],
      searchUserPhone: null,
      searchUserNo: null,
      transferUserList: [],
      searchCompanyNo: null,
      searchUserList: [],
      selectCompanyList: [],
      companyID: null,
      searchUserType: null,
      selectTransferUser: [],
    };
  },
  watch: {
    $route(val) {
      this.init();
    },
    companyNo(val) {
      if (val) {
        this.init();
      }
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.searchUserNo = this.personNo;
      this.searchCompanyNo = this.companyNo;
      this.companyID = this.companyList.filter(
        (item) => item.CNo === this.searchCompanyNo
      )[0].CID;
      this.selectCompanyList = this.companyList.filter(
        (item) => item.CRole === "单位管理员"
      );
      this.getUserList();
    },
    // 获取选项id
    getSelectList(selection) {
      // 清空选项列表
      this.clearSelect();
      this.selectList = selection;
    },
    // 关闭弹窗
    close() {
      this.selectUserVisible = false;
      this.transferLoading = false;
      this.clearSelect();
      this.$refs.transferTable.$refs.table.clearSelection();
      this.selectUserPhone = null;
      this.selectUserNo = null;
    },
    // 转移业务
    transfer(item) {
      const { selectList, $message } = this;
      if (!item) {
        if (!selectList.length) {
          $message.warning("请选择要移交的选项");
          return false;
        } else {
          this.selectUserVisible = true;
        }
      } else {
        this.selectList = [];
        this.selectList.push(item);
        this.selectUserVisible = true;
      }
    },
    // 确认转移
    confirmTransfer() {
      if (this.selectUserNo === null) {
        this.$message.warning("请选择要移交的用户");
      } else {
        this.transferLoading = true;
        let ids = this.selectList.map((item) => item.ID);
        let transferData = {
          ids: ids,
          fromPhone: this.searchUserPhone,
          toPhone: this.selectTransferUser[0].PersonPhone,
          companyNo: this.searchCompanyNo,
          toUserType: this.selectTransferUser[0].UserType,
        };
        Api.TransferBusiness(transferData)
          .then(async (res) => {
            if (res.StateCode === 1) {
              this.getList(1, this.page.pageSize);
              this.$message.success(res.Message);
              this.transferLoading = false;
              this.close();
            } else {
              this.$message.error(res.Message);
              this.transferLoading = false;
            }
          })
          .catch((err) => {
            console.log(err);
            this.transferLoading = false;
            this.$message.warning("服务器繁忙，请稍后重试");
          });
      }
    },
    //获取用户列表
    getUserList() {
      companyApi
        .GetMemberList(this.companyID, 1, 99999)
        .then(async (res) => {
          if (res.StateCode === 1) {
            this.list = res.Data.DataTable;
            this.transferUserList = this.list.filter(
              (item) => item.PersonNumber !== this.personNo
            );
            this.searchUserList = this.list.filter(
              (item) => item.PersonNumber === this.personNo
            );
            this.searchUserPhone = this.searchUserList[0].PersonPhone;
            this.searchUserType = this.searchUserList[0].UserType;
            this.getList(1, this.page.pageSize);
            // console.log(this.searchUserList)
          } else this.$message.error(res.Message);
          // 删除成功要清除selectList
        })
        .catch((err) => {
          console.log(err);
          this.$message.warning("服务器繁忙，请稍后重试");
        });
    },
    // 切换公司
    changeCompany() {
      this.companyID = this.companyList.filter(
        (item) => item.CNo === this.searchCompanyNo
      )[0].CID;
      this.searchUserNo = this.personNo;
      this.getUserList();
      this.search();
    },
    // 切换用户
    changeUser() {
      this.searchUserPhone = this.list.filter(
        (item) => item.PersonNumber === this.searchUserNo
      )[0].PersonPhone;
      this.searchUserType = this.list.filter(
        (item) => item.PersonNumber === this.searchUserNo
      )[0].UserType;
      this.transferUserList = this.list.filter(
        (item) => item.PersonNumber !== this.searchUserNo
      );
      this.search();
    },
    //切换转移用户
    changeTransferUser(){
      this.selectTransferUser = this.transferUserList.filter(item=>item.PersonNumber === this.selectUserNo);
    }
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/compnay-info-tabs.scss";
.transfer-business-container {
  padding: 20px 0;
}
.transfer-business-content {
  padding: 20px;
  min-height: calc(100vh - 456px);
  background: #ffffff;
  border: 1px solid #dfe4ed;
  border-radius: 4px;
}
.classify {
  justify-content: space-between;
}
</style>