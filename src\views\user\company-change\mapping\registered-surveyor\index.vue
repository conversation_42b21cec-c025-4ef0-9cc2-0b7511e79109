<template>
  <!-- eslint-disable -->
  <div class="surveyor-container">
    <!-- 操作按钮 -->
    <div v-if="!disableEdit" class="operate-btn-container flex mb-20">
      <div>
        <el-button class="mr-10" type="primary" @click="showStore(null, -1)">添加</el-button>
      </div>
      <div class="hint">
        <i class="el-icon-warning mr-5"></i>温馨提示：以下列出的是您单位原来的注册测绘师人员，可直接编辑或添加新的注册测绘师。
      </div>
    </div>
    <!-- 表格 -->
    <dynamic-table
      ref="surveyorTable"
      class="table-container"
      :table-header="tableHeader"
      :table-data="listData"
      :default-props="tableProps"
      :showPagination="false"
      @selection-change="getSelectList"
    >
      <el-table-column prop="PersonRole" label="角色" width="120" align="center">
        <template slot-scope="{ row }">
          <el-tag v-if="row.PersonRole === '单位管理员'">{{ row.PersonRole }}</el-tag>
          <el-tag v-if="row.PersonRole === '报建员'" type="success">{{ row.PersonRole }}</el-tag>
          <el-tag v-if="row.PersonRole === '测绘人员'" type="success">{{ row.PersonRole }}</el-tag>
          <el-tag v-if="row.PersonRole === '注册测绘师'" type="warning">{{ row.PersonRole }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="listData.length"
        prop="action"
        label="操作"
        width="130"
        fixed="right"
        align="center"
      >
        <template slot-scope="{ row, $index }">
          <template v-if="!disableEdit">
            <el-button type="text" icon="el-icon-edit" @click="showStore(row, $index)">编辑</el-button>
            <el-popconfirm v-if="showDelBtn(row)" title="确认删除?" class="ml-5" @onConfirm="delRow(row)">
              <el-button slot="reference" type="text" icon="el-icon-delete">删除</el-button>
            </el-popconfirm>
          </template>
          <el-button v-else type="text" icon="el-icon-document" @click="showStore(row, $index)">查看详情</el-button>
        </template>
      </el-table-column>
    </dynamic-table>
    <!-- 存储弹窗 -->
    <store-dialog
      :visible.sync="storeDialog.visible"
      :row="storeDialog.row"
      :list="listData"
      :index="storeDialog.index"
      :disable-edit="disableEdit"
      :company-info-type="companyInfoType"
      :company-info-name="companyInfoName"
      @cancel="cancelStore"
      @submit="storeSuccess"
    />
  </div>
</template>

<script>
/* eslint-disable */
import DynamicTable from "components/common/Table/DynamicTable";
import StoreDialog from "./store.vue";
// mixins
import CompanyManageTable from "mixins/company-info/table.js";
// 工具
import { objOmit } from "utils";

export default {
  name: "CompanyRegisteredSurveyor",
  components: { DynamicTable, StoreDialog },
  mixins: [CompanyManageTable],
  data() {
    return {
      tableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50"
        },
        {
          title: "姓名",
          key: "PersonName",
          align: "center"
        },
        {
          title: "身份证号码",
          key: "PersonNumber",
          align: "center"
        },
        {
          title: "手机号码",
          key: "PersonPhone",
          align: "center"
        }
      ]
    };
  },
  methods: {
    // 初始化
    init() {
      this.listData = this.data && this.data.length ? [...this.data] : [];
      this.defaultListData = [...this.listData];
    },
    // 是否显示删除按钮
    showDelBtn(row){
      const hasRow = this.defaultListData.find(e => e.PersonNumber === row.PersonNumber);
      return !hasRow;
    },
    // 存储操作成功
    // type: 1 编辑 0 添加
    storeSuccess(type, params, index) {
      // 应后端接口需求特殊处理AttachmentInfo字段
      const {
        SurveyorCertificateImg,
        SurveyorCertificateChapter
      } = params;
      params.AttachmentInfo = JSON.stringify({
        SurveyorCertificateImg,
        SurveyorCertificateChapter
      });
      params = objOmit(params, [
        "SurveyorCertificateImg",
        "SurveyorCertificateChapter"
      ]);

      if (type === 1) {
        this.$set(this.listData, index, params);
        this.$emit("change", "surveyor", this.listData, "注册测绘师信息Tab变更");
      } else {
        this.listData.push(params);
        this.$emit("change", "surveyor", this.listData, "注册测绘师信息Tab变更");
      }

      this.storeDialog.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.operate-btn-container {
  justify-content: space-between;
  align-items: center;

  .hint {
    font-size: 12px;
    color: #f07057;
  }
}
</style>

