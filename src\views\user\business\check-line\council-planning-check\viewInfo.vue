<template>
  <!-- eslint-disable -->
  <div class="view-info-container" v-loading="downLoading" element-loading-text="文件下载中，请稍等...">
    <div class="business-info-title">
      <span>申请信息</span>
    </div>
    <el-card shadow="never">
      <el-form ref="form" :model="form" label-width="180px">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基本信息" name="1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务编号：">{{ baseInfo.BusinessNumber | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务类型：">{{ baseInfo.BusinessType | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务名称：">{{ baseInfo.BusinessName | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人：">{{ baseInfo.CreatePersonName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人手机号：">{{ baseInfo.CreatePersonPhone | isNull }}</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位：">{{ baseInfo.DeveloperName | isNull }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测绘单位：">{{ baseInfo.SurveyCompanyName | isNull }}</el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="业务信息" name="2">
            <el-form-item label="工程规划许可证：">
              <dynamic-table
                ref="licenceTable"
                class="table-container"
                :table-header="licenceTableHeader"
                :table-data="contentInfo.ProjectPlanPermission"
                :default-props="tableProps"
                :showPagination="false"
              >
                <el-table-column prop="action" label="操作" width="170" fixed="right" align="center">
                  <template slot-scope="{ row, $index }">
                    <el-button
                      type="text"
                      icon="el-icon-document"
                      @click="viewLicence(row, $index)"
                    >查看详情</el-button>
                  </template>
                </el-table-column>
              </dynamic-table>
            </el-form-item>
            <!-- <el-form-item label="不动产单元号（宗地号）：">{{ contentInfo.GroundCode | isNull }}</el-form-item> -->
          </el-collapse-item>
          <el-collapse-item title="附件信息" name="3">
            <el-form-item label="市政工程规划许可证件：">
              <file-list
                :file-list="attachmentData.CouncilLicenceImg"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label="施工红线图：">
              <file-list
                :file-list="attachmentData.RedLineDrawingImg"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
            <el-form-item label="其他：">
              <list-upload
                v-if="isLastStep && isCurrentApplicant"
                file-format="png / jpg / gif / bmp / pdf / zip / rar"
                :file-list="form.Others"
                :file-size="102400"
                :data="{ BusinessType: baseInfo.BusinessType, BusinessID: baseInfo.ID, AttachmentType: '申请材料附件', AttachmentCategories: '其他附件' }"
                :on-check-format="checkOthers"
                :disabled="baseInfo.StateCode === 4"
                @upload-success="upload($event, 'form', 'Others')"
                @delete="del($event, 'form', 'Others')"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
              <file-list
                v-else
                :file-list="attachmentData.Others"
                @preview="preview"
                @download-start="downloadStart"
                @download-end="downloadEnd"
                @download-fail="downloadFail"
              />
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-card>
  </div>
</template>

<script>
/* eslint-disable */
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "SetoutSurveyViewInfo",
  mixins: [ViewInfo],
  data() {
    return {
      licenceTableHeader: [
        {
          title: "序号",
          type: "index",
          align: "center",
          width: "50"
        },
        {
          title: "工程规划许可证号",
          key: "Code",
          align: "center"
        },
        {
          title: "建设单位",
          key: "ConstructCompany",
          align: "center"
        },
        {
          title: "项目名称",
          key: "ProjectName",
          align: "center"
        }
      ],
      // 表格配置属性
      tableProps: {
        prop: "key",
        label: "title"
      },
      tipType: "default"
    };
  },
  methods: {
    // 查看工程规划许可证
    viewLicence(row, index) {
      this.$emit("view-licence", row, index);
    }
  }
};
</script>
<style lang="scss" scoped>
@import "~@/styles/business.scss";
/deep/.el-collapse-item__header.is-active {
  color: $color-primary;
  font-weight: bold;
}

/deep/ .el-collapse-item__content{
  padding-bottom: 0;
}
</style>
