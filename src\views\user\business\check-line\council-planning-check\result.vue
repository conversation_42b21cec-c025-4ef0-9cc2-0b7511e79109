<template>
  <!-- eslint-disable -->
  <div>
    <template v-if="report.length && showReport">
      <div class="result-title mt-0">
        <span>【{{ name }}】放线报告</span>
      </div>
      <file-list
        :file-list="report"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </template>
    <template v-if="auditFeedback && auditFeedback.length">
      <div class="result-title mt-10">
        <span>【{{ name }}】成果备案反馈</span>
      </div>
      <div
        v-for="(item, index) in auditFeedback"
        :key="'auditFeedback' + index"
        class="result-file flex mb-15"
      >
        <div>{{ item.AttachmentName + item.AttachmentExt }}</div>
        <el-button
          type="primary"
          icon="el-icon-download"
          class="ml-10"
          @click="download(item)"
          >下载</el-button
        >
      </div>
    </template>
  </div>
</template>

<script>
/* eslint-disable */
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";
import BusinessResult from "mixins/business/result.js";

export default {
  name: "SetoutSurveyResult",
  mixins: [AttachmentDownload, BusinessResult],
  props: {
    report: {
      type: Array,
      default: () => [],
    },
    showReport: {
      type: Boolean,
      default: false,
    },
    // 成果反馈附件
    auditFeedback: {
      type: Array,
      default: null,
    },
  },
};
</script>
<style lang="scss" scoped>
@import "~@/styles/result.scss";
</style>

