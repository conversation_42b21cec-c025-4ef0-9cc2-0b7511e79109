<template>
  <section id="appMain" class="app-main">
    <div class="wrapper-container">
      <transition name="fade-transform" mode="out-in">
        <router-view />
      </transition>
    </div>
  </section>
</template>

<script>
/* eslint-disable */
export default {
  name: "AppMain",
  computed: {
    key() {
      return this.$route.path;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-main {
  // margin-top: 133px;
  // min-height: calc(100vh - 80px);
  position: relative;
  overflow: hidden;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
