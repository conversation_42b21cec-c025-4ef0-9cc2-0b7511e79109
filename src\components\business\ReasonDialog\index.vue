<template>
  <!-- eslint-disable -->
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="500px"
    @close="close"
    class="reason-dialog-container"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="0">
      <el-form-item label prop="reason">
        <!-- 修改至2021-03-16，添加字数限制 -->
        <el-input
          type="textarea"
          :autosize="{ minRows: 4 }"
          v-model.trim="form.reason"
          placeholder="请输入原因，500字以内"
          maxlength="500"
          show-word-limit
          @keyup.enter.native="submitForm('form')"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="submitForm('form')"
        :loading="loading"
      >{{ title === "验收不通过" ? "提交" : title }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
/* eslint-disable */

export default {
  name: "ReasonDialog",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false
    },
    // 名称
    title: {
      type: String,
      default: ""
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 表单
      form: {
        reason: null
      },
      // 规则
      rules: {
        reason: [{ required: true, message: "请输入原因", trigger: "blur" }]
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.init();
      }
    }
  },
  methods: {
    // 初始化
    init() {
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      });
    },
    // 关闭存储弹窗
    close() {
      this.$emit("cancel", false);
      this.$emit("update:visible", false);
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$emit("submit", this.form.reason, this.title);
        } else {
          this.$message.error("信息填写有误，请检查");
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.reason-dialog-container {
  /deep/ .el-dialog__body {
    padding: 10px 20px 0px 20px;
  }
}
</style>