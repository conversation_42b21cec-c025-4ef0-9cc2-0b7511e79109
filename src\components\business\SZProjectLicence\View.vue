<template>
  <!-- eslint-disable -->
  <div>
    <div class="licence-title">【{{ data.Code }}】</div>
    <table class="table" cellpadding="0" cellspacing="0">
      <tr>
        <th colspan="2">建设单位</th>
        <td colspan="7">{{ data.ConstructCompany }}</td>
        <th colspan="2">申请号</th>
        <td colspan="4">{{ data.AppendixImgNumber }}</td>
      </tr>
      <tr>
        <th colspan="2">建设地址</th>
        <td colspan="7">{{ data.Address }}</td>
        <th colspan="2">项目号</th>
        <td colspan="4">{{ data.CaseCode }}</td>
      </tr>
      <tr>
        <th colspan="2">审批内容</th>
        <td colspan="13" style="line-height: 22px;width: 90%;text-align:left;">{{ data.SPNR }}</td>
      </tr>
      <tr>
        <th colspan="2">项目名称</th>
        <td colspan="13" style="text-align: left;">{{ data.ProjectName }}</td>
      </tr>
    </table>
  </div>
</template>

<script>
/* eslint-disable */

export default {
  name: "ProjectLicenceView",
  props: {
    // 弹窗可见性
    visible: {
      type: Boolean,
      default: false
    },
    // 记录数据
    row: {
      type: Object,
      default: () => ({})
    },
    // 当前记录索引
    index: {
      type: Number,
      default: -1
    },
    // 工规证类型
    licenceType: {
      type: String,
      default: "放线",
    },
  },
  watch: {
    row(val) {
      this.init();
    }
  },
  data() {
    return {
      data: {
        // 建设单位
        ConstructCompany: null,
        // 建设地址
        Address: null,
        // 项目名称
        ProjectName: null,
        // 附图编号
        AppendixImgNumber: null,
        //报建编号
        CaseCode: null,
        //审批内容
        SPNR: null
      }
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      // console.log(this.row);
      this.data = this.row;
    },
    // 关闭弹窗
    close() {
      this.$emit("close");
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  th {
    background: #f8f8f8;
    color: #909399;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    word-break: break-all;
  }

  td {
    .table-div:last-child {
      div {
        border-bottom: none;
      }
    }
  }

  .th-fixed-width {
    min-width: 78px;
    width: 78px;
    max-width: 78px;
  }
}

.table-div {
  display: table;
  line-height: 1;
  min-height: 35px;

  div {
    display: table-cell;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    padding: 10px 3px;

    &:last-child {
      border-right: none;
    }
  }
}

.licence-title {
  text-align: center;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
}
</style>