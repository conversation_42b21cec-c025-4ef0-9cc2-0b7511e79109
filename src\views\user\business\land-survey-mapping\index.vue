<template>
  <business-layout
    class="land-survey-mapping-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :down-loading="downLoading"
    :prev-btn="{ visible: false, loading: false }"
    :next-btn="nextBtn"
    :save-btn="saveBtn"
    :close-btn="closeBtn"
    :img-dialog="imgDialog"
    :pdf-dialog="pdfDialog"
    :reason-dialog="reasonDialog"
    @back-to-list="backToList"
    @reload="reload"
    @next="next"
    @save="save"
    @cancel-preview="cancelPreview"
    @submit-reason="handleReasonDialogSubmit"
    @close-business="close('申请人自行关闭')"
  >
    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="BaseInfo.StateCode === 4 ? {} : rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="申请单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <el-form-item label="项目入库MDB文件：" label-width="150px">
              <mdb-upload
                :id="BaseInfo.ID"
                :file-list="attachmentForm.SurveyResultMaterials"
                :file-size="102400"
                :upload-url="resultUploadUrl"
                @upload-success="resultUpload"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import BusinessLayout from 'components/business/BusinessLayout/index.vue'
import MdbUpload from 'components/business/MdbUpload/index.vue'
import Description from 'components/common/Description/index.vue'
// mixins
import BusinessMixin from 'mixins/business/index.js'
import AttachmentMixin from 'mixins/attachment/index.js'
// API
// import Api from 'api/business/index.js'

export default {
  name: 'LandSurveyMapping',
  components: {
    BusinessLayout,
    MdbUpload,
    Description
  },
  mixins: [BusinessMixin, AttachmentMixin],
  data() {
    return {
      // 步骤1表单
      form1: {
        SurveyResultMaterials: [] // 测绘成果资料
      },
      // 步骤1验证规则
      rules1: {
        // 可以在这里添加验证规则
      },
      // 附件表单（保持兼容性）
      attachmentForm: {
        SurveyResultMaterials: [] // 测绘成果资料
      },
      // 征拆测绘数据上传地址
      resultUploadUrl: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostSurveyProjectResult`,
    }
  },

  methods: {
    /**
     * 处理API返回的数据
     * @param {Object} Data 接口返回的数据
     */
    handleApiData(Data) {
      // 处理其他业务数据
      this.ContentInfo = Data.ContentInfo || {}

      return true
    },    
    /**
     * 处理要提交的业务数据
     */
    handleBusinessData() {
      const { businessID, form1 } = this

      const data = {
        BaseInfo: this.BaseInfo,
        ContentInfo: {
          ID: businessID
        },
        AttachmentInfo: JSON.stringify({
          SurveyResultMaterials: form1.SurveyResultMaterials
        })
      }

      return data
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/business.scss";
</style>