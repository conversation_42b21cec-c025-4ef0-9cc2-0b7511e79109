<template>
  <business-layout
    class="land-survey-mapping-container"
    :fixed-step-list="fixedStepList"
    :current-step="currentStep"
    :step-list="stepList"
    :base-info="BaseInfo"
    :extend-info="ExtendInfo"
    :current-action="CurrentAction"
    :prev-btn="{ visible: false, loading: false }"
    :next-btn="{ visible: false, loading: false }"
    :save-btn="{ visible: false, loading: false }"
    :close-btn="{ visible: false, loading: false }"
    @back-to-list="backToList"
    @reload="reload"
    @next="next"
    @save="save"
  >
    <template slot="operateBtnAfter">
      <!-- 只有在业务办理中时才显示提交按钮 -->
      <template v-if="BaseInfo.StateCode !== 2">
        <el-button
          type="success"
          :loading="submitLoading"
          :disabled="!form1.Data || form1.Data.length === 0"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </template>
    </template>

    <!-- 步骤内容 -->
    <template slot="stepCotent">
      <div v-if="currentStep === 1">
        <!-- 申请信息 -->
        <el-form
          ref="form1"
          :model="form1"
          :rules="rules1"
          label-width="125px"
        >
          <description title="基本信息" style="margin-top: -20px">
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务编号：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessNumber"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务类型：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessType"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="业务名称：">
                  <el-input
                    v-model.trim="BaseInfo.BusinessName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="联系人手机号：">
                  <el-input
                    v-model.trim="BaseInfo.CreatePersonPhone"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                <el-form-item label="测绘单位：">
                  <el-input
                    v-model.trim="BaseInfo.SurveyCompanyName"
                    placeholder="-"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </description>
          <description title="附件信息" style="margin-top: -10px">
            <!-- 提示语 -->
            <tip
              v-if="!ContentInfo.DataCheckID"
              class="mb-20"
            >请上传测绘成果</tip>
            <template v-else>
              <div v-if="ContentInfo.DataCheckState === 0" class="mb-20">
                <tip type="default" class="mb-20">
                  测绘成果已上传完成，
                  <i
                    class="el-icon-loading mr-5"
                  />系统正在对成果进行检查，检查大概需要10分钟，请耐心等待结果
                </tip>
                <timing-progress-bar :is-finished="surveyResultCheckFinished" />
              </div>
              <tip
                v-if="ContentInfo.DataCheckState === 1"
                type="success"
                class="mb-20"
              >
                您上传的测绘成果符合南宁市不动产测绘成果格式要求，可<template v-if="surveyBusinessManager || surveyAdmin">联系本单位注册测绘师</template>登录邕e登App进入"我的授权"模块刷脸确认测绘成果。若想修改，请重新上传测绘成果
              </tip>
              <tip
                v-if="ContentInfo.DataCheckState === 2"
                type="error"
                class="mb-20"
              >
                您上传的测绘成果未能通过检查，请
                <span
                  class="link"
                  @click="downloadSurveyResultErrorReport('征拆测绘')"
                >点击此处</span>下载成果检查报告，待整改后重新上传
              </tip>
            </template>
            <!-- 上传按钮 -->
            <el-form-item
              v-if="
                (!ContentInfo.DataCheckID ||
                (ContentInfo.DataCheckID && ContentInfo.DataCheckState > 0)) &&
                BaseInfo.StateCode !== 2
              "
              label-width="100px"
              label="测绘成果："
              prop="Data"
            >
              <mdb-upload
                :id="businessID"
                :file-list="form1.Data"
                :file-size="102400"
                :upload-url="resultUploadUrl"
                @upload-success="resultUpload"
              />
            </el-form-item>
          </description>
        </el-form>
      </div>
    </template>
  </business-layout>
</template>

<script>
/* eslint-disable */
// 组件
import TimingProgressBar from "components/business/TimingProgressBar/index.vue";
import BusinessLayout from 'components/business/BusinessLayout/index.vue'
import MdbUpload from 'components/business/MdbUpload/index.vue'
// mixins
import BusinessMixin from 'mixins/business/index.js'
import SurveyResultMixin from "mixins/business/survey-result.js";
import AttachmentMixin from 'mixins/attachment/index.js'

const VUE_APP_SERVER_API = process.env.VUE_APP_SERVER_API;

export default {
  name: 'LandSurveyMapping',
  components: {
    TimingProgressBar,
    BusinessLayout,
    MdbUpload
  },
  mixins: [BusinessMixin, SurveyResultMixin, AttachmentMixin],
  data() {
    return {
      form1: {
        Data: []
      },
      rules1: {
        Data: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.form1.Data || !this.form1.Data.length) {
                callback(new Error("请上传测绘成果"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
      ContentInfo: {
        DataCheckID: null,
        DataCheckState: null
      },
      // 征拆测绘数据上传地址
      resultUploadUrl: `${VUE_APP_SERVER_API}/sdcapi/BusinessFlow/PostSurveyProjectResult`,
      // 按钮加载状态
      submitLoading: false
    }
  },

  methods: {
    /**
     * 处理API返回的数据
     * @param {Object} Data 接口返回的数据
     */
    handleApiData(Data) {
      // 处理业务数据
      this.ContentInfo = Data.ContentInfo || {}
      return true
    },

    /**
     * 处理步骤列表 - 覆盖BusinessMixin的方法
     * @param {Number} step 当前步骤
     * @param {Array} list 流程列表
     */
    handleStepList(/* step, list */) {
      // 征拆测绘业务只有一个步骤，使用固定的步骤配置
      this.stepList = [
        {
          id: 1,
          name: '征拆测绘数据汇交',
          finished: this.BaseInfo.StateCode === 2
        }
      ]

      this.currentStep = 1
      this.GLOBAL.logInfo(`征拆测绘业务 - 当前为第1步`)

      // 调用handleStep处理步骤逻辑
      this.handleStep(1)

      // 处理按钮显示
      if (this.BaseInfo.StateCode === 4) {
        this.hideOperateBtn()
      } else {
        this.handleOperateBtn(1)
      }

      // 清除表单校验
      const form = this.$refs.form1
      if (form) {
        this.$nextTick(() => {
          form.clearValidate()
        })
      }
    },

    /**
     * 处理表单数据
     * @param {Number} currentStep 当前步骤
     */
    handleFormData(currentStep) {
      const { ContentInfo } = this;
      const { DataCheckID, DataCheckState } = ContentInfo;

      this.form1 = {
        // 判断Data是否有值，DataCheckState： 0 检查中  1 检查通过  2 检查不通过
        Data:
          DataCheckID && DataCheckState > 0
            ? [{ name: "hasResult", status: "success" }]
            : [],
      };

      // 审核中定时请求接口
      if (currentStep === 1) {
        if (DataCheckID && DataCheckState === 0) {
          this.getSurveyResultCheckState(DataCheckID);
        }
      }
    },

    /**
     * 处理要提交的业务数据
     */
    handleBusinessData() {
      const { businessID } = this

      const data = {
        BaseInfo: this.BaseInfo,
        ContentInfo: {
          ID: businessID
        }
      }

      return data
    },

    /**
     * 处理操作按钮显示
     * @param {Number} currentStep 当前步骤
     */
    handleOperateBtn() {
      // 征拆测绘只有一个步骤，隐藏所有默认按钮，使用自定义按钮
      this.saveBtn.visible = false
      this.prevBtn.visible = false
      this.nextBtn.visible = false
      this.closeBtn.visible = false
    },

    /**
     * 处理提交按钮点击
     */
    handleSubmit() {
      this.submitLoading = true
      this.next(1).finally(() => {
        this.submitLoading = false
      })
    },


  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/business.scss";
</style>