<template>
  <!-- eslint-disable -->
  <div v-if="buildingTableInfo.length">
    <div
      v-for="(item, index) in buildingTableInfo"
      :key="'buildingTableInfo' + index"
    >
      <table
        class="table"
        cellpadding="0"
        cellspacing="0"
        style="margin-top: -1px"
      >
        <tr>
          <th class="th-width">
            <div>建设单位</div>
          </th>
          <td class="td-width">{{ item.JSDW | isNull }}</td>
          <th class="th-width">
            <div>建设地址</div>
          </th>
          <td class="td-width">{{ item.JSDZ | isNull }}</td>
        </tr>
        <tr>
          <th class="th-width">
            <div>项目名称</div>
          </th>
          <td class="td-width">{{ item.XMMC | isNull }}</td>
          <th class="th-width">
            <div>项目编号</div>
          </th>
          <td class="td-width">{{ item.XMBH | isNull }}</td>
        </tr>
        <tr>
          <th class="th-width">市政工程分类</th>
          <td colspan="3" style="padding: 0">
            <table class="table inside inside1" cellpadding="0" cellspacing="0">
              <tr v-if="item.SZCZ && item.SZCZ !== null">
                <th class="th-width">市政场站类</th>
                <td class="br-none bb-none" style="padding: 0">
                  <table
                    class="table inside inside2"
                    cellpadding="0"
                    cellspacing="0"
                  >
                    <tr>
                      <th style="width: 10%">项目分类</th>
                      <th style="width: 10%">
                        净用地面积
                        <div class="mt-5">(㎡)</div>
                      </th>
                      <th style="width: 17.2%">主要建设内容</th>
                      <th style="width: 12%">容积率</th>
                      <th style="width: 8%">建筑密度</th>
                      <th style="width: 6%">绿地率</th>
                      <th style="width: 9%">与道路红线（或绿线）退距</th>
                      <th style="width: 10%">主要设施安全距离</th>
                      <th style="width: 8%">
                        总投资额
                        <div class="mt-5">（万元）</div>
                      </th>
                      <th class="br-none" style="width: 6.4%">其他</th>
                    </tr>
                    <tr>
                      <td>{{ item.SZCZ.XMFL | isNull }}</td>
                      <td>{{ item.SZCZ.JGCLYDMJ | isNull }}</td>
                      <td>{{ item.SZCZ.ZYJSNR | isNull }}</td>
                      <td>{{ item.SZCZ.RJL | isNull }}</td>
                      <td>{{ item.SZCZ.JZMD | isNull }}</td>
                      <td>{{ item.SZCZ.LDL | isNull }}</td>
                      <td>{{ item.SZCZ.TJ | isNull }}</td>
                      <td>{{ item.SZCZ.ZYSSAQJL | isNull }}</td>
                      <td>{{ item.SZCZ.Total | isNull }}</td>
                      <td class="br-none">{{ item.SZCZ.BeiZ | isNull }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr v-if="item.DLQL && item.DLQL !== null">
                <th class="th-width">道路桥梁类</th>
                <td class="br-none bb-none" style="padding: 0">
                  <table
                    class="table inside inside2"
                    cellpadding="0"
                    cellspacing="0"
                  >
                    <tr>
                      <th rowspan="2" style="width: 10%">道路等级</th>
                      <th rowspan="2" style="width: 5%">红线宽度</th>
                      <th colspan="2" style="width: 12%">工程范围</th>
                      <th rowspan="2" style="width: 10%">
                        规模总长
                        <div class="mt-5">（m）</div>
                      </th>
                      <th rowspan="2" style="width: 25.4%">标准横断面布置</th>
                      <th rowspan="2" style="width: 18.8%">
                        桥下空间使用功能构成
                      </th>
                      <th rowspan="2" style="width: 8%">
                        总投资额
                        <div class="mt-5">（万元）</div>
                      </th>
                      <th class="br-none" rowspan="2" style="width: 6.4%">
                        其他
                      </th>
                    </tr>
                    <tr>
                      <th style="width: 6%">起点</th>
                      <th style="width: 6%">终点</th>
                    </tr>
                    <tr>
                      <td>{{ item.DLQL.DLDJ | isNull }}</td>
                      <td>{{ item.DLQL.HXKD | isNull }}</td>
                      <td>{{ item.DLQL.GCFWQD | isNull }}</td>
                      <td>{{ item.DLQL.GCFWZD | isNull }}</td>
                      <td>{{ item.DLQL.ZCD | isNull }}</td>
                      <td>{{ item.DLQL.BZHDMBZ | isNull }}</td>
                      <td>{{ item.DLQL.QXKJSYGN | isNull }}</td>
                      <td>{{ item.DLQL.Total | isNull }}</td>
                      <td class="br-none">{{ item.DLQL.BeiZ | isNull }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr v-if="item.DXGX && item.DXGX !== null">
                <th class="th-width">地下（上）管线类</th>
                <td class="br-none bb-none" style="padding: 0">
                  <table
                    class="table inside inside2"
                    cellpadding="0"
                    cellspacing="0"
                  >
                    <tr>
                      <th rowspan="2" style="width: 4.8%">名称</th>
                      <th rowspan="2" style="width: 4.8%">总长</th>
                      <th rowspan="2" style="width: 4.8%">管材</th>
                      <th rowspan="2" style="width: 5.6%">管径</th>
                      <th colspan="2" style="width: 12%">埋深</th>
                      <th rowspan="2" style="width: 30%">总体敷设位置</th>
                      <th rowspan="2" style="width: 8.8%">
                        电压等级
                        <div class="mt-5">（KV）</div>
                      </th>
                      <th rowspan="2" style="width: 9%">
                        杆塔高度
                        <div class="mt-5">（m）</div>
                      </th>
                      <th rowspan="2" style="width: 7.6%">
                        总投资额
                        <div class="mt-5">（万元）</div>
                      </th>
                      <th class="br-none" rowspan="2" style="width: 4.8%">
                        其他
                      </th>
                    </tr>
                    <tr>
                      <th style="width: 5.2%">起点</th>
                      <th style="width: 5.2%">终点</th>
                    </tr>
                    <tr>
                      <td>{{ item.DXGX.MC | isNull }}</td>
                      <td>{{ item.DXGX.ZCD | isNull }}</td>
                      <td>{{ item.DXGX.GC | isNull }}</td>
                      <td>{{ item.DXGX.GJ | isNull }}</td>
                      <td>{{ item.DXGX.GTBG | isNull }}</td>
                      <td>{{ item.DXGX.GDBG | isNull }}</td>
                      <td>{{ item.DXGX.ZTFSWZ | isNull }}</td>
                      <td>{{ item.DXGX.DYDJ | isNull }}</td>
                      <td>{{ item.DXGX.GTGD | isNull }}</td>
                      <td>{{ item.DXGX.Total | isNull }}</td>
                      <td class="br-none">{{ item.DXGX.BeiZ | isNull }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr v-if="item.SXGC && item.SXGC !== null">
                <th class="th-width">水系工程类</th>
                <td style="padding: 0" class="br-none bb-none">
                  <table
                    class="table inside inside2"
                    cellpadding="0"
                    cellspacing="0"
                  >
                    <tr>
                      <th style="width: 34%">名称</th>
                      <th style="width: 33%">规划审批水系面积</th>
                      <th class="br-none">竣工测量水系面积</th>
                    </tr>
                    <tr>
                      <td>{{ item.SXGC.MC | isNull }}</td>
                      <td>{{ item.SXGC.GHSPSXMJ | isNull }}</td>
                      <td class="br-none">{{ item.SXGC.JGCLSXMJ | isNull }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <!-- <tr>
          <th class="th-width">备注</th>
          <td colspan="3">-</td>
        </tr> -->
      </table>
      <div class="operate-btn flex">
        <el-button
          v-if="conditionVerificateInfo"
          type="primary"
          icon="el-icon-download"
          @click="download(conditionVerificateInfo)"
          >下载竣工规划条件核实及土地核验信息表</el-button
        >
      </div>
    </div>
    <!-- <property-info-table-dialog
      :visible.sync="propertyDialog.visible"
      :info="propertyDialog.info"
      :file="propertyInfo"
    /> -->
  </div>
</template>

<script>
/* eslint-disable */
// 组件
import PropertyInfoTableDialog from "components/business/PropertyInfoTableDialog/index.vue";
// Api
import Api from "api/public/index.js";
// mixins
import AttachmentDownload from "mixins/attachment/download.js";

export default {
  name: "CouncilTableInfo",
  mixins: [AttachmentDownload],
  // components: { PropertyInfoTableDialog },
  props: {
    // 楼盘信息表pdf
    propertyInfo: {
      type: Object,
      default: null,
    },
    // 竣工规划条件核实信息表pdf
    conditionVerificateInfo: {
      type: Object,
      default: null,
    },
    // 楼盘信息
    buildingTableInfo: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      propertyDialog: {
        visible: false,
        info: null,
      },
    };
  },
  methods: {
    downloadStart(file) {
      this.$emit("download-start", file);
    },
    downloadEnd(file) {
      this.$emit("download-end", file);
    },
    downloadFail(file) {
      this.$emit("download-fail", file);
    },
    view(info) {
      this.propertyDialog = {
        visible: true,
        info,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  line-height: 1;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;

  .inside {
    border-top: none;
    border-left: none;
    border-right: none;

    tr:last-child {
      th,
      td {
        // border-bottom: none;
      }
    }
  }

  th {
    background: #f8f8f8;
    color: #909399;
    // min-width: 130px;
    // width: 110px;
    // max-width: 130px;
  }
  th,
  td {
    text-align: center;
    padding: 10px 3px;
    border-right: 1px solid #dfe6ec;
    border-bottom: 1px solid #dfe6ec;
    // min-width: 130px;
  }
}

.operate-btn {
  justify-content: center;
  // border-bottom: 1px solid #dfe6ec;
  padding: 20px 0;
}
.th-width {
  width: 10%;
}
.td-width {
  width: 40%;
}
.br-none {
  border-right: none !important;
}
.bb-none {
  border-bottom: none !important;
}
.table.inside1 tr:last-child .inside2 td,
.table.inside1 tr:last-child > th {
  border-bottom: none;
}
</style>