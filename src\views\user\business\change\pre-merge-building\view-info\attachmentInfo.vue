<template>
  <!-- eslint-disable -->
  <el-form ref="form" :model="form" label-width="140px">
    <!-- ChangeDescription -->
    <el-form-item label-width="160px" label="变更后的楼盘信息表：">
      <file-list
        :file-list="attachmentData.ChangeBuildingInfo"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="变更说明：">
      <file-list
        :file-list="attachmentData.ChangeDescription"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
    <el-form-item label-width="160px" label="其他：">
      <list-upload
        v-if="isLastStep && isCurrentApplicant"
        file-format="png / jpg / gif / bmp / pdf / zip / rar"
        :file-list="form.Others"
        :file-size="102400"
        :data="{
          BusinessType: baseInfo.BusinessType,
          BusinessID: baseInfo.ID,
          AttachmentType: '申请材料附件',
          AttachmentCategories: '其他附件',
        }"
        :on-check-format="checkOthers"
        :disabled="baseInfo.StateCode === 4"
        @upload-success="upload($event, 'form', 'Others')"
        @delete="del($event, 'form', 'Others')"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
      <file-list
        v-else
        :file-list="attachmentData.Others"
        @preview="preview"
        @download-start="downloadStart"
        @download-end="downloadEnd"
        @download-fail="downloadFail"
      />
    </el-form-item>
  </el-form>
</template>

<script>
/* eslint-disable */
// 组件
import ViewInfo from "mixins/business/view-info.js";

export default {
  name: "PreMappingAttachmentInfo",
  mixins: [ViewInfo],
  methods: {
    // 附件上传
    upload(e, formName, attr) {
      if (formName) {
        this.$refs[formName].model[attr] = e;
        if (e.length) {
          this.$refs[formName].clearValidate(attr);
        }
      }

      this.$emit("upload-success", e);
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
